// Node modules
import React, { ReactNode } from "react";
// import { <PERSON> } from "react-router-dom";
// helpers
import appUrls from "unmatched/utils/urls/app-urls";
// components
import { Layout, Image, Card, Div } from "unmatched/components";
import useSession from "unmatched/modules/session/hook";

interface AuthContainerProps {
  children?: ReactNode;
  className?: string;
}

const AuthContainer = (props: AuthContainerProps) => {
  const { children, className } = props;
  const { client } = useSession();
  return (
    <Layout.Row className="pb-5">
      <Layout.Col className={className}>
        <Div className="pt-4 pb-2">
          <a href={appUrls.auth.login}>
            {client.lightLogo.length > 0 ? (
              <Image
                height="90"
                width="200"
                className="img-contain"
                src={client.lightLogo}
                alt="CLIENT_LOGO"
              ></Image>
            ) : null}
          </a>
        </Div>
        <Card className="p-2">
          <Card.Body>{children}</Card.Body>
        </Card>
      </Layout.Col>
    </Layout.Row>
  );
};

export default AuthContainer;
