import React from "react";
import { Link } from "react-router-dom";
import { Text, Div, Table } from "unmatched/components";
import { useTable, useHistory } from "unmatched/hooks";
import util from "unmatched/utils";
import appUrls from "unmatched/utils/urls/app-urls";
import { Empty } from "../CreateDataLoad";
import { keys, map, get } from "lodash";

import { getAllAssociateFileFact } from "../../dataload-api";

export default function OngoingDataList(props: any) {
  const {
    data,
    isLoading,
    filters: { search },
    setAssociateData,
  } = props;

  const tableMeta = useTable({});

  React.useEffect(() => {
    tableMeta.updatePagination({ totalPages: data?.count_pages });
  }, [data]);

  const history = useHistory();

  const columns = [
    { key: 2, label: "No.", hasSort: false },
    {
      key: 3,
      label: "Survey Title",
      hasSort: true,
      sortKey: "title",
      sortValue: "asc",
    },
    {
      key: 4,
      label: "Chosen Records",
      hasSort: false,
      sortKey: "records",
      sortValue: "asc",
    },
    { key: 5, label: "Status", hasSort: false },
    {
      key: 6,
      label: "Date",
      hasSort: true,
      sortKey: "updated_at",
      sortValue: "asc",
    },
    { key: 7, label: "Action", hasSort: false },
  ];

  const [columnsData, setColumnsData] = React.useState<any>(columns);

  const getColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const renderRow = (item: any, index: number) => {
    return (
      <React.Fragment key={item.id}>
        <Table.Data width="70px">
          <Text.P1>{index + 1}</Text.P1>
        </Table.Data>
        <Table.Data>
          <Text.P1>{item.title}</Text.P1>
        </Table.Data>
        <Table.Data>
          <Text.P1>{item.pairing_file?.records}</Text.P1>
        </Table.Data>
        <Table.Data>
          <Text.P1>On going</Text.P1>
        </Table.Data>
        <Table.Data>
          <Text.P1>
            {util.date.getBrowserTime(
              item.pairing_file?.updated_at,
              "MMMM dd, yyyy, HH:mm"
            )}
          </Text.P1>
        </Table.Data>
        <Table.Data>
          <Text.P1>
            <Link
              to={appUrls.admin.dataLoad.getManageOngoingUrl(item.index_id)}
              style={{ fontSize: "0.815rem" }}
            >
              Manage
            </Link>
          </Text.P1>
        </Table.Data>
      </React.Fragment>
    );
  };

  const onPageSelect = async (page: number) => {
    try {
      const response = await getAllAssociateFileFact({
        page,
        search: "",
        page_size: 10,
      });
      setAssociateData(response.data);
      tableMeta.onPageSelect(page);
    } catch (e: any) {}
  };

  return (
    <Div className="mt-4">
      <Table
        columns={getColumns()}
        onRowClick={(item: any) => {
          history.push(
            appUrls.admin.dataLoad.getManageOngoingUrl(item.index_id)
          );
        }}
        rows={data?.results?.filter((r: any) =>
          util.filterRow(r, ["title", "records"], search)
        )}
        render={renderRow}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={onPageSelect}
        isLoading={isLoading}
        onSort={(item: any) => {
          const label = util.label.getSortingLabel(
            item.sortKey,
            item.sortValue
          );
          setColumnsData((_columns: any) => {
            return Object.values(tableMeta.resetColumns(_columns, item));
          });
          props.dataCall({ associateOrdering: label });
        }}
        {...(search && { notFoundMsg: util.noSearchRecordsFoundMsg })}
      />
      {!isLoading && data.results.length === 0 && <Empty />}
    </Div>
  );
}
