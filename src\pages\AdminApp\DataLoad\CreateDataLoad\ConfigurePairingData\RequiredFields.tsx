import React from "react";
import {
  <PERSON>,
  Layout,
  But<PERSON>,
  Div,
  CustomModal as Mo<PERSON>,
  Icon,
} from "unmatched/components";
import { getSamplePairingDownload } from "../../dataload-api";
// import { Modal.Body, ModalFooter } from "../CreateDataLoad.style";

export default function RequiredFields(props: any) {
  const { setScreen, loading, data } = props;
  return (
    <>
      <Modal.Body>
        <Text.H2 className="pb-2 text-capitalize ">Add pairings</Text.H2>
        <Text.P1 className="pb-4">
          Create a list of pairings with the reviewer and the reviewee pairs and
          upload the list to the platform. Below are the required fields to
          create pairings. You can download a sample excel/csv file to work the
          list.
        </Text.P1>

        {data.map((_d: string, i: number) => (
          <Text.P1 className="pb-2" key={i}>
            {_d}
          </Text.P1>
        ))}
        {/* <Text.H2 className="pb-2">Required fields :</Text.H2>
        <Text.P1 className="pb-2">Reviewer Email</Text.P1>
        <Text.P1 className="pb-2">Reviewer First Name</Text.P1>
        <Text.P1 className="pb-2">Reviewer Last Name</Text.P1>
        <Text.P1 className="pb-2">Reviewer Employee ID</Text.P1>
        <Text.P1 className="pb-2">Reviewee Email</Text.P1>
        <Text.P1 className="pb-2">Reviewee First Name</Text.P1>
        <Text.P1 className="pb-2">Reviewee Last Name</Text.P1> 
        <Text.P1 className="pb-2">Reviewee Email</Text.P1>*/}
      </Modal.Body>
      <Modal.Footer>
        <Div className="w-100">
          <Layout.Row>
            <Layout.Col className="col-7">
              <Text.P1
                className="pb-0 pointer"
                style={{ fontWeight: 500 }}
                onClick={() => getSamplePairingDownload({ f: "xlsx" })}
              >
                {/* <b>
                  Download sample{" "}
                  <span
                    className="cursor-pointer"
                    
                  >
                    excel
                  </span>
                  /
                  <span
                    className="cursor-pointer"
                    onClick={() => getSamplePairingDownload({ f: "csv" })}
                  >
                    csv
                  </span>{" "}
                  file
                </b> */}
                <Icon icon="far fa-arrow-alt-circle-down mr-1" />
                <u>Download sample excel(.xlsx) file</u>
              </Text.P1>
            </Layout.Col>
            <Layout.Col>
              <Button
                className="float-right"
                type="button"
                variant="primary"
                size="lg"
                onClick={() => {
                  setScreen(2);
                }}
                disabled={loading}
              >
                Next
              </Button>
            </Layout.Col>
          </Layout.Row>
        </Div>
      </Modal.Footer>
    </>
  );
}
