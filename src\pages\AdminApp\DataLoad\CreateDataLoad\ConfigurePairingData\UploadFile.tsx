import React from "react";
import {
  Text,
  Layout,
  Div,
  Button,
  MultiSelect,
  FormControl,
  FormGroup,
  CustomModal as Modal,
} from "unmatched/components";
import data from "../../../../../assets/images/images";
import icons from "assets/icons/icons";
import Loader from "../../../../../assets/images/Loader";
import { useDropzone } from "react-dropzone";
import { pairingFileUploadFact, patchPairUploadFact } from "../../dataload-api";
import {
  // ModalBody,
  // ModalFooter,
  FileCard,
  CancelButton,
  FormContainer,
  TranparentButton,
} from "../CreateDataLoad.style";
import useToastr from "unmatched/modules/toastr/hook";

const activeStyle = {
  borderColor: "#2196f3",
};
const rejectStyle = {
  borderColor: "#2196f3",
};

const uploadBox = {
  width: "100%",
  minHeight: "40vh",
  borderColor: "rgba(0,0,0,0.1)",
  borderStyle: "dashed",
  padding: "5% 5%",
  fontSize: "14px !important",
};
const isActiveDiv = {
  background: "rgba(0,0,0,0.1)",
};

const stylee = {
  uploadText: {
    textDecoration: "underline",
    cursor: "pointer",
  },
};

const Uploadfile = (props: any) => {
  const { setScreen } = props;
  const { showToast } = useToastr();

  const [Stage, setStage] = React.useState(0);
  const [File, setFile] = React.useState<any>([]);

  const [_params, setParams] = React.useState({
    id: "",
    title: "",
    tags: [],
  });

  const onSubmit = async () => {
    if (_params.title.length === 0) {
      setFile([]);
      setStage(0);
    }
    if (_params.title.length === 0) {
      return showToast({
        variant: "danger",
        title: "Add title",
        content: "Title is compulsory.",
      });
    }
    try {
      setScreen(3);
      const res = await tryUpload(File);
      await patchPairUploadFact(res.data.file_id, {
        ..._params,
        id: res.data.file_id,
      });
    } catch (err: any) {
      setScreen(2);
      setStage(1);
      showToast({
        variant: "danger",
        title: "Something went wrong",
        content: "Try again.",
      });
    }
  };

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
  } = useDropzone({
    accept: ".xlsx, .xls, .csv",
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setFile(
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        )
      );
      if (
        acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          })
        ).length > 0
      ) {
        setStage(3);
      }
    },
  });

  const titleTypes = [
    { value: 0, label: "Jr Associate", isFixed: true },
    { value: 1, label: "Associate", disabled: true },
    { value: 2, label: "Senior Associate" },
    { value: 3, label: "Partner", isFixed: true },
    { value: 4, label: "Name Partner" },
  ];

  const styledBox = React.useMemo(
    () => ({
      ...uploadBox,
      ...(isDragActive ? activeStyle : {}),
      ...(isDragAccept ? isActiveDiv : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isDragActive, isDragReject, isDragAccept]
  );

  const [Conditionals, setConditionals] = React.useState<Array<any>>([
    {
      by_user: [
        {
          type: "",
          value: "",
        },
      ],
      for_user: [
        {
          type: "",
          value: "",
        },
      ],
    },
    {
      by_user: [
        {
          type: "",
          value: "",
        },
      ],
      for_user: [
        {
          type: "",
          value: "",
        },
      ],
    },
  ]);

  const tryUpload = async (File: any) => {
    const extArray = File[0].name.split(".");
    const ext = extArray[extArray.length - 1];
    return await pairingFileUploadFact({
      File: File[0],
      Format: ext,
    });
  };

  // const tryUpload = async (File: any) => {
  //   try {
  //     const extArray = File[0].name.split(".");
  //     const ext = extArray[extArray.length - 1];
  //     const response = await pairingFileUploadFact({
  //       File: File[0],
  //       Format: ext,
  //     });
  //     setParams((p) => {
  //       return { ...p, id: response.data.file_id };
  //     });
  //     setStage(3);
  //     // upLoad;
  //   } catch (err) {
  //     setStage(1)
  //     console.log(err);
  //   }
  // };

  const AddCondition = () => {
    setConditionals([
      ...Conditionals,
      {
        by_user: [
          {
            type: "",
            value: "",
          },
        ],
        for_user: [
          {
            type: "",
            value: "",
          },
        ],
      },
    ]);
  };
  const deleteNode = (conditionId: number, nodeId: number, type: string) => {
    let conditionItem = Conditionals[conditionId];
    const updatedList = conditionItem[type].filter(
      (item: any, index: number) => {
        return index !== nodeId;
      }
    );
    conditionItem = {
      ...conditionItem,
      [type]: updatedList,
    };

    const newConditionals = Conditionals.map((item: any, index: number) => {
      return index === conditionId ? conditionItem : item;
    });
    setConditionals(newConditionals);
    // console.log(Conditionals[conditionId][type])
  };
  const addOption = (conditionId: number, type: string) => {
    setConditionals((prevConditionals) => {
      return [
        ...prevConditionals.slice(0, conditionId),
        {
          ...prevConditionals[conditionId],
          [type]: [
            ...prevConditionals[conditionId][type],
            { type: "", value: "" },
          ],
        },
        ...prevConditionals.slice(conditionId + 1),
      ];
    });
  };

  const viewStage = (screen: number) => {
    switch (screen) {
      case 1:
        return (
          <Upload
            getRootProps={getRootProps}
            getInputProps={getInputProps}
            styledBox={styledBox}
          />
        );
      case 2:
        return <OnUploadStart setStage={setStage} file={File[0]} />;

      case 3:
        return (
          <OnUploadFinish
            setStage={setStage}
            file={File[0]}
            _params={_params}
            setParams={setParams}
            onSubmit={onSubmit}
          />
        );

      default:
        break;
    }
  };
  return Stage === 0 ? (
    <SetRules
      titleTypes={titleTypes}
      setStage={setStage}
      Conditionals={Conditionals}
      setConditionals={setConditionals}
      AddCondition={AddCondition}
      deleteNode={deleteNode}
      addOption={addOption}
    />
  ) : (
    <>
      <Modal.Body>{viewStage(Stage)}</Modal.Body>
      <Modal.Footer>
        {Stage === 3 ? (
          <Div className="w-100">
            <Layout.Row>
              <Layout.Col></Layout.Col>
              <Layout.Col xs={12} md={6}>
                <Button
                  className="float-right"
                  type="button"
                  variant="primary"
                  size="lg"
                  onClick={onSubmit}
                >
                  Continue
                </Button>
              </Layout.Col>
            </Layout.Row>
          </Div>
        ) : (
          ""
        )}
      </Modal.Footer>
    </>
  );
};
const SetRules = (props: any) => {
  const {
    titleTypes,
    setStage,
    Conditionals,
    // setConditionals,
    AddCondition,
    deleteNode,
    addOption,
  } = props;
  const { AddButtonIcon, Add, CrossButton } = icons;
  return (
    <>
      <Modal.Body>
        <Text.H2 className="pb-2 text-capitalize ">Add pairing rule</Text.H2>
        <Text.P1 className="pb-4">
          To make sure to match the right employees on the pairing list, add
          pairing rules. When you upload your file, all the pairings will be
          validated with the rules you assign.
        </Text.P1>
        {Conditionals.map((cd: any, condIndex: number) => (
          <>
            <Text.H3 className="pb-2 mt-3">Condition {condIndex + 1}</Text.H3>
            <FormContainer>
              <Text.H3 className="pb-2">User that have</Text.H3>
              {cd.by_user.map((con: any, nodeIndex: number) => (
                <Layout.Row key={nodeIndex} className="mb-3">
                  <Div className="col-lg-5 col-md-12">
                    <FormGroup>
                      <FormControl.Select value="Title">
                        <FormControl.SelectItem>Title </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Location{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Practice Group{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Department{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>Level</FormControl.SelectItem>
                      </FormControl.Select>
                    </FormGroup>
                  </Div>
                  <Div className="col">
                    <MultiSelect options={titleTypes} />
                  </Div>
                  <Div className="col" style={{ maxWidth: 100 }}>
                    {cd["by_user"].length === nodeIndex + 1 ? (
                      <TranparentButton>
                        <Add
                          width="18px"
                          height="18px"
                          onClick={() => addOption(condIndex, "by_user")}
                        />
                      </TranparentButton>
                    ) : (
                      ""
                    )}
                    {nodeIndex > 0 ? (
                      <TranparentButton
                        onClick={() => deleteNode(0, nodeIndex, "by_user")}
                      >
                        <CrossButton width="18px" height="18px" />
                      </TranparentButton>
                    ) : (
                      ""
                    )}
                  </Div>
                </Layout.Row>
              ))}

              <Text.H3 className="pb-2">Can Rate users that have</Text.H3>
              {cd.for_user.map((con: any, index: number) => (
                <Layout.Row>
                  <Div className="col-lg-5 col-md-12">
                    <FormGroup>
                      <FormControl.Select value="Location">
                        <FormControl.SelectItem>Title </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Location{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Practice Group
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Department{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>Level</FormControl.SelectItem>
                      </FormControl.Select>
                    </FormGroup>
                  </Div>
                  <Div className="col">
                    <FormGroup>
                      <FormControl.Select value="-">
                        <FormControl.SelectItem>Title </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Location{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Practice Group
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>
                          Department{" "}
                        </FormControl.SelectItem>
                        <FormControl.SelectItem>Level</FormControl.SelectItem>
                      </FormControl.Select>
                    </FormGroup>
                  </Div>
                  <Div className="col" style={{ maxWidth: 100 }}>
                    <TranparentButton
                      onClick={() => addOption(condIndex, "for_user")}
                    >
                      <Add width="18px" height="18px" />
                    </TranparentButton>
                    {index > 0 ? (
                      <TranparentButton
                        onClick={() => deleteNode(0, index, "for_user")}
                      >
                        <CrossButton width="18px" height="18px" />
                      </TranparentButton>
                    ) : (
                      ""
                    )}
                  </Div>
                </Layout.Row>
              ))}
            </FormContainer>
          </>
        ))}

        <TranparentButton
          className="text-primary f12 my-3"
          type="button"
          onClick={AddCondition}
        >
          <AddButtonIcon /> Add Condition
        </TranparentButton>
      </Modal.Body>
      <Modal.Footer>
        <Div className="w-100">
          <Layout.Row>
            <Layout.Col></Layout.Col>
            <Layout.Col xs={12} md={12}>
              <Button
                className="float-right ml-3"
                type="button"
                variant="primary"
                size="lg"
                onClick={() => setStage(1)}
              >
                Continue to file upload
              </Button>
              <Button
                variant="outline-primary"
                className="float-right "
                title="asdasd"
                size="lg"
                type="button"
                onClick={() => setStage(1)}
              >
                Skip Rules
              </Button>
            </Layout.Col>
          </Layout.Row>
        </Div>
      </Modal.Footer>
    </>
  );
};
const Upload = (props: any) => {
  const { PAIRICON } = data;
  const { getRootProps, getInputProps, styledBox } = props;
  return (
    <>
      <Text.H2 className="pb-2 text-capitalize">Upload pairings</Text.H2>
      <Text.P1 className="pb-4">
        Upload the pairings file and give a title to the pairings list. You can
        give few tags that are relevant for the pairings in the list for your
        future references.
      </Text.P1>
      <div
        {...getRootProps({
          style: styledBox,
          className:
            "d-flex justify-content-center align-items-center flex-column text-center",
        })}
      >
        <input {...getInputProps()} />
        <Text.H3 className="pb-2 mb-3 f10">
          Simply drag and drop the file here to Upload or{" "}
          <span
            // htmlFor="pairuploader"
            style={stylee.uploadText}
            className="text-primary"
          >
            browse & choose your file
          </span>
        </Text.H3>
        <img src={PAIRICON} alt="" />
      </div>
      {/* <input type="file" id="pairuploader" style={{ display: "none" }} /> */}
    </>
  );
};
const OnUploadStart = (props: any) => {
  const { file } = props;
  // React.useEffect(() => {
  //   setTimeout(() => {
  //     setStage(3);
  //   }, 3000);
  // }, [setStage]);
  return (
    <>
      <Text.H2 className="pb-2 text-capitalize">Upload pairings</Text.H2>
      <Text.P1 className="pb-4">
        Upload the pairings file and give a title to the pairings list. You can
        give few tags that are relevant for the pairings in the list for your
        future references.
      </Text.P1>
      <div
        style={uploadBox}
        className="d-flex justify-content-center align-items-center flex-column text-center"
      >
        <Loader size={105} />
        <Text.H2 className="pb-2 text-primary">
          Uploading Your File, Please wait.
        </Text.H2>
        <Text.P1 className="pb-0">{file?.name}</Text.P1>
      </div>
    </>
  );
};

const OnUploadFinish = (props: any) => {
  const { file, setStage, setParams } = props;
  const { SuccessTick } = icons;
  return (
    <>
      <Text.H2 className="pb-2 text-capitalize">Upload Pairings</Text.H2>
      <Text.P1 className="pb-4">
        Upload the pairings file and give a title to the pairings list. You can
        give few tags that are relevant for the pairings in the list for your
        future references.
      </Text.P1>
      <Text.H2 className="pb-2">1 File ready to be uploaded</Text.H2>
      <FileCard className="f-12">
        <Layout.Row className="justify-content-center align-item-center">
          <Layout.Col className="col-1">
            <SuccessTick />
          </Layout.Col>
          <Layout.Col className="col-8">{file?.name}</Layout.Col>
          <Layout.Col className="col-3">
            <CancelButton onClick={() => setStage(1)}>Remove</CancelButton>
          </Layout.Col>
        </Layout.Row>
      </FileCard>
      <Div className="mt-3" style={{ maxWidth: 300 }}>
        <FormGroup>
          <FormGroup.Label>Title *</FormGroup.Label>
          <FormControl.Text
            placeholder="Give a title for the records"
            name="title"
            onChange={(e: any) =>
              setParams((_d: any) => {
                return { ..._d, title: e.target.value };
              })
            }
          />
        </FormGroup>
        <FormGroup>
          <FormGroup.Label>Tags</FormGroup.Label>
          <FormControl.Text
            placeholder="Remote, NY, Banking, etc.."
            name="tags"
            onChange={(e: any) =>
              setParams((_d: any) => {
                return { ..._d, tags: e.target.value.split(",") };
              })
            }
          />
        </FormGroup>
      </Div>
    </>
  );
};

export default Uploadfile;
