import React, { useEffect, useState } from "react";
import { useDebounce, useTable } from "unmatched/hooks";
import {
  Text,
  Layout,
  Div,
  Table,
  FormControl,
} from "unmatched/components";
import { util } from "@unmatchedoffl/ui-core";
import { get, keys, map } from "lodash";
import {
  getLogParticipants,
} from "pages/AdminApp/Survey/survey-api";

interface ParticipantsList {
  key: string;
  firstName: string;
  lastName: string;
  empId: string;
  email: string;
  location?: string;
  department?: string;
  results?: Array<any>;
  metadata?: any;
  id?: any;
}

const ParticipantsList = (props: any) => {
  const tableMeta = useTable({});
  const [searchDirty, setSearchDirty] = useState(false);

  // setMetas
  const [selectedMeta] = React.useState<any>({
    cohort: "",
    applied: undefined,
    isSet: false,
  });
  const [selectAll] = React.useState(false);
  const [ordering, setOrdering] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(true);
  const [users, setUsers] = React.useState<any>([]);
  const [search, setSearch] = React.useState("");
//   const [confirmDelete, setConfirmDelete] = useState<any>(null);
  const [selected] = React.useState<Array<string>>([]);
  React.useEffect(() => {
    // getFilters();
  }, []);

  React.useEffect(() => {
    if (users?.length) setColumnsData(getColumns());
  }, [users]);

  useEffect(() => {
    props.refreshRatersTable && getEmployees();
  }, [props.refreshRatersTable]);

  useEffect(() => {
    props.logID && getEmployees();
  }, [props.logID]);

  const getEmployees = async (params?: any) => {
    try {
      setIsLoading(true);
      const response = await getLogParticipants(
        {
          page_size: 10,
          search,
          ...(params && { ...params }),
        },
        props.logID
      );
      setUsers(response.results);
      tableMeta.updatePagination({
        totalPages: response?.totalPages, // deepscan-disable-line INSUFFICIENT_NULL_CHECK
        page: 1,
        totalItems: response.totalElements,
      });
      props.setRefreshRatersTable(false);
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setColumnsData(getColumns());
  }, [selectAll]);

  const checkSelected = (item: ParticipantsList) => {
    return selected.includes(item.id);
  };

  const onSearch = (term: string) => {
    setSearch(term);
  };

  const onPageSelect = async (page: number) => {
    try {
      setIsLoading(true);
      const obj = {};
      const response = await getLogParticipants(
        {
          page_size: 10,
          page,
          search: search.length > 0 ? search : undefined,
          ...obj,
          ...(ordering && { ordering }),
        },
        props.logID
      );
      setUsers(response?.results);
      tableMeta.updatePagination({
        totalPages: response?.totalPages,
        page,
      });
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  useDebounce(
    () => {
      searchDirty && onPageSelect(1);
    },
    500,
    [search]
  );

  useDebounce(
    () => {
      if (selectedMeta.applied === undefined && selectedMeta.isSet === false) {
        return;
      }
      onPageSelect(1);
    },
    500,
    [selectedMeta]
  );

  const getColumns = () => {
    const basic = [
      { key: 2, label: "No.", hasSort: false },
    ];

    return [
      ...basic,
      {
        key: 3,
        label: "First Name",
        hasSort: false,
        sortValue: "",
        sortKey: "first_name",
      },
      {
        key: 4,
        label: "Last Name",
        hasSort: false,
        sortValue: "",
        sortKey: "last_name",
      },
      {
        key: 5,
        label: "Employee ID",
        hasSort: false,
        sortValue: "",
        sortKey: "emp_id",
      },
      {
        key: 6,
        label: "Email",
        hasSort: false,
        sortValue: "",
        sortKey: "email",
      },
    ];
  };

  const [columnsData, setColumnsData] = React.useState<any>(getColumns()); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL

  const getCompColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const getRows = () => {
    return users.map((item: ParticipantsList, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.key} selected={checked}>
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firstName}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.lastName}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.empId}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.email}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const getFilterLayout = () => {
    return (
      <Div>
        <Layout.Row className="py-3">
          <Layout.Col lg={5} md={5}>
          </Layout.Col>

          <Layout.Col
            className="d-flex flex-row justify-content-end"
            lg={7}
            md={7}
          >
            <FormControl.Search
              value={search}
              onChange={(e: any) => {
                setSearchDirty(true);
                setSearch(e.target.value);
              }}
              onSearch={(value: string) => {
                onSearch(value);
              }}
              placeholder="Search for name, email or emp id"
            />
          </Layout.Col>
        </Layout.Row>
      </Div>
    );
  };

  return (
    <Div  style={{ minHeight: 500 }} className="bg-white pb-5">
      <>
        {getFilterLayout()}
        <Table
          columns={getCompColumns()}
          isLoading={isLoading}
          rows={users}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={(d: any) => {
            tableMeta.onPageSelect(d);
            onPageSelect(d);
          }}
          style={{ overflowX: "scroll" }}
          onSort={(item: any) => {
            const label = util.label.getSortingLabel(
              item.sortKey,
              item.sortValue
            );
            setColumnsData((_columns: any) => {
              return Object.values(tableMeta.resetColumns(_columns, item));
            });
            setOrdering(label);
            getEmployees({ ordering: label });
            // dataCall({ empOrdering: label });
          }}
          totalItems={tableMeta.totalItems}
          {...(search && { notFoundMsg: util.noSearchRecordsFoundMsg })}
        />
      </>
    </Div>
  );
};

export default ParticipantsList;
