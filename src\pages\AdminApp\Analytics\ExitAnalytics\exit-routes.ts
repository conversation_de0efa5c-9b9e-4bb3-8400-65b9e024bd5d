import appUrls from "unmatched/utils/urls/app-urls";
import AnalyticsView from "./AnalyticsView/AnalyticsView";
// import PeopleAnalytics from "./PeopleAnalytics/PeopleAnalytics";
// import PeopleList from "./PeopleList/PeopleList";
import SurveyList from "./SurveyList/SurveyList";

const routes = [
  // {
  //   name: "People analytics container",
  //   path: appUrls.admin.analytics.people.getAnalyticsUrl(":id"),
  //   isExact: false,
  //   component: PeopleAnalytics,
  // },
  // {
  //   name: "Survey List",
  //   path: appUrls.admin.analytics.people.getListView(":id"),
  //   isExact: false,
  //   component: PeopleList,
  // },
  {
    name: "Survey List - Exit Analytics",
    path: appUrls.admin.analytics.exit.list,
    isExact: true,
    component: SurveyList,
  },
  {
    name: "Exit Analytics View",
    path: appUrls.admin.analytics.exit.getListView(":id"),
    isExact: true,
    component: AnalyticsView,
  },
];

export default routes;
