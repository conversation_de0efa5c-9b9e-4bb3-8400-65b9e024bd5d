import useFilter from "pages/CommonFilters/hook";
import React, { useState } from "react";
import { Div, ComboFilter, Text } from "unmatched/components";
import { getUsersFact } from "unmatched/modules/session/api";

const MultiCompositeFilter = ({
  onFilterSelect,
  onFilterRemove,
  disallowedFilters,
}: any) => {
  const [filterGroups, setFilterGroups] = useState([{ id: 1 }]);

  return (
    <Div className="my-4">
      <Div>
        {filterGroups.map((fg: any) => {
          return (
            <CompositeFilterGroup
              groupID={fg.id}
              key={fg.id}
              onFilterSelect={onFilterSelect}
              setFilterGroups={setFilterGroups}
              filterGroups={filterGroups}
              onFilterRemove={onFilterRemove}
              disallowedFilters={disallowedFilters}
            />
          );
        })}
      </Div>

      <Text.P1
        onClick={() =>
          setFilterGroups((s) => [
            ...s,
            { id: Math.floor(Math.random() * 90000) + 10000 },
          ])
        }
        style={{ color: "#518CFF" }}
        className="cursor-pointer mt-2"
      >
        Add another compare group
      </Text.P1>
    </Div>
  );
};

export default MultiCompositeFilter;

const CompositeFilterGroup = ({
  onFilterSelect,
  groupID,
  setFilterGroups,
  filterGroups,
  onFilterRemove,
  disallowedFilters = []
}: any) => {
  const filtersState = useFilter();

  const onSearchPeople = (_val: string) => {
    return getUsersFact(_val).then((response: any) => {
      return response.map((item: any) => `${item.name}(${item.empId})`);
    });
  };

  return (
    <Div className="mb-1">
      <ComboFilter
        filters={{
          ...filtersState.filters,
          ...(!disallowedFilters.includes('people') && {
            people: {
              label: "People",
              values: [],
              isDynamic: true,
              onSearch: onSearchPeople,
            },
          }),
        }}
        selected={filtersState.selected}
        onFilterSelect={(_selected: any) => {
          const computedFilters = {
            ..._selected,
            ...(_selected.people && {
              people: _selected.people.map((p: any) =>
                p.substring(p.indexOf("(") + 1, p.indexOf(")"))
              ),
            }),
          };

          filtersState.onSelect(_selected);
          onFilterSelect(computedFilters, `${groupID}`);
        }}
        onSubmit={() => ""}
      />
      {filterGroups.length > 1 && (
        <Div
          onClick={() => {
            setFilterGroups((s: any) =>
              s.filter((el: any) => el.id !== groupID)
            );
            onFilterRemove(groupID);
          }}
          className="fs-10 cursor-pointer"
          style={{ color: "red", textAlign: "right" }}
        >
          Remove
        </Div>
      )}
    </Div>
  );
};
