import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";
//
import api from "unmatched/utils/api";
import API_URLS from "unmatched/utils/urls/api-urls";

export const getOverallInfo = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.EXIT_OVERALL_URL, config)
    .then(({ data }: AxiosResponse) => {
      const { participation_stats, gap_analysis } = data;
      return {
        stats: {
          total: participation_stats.total_reviewers,
          notVisited: participation_stats.not_visited,
          visited: participation_stats.visited,
          participated: participation_stats.participated,
          participatedPercentage: participation_stats.participation_percentage,
        },
        top: gap_analysis.top,
        bottom: gap_analysis.bottom,
      };
    });
};

// export const getDemographicsSchema = (params?: any, meta?: any) => {
//   const config = api.getConfigurations(params, meta);
//   return axios
//     .get(API_URLS.DEMOGRAPHICS_SCHEMA, config)
//     .then(({ data }: AxiosResponse) => {
//       return data.map((_dt: any) => {
//         return {
//           key: _dt.name,
//           title: _dt.name,
//           options: _dt.options,
//         };
//       });
//     });
// };

export const getExitSurveyDetails = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.EXIT_URL, config)
    .then(({ data, status }: AxiosResponse) => {
      if (status === 206) {
        return [];
      }
      return data.timeseries.map((item: any) => {
        const {
          comment_sentiment,
          distribution,
          survey_index,
          total_participants,
          year,
          survey_index_title,
          not_participated,
          participated,
          total_groups,
          total_invite_sent,
        } = item;
        return {
          title: survey_index_title,
          sentiment: comment_sentiment,
          distribution: distribution,
          surveyIndex: survey_index,
          totalParticipants: total_participants,
          notParticipated: not_participated,
          participated,
          totalGroups: total_groups,
          totalInviteSent: total_invite_sent,
          year,
        };
      });
    });
};

export const getExitSurveyItemized = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.EXIT_ITEM_URL, config)
    .then(({ data, status }: AxiosResponse) => {
      if (status === 206) {
        return [];
      }
      return data.timeseries.map((item: any) => {
        const {
          comment_sentiment,
          distribution,
          survey_index,
          total_participants,
          year,
          survey_index_title,
          not_participated,
          participated,
          total_groups,
          total_invite_sent,
        } = item;
        return {
          title: survey_index_title,
          sentiment: comment_sentiment,
          distribution: distribution,
          surveyIndex: survey_index,
          totalParticipants: total_participants,
          notParticipated: not_participated,
          participated,
          totalGroups: total_groups,
          totalInviteSent: total_invite_sent,
          year,
        };
      });
    });
};

export const getSectionAndQuestionFromID = (
  surveyVersionID: number,
  params?: any,
  meta?: any
) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(`${API_URLS.ADMIN_SURVEY_VERSIONS}${surveyVersionID}`, config)
    .then(({ data }: AxiosResponse) => {
      return {
        id: data.id,
        title: data.name,
        sections: data.sections,
        indexID: data.index_id,
      };
    });
};

export const getCommentsFromQuestionID = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.EXIT_COMMENT, config)
    .then(({ data }: AxiosResponse) => {
      return data;
    });
};
export const getSectionComparative = (
  data: { indexId: string; compareGroup: string },
  params?: any,
  meta?: any
) => {
  const modData = {
    index_id: data.indexId,
    compare_group: data.compareGroup,
  };

  const config = api.getConfigurations(params, meta);
  return axios
    .post(`${API_URLS.EXIT_COMPARATIVE_ANALYSIS}/section/`, modData, config)
    .then(({ data }: AxiosResponse) => {
      if (data.data.length > 0) {
        const col: any = [];
        const row: any = [];
        data.options.map((_d: string, i: number) => {
          return col.push({ label: _d, id: i });
        });
        data.data.map((_d: any, i: number) => {
          return row.push({
            id: i,
            label: _d.name,
            ..._d["compare_groups"],
          });
        });
        return { col, row };
      } else {
        return {
          row: [],
          col: [],
        };
      }
    });
};
export const getQuestionComparative = (data: any, params?: any, meta?: any) => {
  const modData = {
    index_id: data.indexId,
    compare_group: data.compareGroup,
    question_ids: data.questions,
  };
  const config = api.getConfigurations(params, meta);
  return axios
    .post(`${API_URLS.EXIT_COMPARATIVE_ANALYSIS}/questions/`, modData, config)
    .then(({ data }: AxiosResponse) => {
      if (data.data.length > 0) {
        const col: any = [];
        const row: any = [];
        data.options.map((_d: string, i: number) => {
          return col.push({ label: _d, id: i });
        });
        data.data.map((_d: any, i: number) => {
          return row.push({
            id: i,
            label: _d.label,
            ..._d["compare_groups"],
          });
        });
        return { col, row };
      } else {
        return {
          row: [],
          col: [],
        };
      }
    });
};

interface PatchDataFormat {
  id: string;
  flag: boolean;
}
export const patchCommentFlag = (data: PatchDataFormat, meta?: any) => {
  const params = {
    flag: data.flag,
  };
  const config = api.getConfigurations({}, meta);
  return axios
    .patch(`${util.apiUrls.COMMENT_OPERATION(data.id)}`, { ...params }, config)
    .then((response: AxiosResponse) => {
      return response.data;
    });
};
