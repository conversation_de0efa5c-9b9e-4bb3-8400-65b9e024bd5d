import React from "react";
// Helpers
import useCustomLayout from "../../Shared/custom-layout-hook";
// Components
import { Button, Navbar, Nav } from "unmatched/components";

interface FooterProps {
  onBack?: Function;
  onNext?: Function;
  submitText?: React.ReactNode;
  hideBack?: boolean;
  buttonType?: string;
}

const Footer = (props: FooterProps) => {
  const { onBack, onNext, buttonType, hideBack, submitText } = props;
  const padding = "20px 30px 20px 30px";
  const layout = useCustomLayout();

  return (
    <Navbar
      className="bg-white border-top py-3"
      style={{ marginLeft: layout.marginLeft, padding }}
      fixed="bottom"
    >
      <Nav>
        {!hideBack && (
          <Button onClick={onBack} size="lg" variant="outline-primary">
            Back
          </Button>
        )}
      </Nav>
      <Nav className="ml-auto">
        <Button type={buttonType} variant="primary" size="lg" onClick={onNext}>
          {submitText || "Next"}
        </Button>
      </Nav>
    </Navbar>
  );
};

Footer.defaultProps = {
  buttonType: "button",
};

export default Footer;
