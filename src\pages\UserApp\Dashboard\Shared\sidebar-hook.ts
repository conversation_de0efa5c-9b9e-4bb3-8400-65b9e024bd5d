import { useCheckSelector } from "unmatched/hooks";
import { userMetadataSelector } from "../../user-app-store";

const WIDTH = 250;

const useSidebar = () => {
  const meta = useCheckSelector(userMetadataSelector);
  return {
    sidebar: {
      width: meta.sidebar ? `${WIDTH}px` : "0px",
      marginLeft: meta.sidebar ? `${meta.sidebarWidth}px` : "0px",
    },
    container: {
      marginLeft: meta.margin ? `${WIDTH}px` : "0px",
    },
    marginLeft: meta.margin ? `${WIDTH + meta.sidebarWidth}px` : "0px",
  };
};

export default useSidebar;
