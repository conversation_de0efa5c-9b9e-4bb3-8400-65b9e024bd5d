// import React from "react";
// useXHR
// import { useState } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";

const surveyColors: any = {
  [util.enums.Survey.Upward]: "#FDD7C2",
  [util.enums.Survey.Engagement]: "#9AB7D3",
  [util.enums.Survey.Self]: "#F6EAC2",
  [util.enums.Survey._360Degree]: "#f1d8ef",
  [util.enums.Survey.Exit]: "#c2a047",
};

const useUtil = () => {
  const toastr = useToastr();

  const getSurveyColor = (type?: string) => {
    return type ? util.lib.get(surveyColors, type) : "#eee";
  };

  return {
    toastr,
    getSurveyColor,
  };
};

export default useUtil;
