import React from "react";
import { useParams } from "unmatched/hooks";
import { getSurveyByIdFactV2 } from "pages/AdminApp/Survey/survey-api";
import util from "unmatched/utils";
import UpwardSurveyReport from "./UpwardSurveyReport/UpwardSurveyReport";
import EngagementSurveyReport from "./EngagementSurveyReport/EngagementSurveyReport";

export default function ReportsContainer() {
  const params = useParams<any>();
  const [survey, setSurveyInfo] = React.useState<any>({
    id: params.id,
    name: "",
    type: "",
    startDate: new Date().toDateString(),
    endDate: new Date().toDateString(),
  });

  React.useEffect(() => {
    async function fetchSurveyInfo() {
      const response: any = await getSurveyByIdFactV2(params.id);
      const { getFormatedTime } = util.date;
      // const startDate = response.startDate?.toISOString();
      setSurveyInfo((_d: any) => {
        return {
          ..._d,
          name: response.name,
          type: response.type,
          startDate: getFormatedTime(response.startDate, "dd MMMM yyyy"),
          endDate: getFormatedTime(response.endDate, "dd MMMM yyyy"),
        };
      });
    }
    fetchSurveyInfo();

    //eslint-disable-next-line
  }, []);

  const getView = (type: string) => {
    switch (type) {
      case "SurveyIndexEngagement":
        return <EngagementSurveyReport survey={survey} />;

      case "SurveyIndexUpward":
        return <UpwardSurveyReport survey={survey} />;

      case "SurveyIndex360":
        return <UpwardSurveyReport survey={survey} />;
      default:
        return <></>;
    }
  };
  return <>{getView(survey.type)}</>;
}
