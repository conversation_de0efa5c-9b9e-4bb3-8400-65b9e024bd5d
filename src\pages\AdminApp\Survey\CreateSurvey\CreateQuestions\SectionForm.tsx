import React from "react";
import * as yup from "yup";
import { useFormik } from "unmatched/hooks";
import util from "unmatched/utils";
import surveyCore from "unmatched/survey/creation/components";
import {
  cloneSectionFact,
  destroySectionFact,
  patchSurveySectionFact,
} from "../../survey-api";
import { useCreateSurveyContext } from "../Provider";

const SectionFormComponent = surveyCore.SectionForm;

const SectionForm = (props: {
  sectionId: number | string;
  children: React.ReactNode;
  updateValidators: Function;
  setLoading: Function;
  onSectionDataUpdate: Function;
  onSectionClone: Function;
  showCloneDelete: boolean;
}) => {
  const {
    sectionId,
    updateValidators,
    setLoading,
    onSectionDataUpdate,
    onSectionClone,
    showCloneDelete,
  } = props;

  const { onDeleteSection, sectionsData } = useCreateSurveyContext();
  const section = sectionsData.data.find((item: any) => item.id === sectionId);

  const onSubmit = ({ name }: any) => {
    setLoading(true);

    patchSurveySectionFact(section.id, name).then(
      () => {
        setLoading(false);
        sectionsData.setData(
          sectionsData.data.map((item: any) =>
            item.id === sectionId
              ? {
                  ...item,
                  name,
                }
              : item
          )
        );
        if (onSectionDataUpdate) onSectionDataUpdate();
      },
      () => {
        setLoading(false);
      }
    );
  };

  const onClone = () => {
    cloneSectionFact(sectionId).then((response: any) => {
      onSectionClone(response);
    });
  };

  const onSectionDelete = () => {
    setLoading(true);
    return destroySectionFact(sectionId).then(
      () => {
        onDeleteSection(sectionId);
        setLoading(false);
      },
      () => {
        setLoading(false);
      }
    );
  };

  const initialValues = {
    name: section.name || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: yup.object().shape({
      name: yup.string().required(),
    }),
    onSubmit: () => {
      // on submit
    },
  });

  React.useEffect(() => {
    const key = `section-${sectionId}`;
    updateValidators({
      key,
      validate: () => util.formik.formikSubmitForm(formik),
    });
    return () => {
      updateValidators({
        key,
        validate: null,
      });
    };
  }, []);

  return (
    <SectionFormComponent
      formik={formik}
      setLoading={sectionsData.setSaving}
      onSubmit={onSubmit}
      onClone={onClone}
      onDelete={onSectionDelete}
      permissions={{
        canShowDelete: showCloneDelete,
      }}
    >
      {props.children}
    </SectionFormComponent>
  );
};

export default React.memo(SectionForm, util.lib.isEqual);
