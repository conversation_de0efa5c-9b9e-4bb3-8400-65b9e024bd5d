import React, { useEffect, useState } from "react";
import {
  Div,
  Text,
  Card,
  Table,
  CustomModal as Modal,
  Icon,
  MultiSelect,
  Button,
} from "unmatched/components";
import {
  addDndUsers,
  deleteDndUsers,
  getAllDndUsers,
} from "../../manage-survey-api";
import { useParams } from "react-router";
import { useTable } from "unmatched/hooks";
import { components } from "react-select";
import { cloneDeep, debounce } from "lodash";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";
import icons from "assets/icons/icons";
import useToastr from "unmatched/modules/toastr/hook";
const { Add, CrossButton } = icons;

const optionStyles = {
  option: (styles: any, state: any) => {
    return {
      ...styles,
      backgroundColor: state.isSelected ? "#518cff" : "#fff",
      color: state.isSelected ? "#FFF" : "#000",
      cursor: state.isDisabled ? "not-allowed" : "default",
    };
  },
};

export default function DndList() {
  const [users, setUsers] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const tableMeta = useTable({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [userOptions, setUserOptions] = useState<any>([]);
  const { id } = useParams<any>();
  const toast = useToastr();

  const [dndData, setDndData] = useState([null]);

  const getDndUsers = async (page?: number) => {
    try {
      const users: any = await getAllDndUsers(id, {
        page: page ?? undefined,
        page_size: 10,
      });
      tableMeta.updatePagination({
        page: page ?? 1,
        totalPages: users.count_pages,
        totalItems: users.count_items,
      });
      setLoading(false);
      setUsers(users.results);
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    getDndUsers();
  }, []);

  const onSelect = (i: number) => (user: any) => {
    const newDndData = cloneDeep<any>(dndData);
    newDndData[i] = user.id;
    setDndData(newDndData);
  };

  const CustomOption = (props: any) => {
    return (
      <div className="px-2 py-1 fs-10">
        <components.Option {...props} className="rounded pt-1 pb-2">
          <Div className="fw-600 fs-12">
            {props.data.first_name} {props.data.last_name}
          </Div>
          <Div>{props.data.email}</Div>
          <Div>ID: {props.data.emp_id}</Div>
        </components.Option>
        <hr className="my-0" />
      </div>
    );
  };

  const getComputedUsers = (users: any) => {
    const usersData = users.map((u: any) => ({
      ...u,
      label: u.email,
      value: u.emp_id,
    }));
    return usersData;
  };
  const getUserOptions = debounce((search: string) => {
    searchUsersFact("", { search })
      .then((res) => {
        const data = getComputedUsers(res.data?.results || []);
        setUserOptions(data);
      })
      .catch((err) => console.log(err));
  }, 200);

  const onAddDndUser = async () => {
    try {
      await addDndUsers(id, { do_not_disturb: dndData });
      getDndUsers(1);
      toast.onSucces({
        title: "Success",
        content: "User Added To DND List.",
      });
      setShowAddModal(false);
    } catch (error) {
      toast.onError({ msg: "Something Went Wrong." });
    }
  };

  const inputProps = {
    closeMenuOnSelect: true,
    isCustom: true,
    onInputChange: getUserOptions,
    isMulti: false,
    placeholder: "",
    options: userOptions,
    CustomOption,
    styles: optionStyles,
    postfix: () => null,
    customValContainer: true,
    className: "flex-grow-1",
  };

  const deleteSingleDndUser = async (page: number, userID: number) => {
    try {
      await deleteDndUsers(id, { do_not_disturb: [userID] });
      toast.onSucces({
        title: "Success",
        content: "User Removed From DND List.",
      });
      getDndUsers(page);
    } catch (err) {
      toast.onError({ msg: "Something Went Wrong." });
    }
    // const newPairData = dndData.filter((_: any, i: number) => i !== index);
    // setDndData(newPairData);
    // setPairErrors(pairErrors.filter((_: any, i: number) => i !== index));
  };

  return (
    <>
      <Card className="mb-4" noShadow>
        <Card.Header className="px-3 py-2">
          <Text.H3>Do Not Disturb (DND) list</Text.H3>
        </Card.Header>
        <Div className="px-3 py-2">
          <Div className="d-flex justify-content-between align-items-center py-3">
            <Text.P1 style={{ fontSize: 14 }}>
              The listed raters in the DND list will not receive any emails
              pertaining to this survey.
            </Text.P1>
            <Button onClick={() => setShowAddModal(true)}>Add Users</Button>
          </Div>

          <Table
            columns={[
              { key: 0, label: "Sr. No", hasSort: false },
              { key: 1, label: "Emp ID", hasSort: false },
              { key: 2, label: "Name", hasSort: false },
              { key: 3, label: "Email", hasSort: false },
              { key: 4, label: "Action", hasSort: false },
            ]}
            type="striped"
            rows={users}
            render={(item: any, i) => {
              return (
                <>
                  <Table.Data>
                    <Text.P1>{tableMeta.page * 10 - 10 + i + 1}.</Text.P1>
                  </Table.Data>
                  <Table.Data>
                    <Text.P1>{item.emp_id}</Text.P1>
                  </Table.Data>
                  <Table.Data>
                    <Text.P1>
                      {item.first_name} {item.last_name}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data>
                    <Text.P1>{item.email}</Text.P1>
                  </Table.Data>
                  <Table.Data>
                    <Text.P1>
                      <Button
                        variant="link"
                        className="p-0"
                        onClick={() =>
                          deleteSingleDndUser(tableMeta.page, item.id)
                        }
                      >
                        <Icon icon="far fa-times-circle text-danger" />
                      </Button>
                    </Text.P1>
                  </Table.Data>
                </>
              );
            }}
            hasPagination
            activePage={tableMeta.page}
            isLoading={isLoading}
            pages={tableMeta.totalPages}
            size={tableMeta.size}
            totalItems={tableMeta.totalItems}
            onPageSelect={(page: any) => getDndUsers(page)}
          />
        </Div>
      </Card>
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Add Dnd Users</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {dndData.map((_, index) => (
            <>
              <Text.H3>DND User:</Text.H3>
              <Div className="d-flex flex-row align-items-center justify-content-center my-3">
                <MultiSelect
                  {...inputProps}
                  onSelect={onSelect(index)}
                  // hasError={(formDirty && errors[index]?.rater) || pairErrors[index]}
                />

                {dndData.length > 1 && (
                  <Button variant="link" onClick={() => deleteDndSelect(index)}>
                    <CrossButton width="18px" height="18px" />
                  </Button>
                )}
                {dndData.length === index + 1 ? (
                  <Button
                    variant="link"
                    className="px-0"
                    onClick={() => setDndData((data: any) => [...data, null])}
                    style={{ width: 36 }}
                  >
                    <Add width="18px" height="18px" />
                  </Button>
                ) : (
                  <div style={{ width: 36 }} />
                )}
              </Div>
              <hr />
            </>
          ))}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={onAddDndUser}>Add</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}
