import React, { useState } from "react";
import { Text, Layout, Tab, Nav, Div, Button } from "unmatched/components";
import AddPairings from "./AddPairings/AddPairings";
import AllPairingsList from "./AllPairingsList/AllPairingsList";
import FilesList from "./FilesList/FilesList";
import { UMTab } from "unmatched/components/Tabs";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import { getPossibleRaters } from "pages/AdminApp/Survey/survey-api";
import {
  fetchRealPairingsFact,
  fetchSurveyAbstractPairingsFact,
  generatePairingFact,
} from "./pairings-api";
import { Spinner } from "react-bootstrap";
import useToastr from "unmatched/modules/toastr/hook";

const PairingTabs = [
  { key: "files", label: "Files" },
  { key: "list", label: "All Pairs" },
];

const SURVEY_BUILDER_ROUTE_CHUNK = "admin/survey/create";
export default function PairingsList(props: any) {
  const toast = useToastr();
  const [activeTab, setActiveTab] = useState(PairingTabs[0].key);
  const [show, setShow] = useState(false);
  const [pairingCount, setPairingCount] = useState(0);
  const [targetCount, setTargetCount] = useState<any>(0);
  // const [isDismissed, setIsDismissed] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  // useEffect(() => {

  // }, [])

  const initCall = async () => {
    try {
      setIsLoading(true);
      const d = await getPossibleRaters({}, props?.survey?.data?.id);

      let adminPairings;
      if (window.location.href.includes(SURVEY_BUILDER_ROUTE_CHUNK)) {
        adminPairings = await fetchSurveyAbstractPairingsFact({
          index_id: props?.survey?.data?.id,
        });
      } else {
        adminPairings = await fetchRealPairingsFact({
          index_id: props?.survey?.data?.id,
        });
      }

      setTargetCount(d.totalElements ?? 0);
      setPairingCount(adminPairings.data?.count_items);

      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setPairingCount(0);
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    initCall();
  }, []);

  const getTabsTemplate = () => {
    return (
      <Div className="custom-tabs-2">
        <Tab.Container activeKey={activeTab}>
          {/* <Nav className="nav-tabs">
            {PairingTabs.map((tab: any) => (
              <Nav.Item key={tab.key}>
                <Nav.Link
                  as={Div}
                  eventKey={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className="cursor-pointer"
                >
                  {tab.label}
                </Nav.Link>
              </Nav.Item>
            ))}
          </Nav> */}
          <Tab.Content>
            <Tab.Pane eventKey={"files"}>
              <Text.P2 className="my-3 user-select-none">
                Click on add pairings to add participants to the survey.
              </Text.P2>
              <FilesList
                show={show}
                survey={props.survey?.data}
                canDelete={props.canDelete}
              />
            </Tab.Pane>
            <Tab.Pane eventKey={"list"}>
              <AllPairingsList
                show={show}
                survey={props.survey?.data}
                setIsLoading={setIsLoading}
              />
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Div>
    );
  };

  const getNavItem = (title: string, key: string) => {
    return (
      <UMTab
        eventKey={key}
        activeKey={activeTab}
        onClick={() => setActiveTab(key)}
      >
        {title}
      </UMTab>
    );
  };

  const onGeneratePairs = async () => {
    //
    try {
      await generatePairingFact({
        data: { survey_index: props?.survey?.data?.id },
      });
      toast.onSucces("Pairs Generated.");
      initCall();
    } catch (error) {
      toast.onError(error);
    }
  };

  return (
    <Div className="position-relative w-100" style={{ minHeight: "80vh" }}>
      {isLoading ? (
        <Div
          className="w-100 position-absolute h-100 bg-white d-flex align-items-center justify-content-center flex-column"
          style={{ zIndex: 9999999, top: 15 }}
        >
          <Spinner animation="border" />
          <i className="mt-3 fs-12 text-muted">Loading Pairs...</i>
        </Div>
      ) : (
        <>
          {pairingCount > 0 || (targetCount > 1 || targetCount === 0) ? (
            <>
              <CustomHeader
                metaItem={
                  <Layout.Flex>
                    <Layout.FlexItem className="pl-3">
                      {props.survey?.data?.isLocked ? null : (
                        <Button onClick={() => setShow(true)}>
                          Add Pairings
                        </Button>
                      )}
                    </Layout.FlexItem>
                  </Layout.Flex>
                }
                title={
                  <Div>
                    <Text.H1 className="pb-2">Pairings</Text.H1>
                    <Div
                      className="sticky-tabs-container"
                      style={{ position: "absolute" }}
                    >
                      <Nav className="nav-tabs sticky">
                        {getNavItem("Files", "files")}
                        {getNavItem("All Pairs", "list")}
                      </Nav>
                    </Div>
                  </Div>
                }
                breadcrumbs={props.breadcrumbs}
                style={{ marginLeft: 310, height: 107 }}
              />
              <Layout.Container fluid className="pt-4">
                <Div className="pt-2">{getTabsTemplate()}</Div>
              </Layout.Container>{" "}
              <AddPairings
                size="lg"
                onHide={() => {
                  setShow(false);
                }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                dialogClassName="modal-90w"
                backdrop="static"
                show={show}
                survey={props.survey}
              />
            </>
          ) : (
            <Div className="m-3 border rounded p-3">
              <Text.P1 className="mb-3">
                We found out there is only one target added to the survey. Do
                you want to map all the possible raters to the single target?
              </Text.P1>
              <Button onClick={onGeneratePairs}>Proceed</Button>
              <Button variant="link" className="text-muted">
                Dismiss
              </Button>
            </Div>
          )}
        </>
      )}
    </Div>
  );
}
