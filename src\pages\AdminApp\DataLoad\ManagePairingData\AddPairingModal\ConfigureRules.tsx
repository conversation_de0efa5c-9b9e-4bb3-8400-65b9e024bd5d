import React from "react";
// Button
import {
  Text,
  Layout,
  Div,
  CustomModal as Mo<PERSON>,
  Button,
} from "unmatched/components";
// import {  } from "../CreateDataLoad.style";
import Loader from "../../../../../assets/images/Loader";
import icons from "assets/icons/icons";
import useToastr from "unmatched/modules/toastr/hook";

export default function ConfigureRules(props: any) {
  const [Status, setStatus] = React.useState(0);

  const ProcessViewer = () => {
    switch (Status) {
      case 1:
        return <OnVerifySuccess setStatus={setStatus} />;

      case 2:
        return <OnVerifyFailure />;

      default:
        return <></>;
    }
  };
  return (
    <>
      <Modal.Body>
        <Div
          className="d-flex align-items-center justify-content-center flex-column"
          style={{ minHeight: "50vh" }}
        >
          {Status === 0 ? <Verify setStatus={setStatus} /> : <ProcessViewer />}
          {/* <OnVerifySuccess/> */}
        </Div>
      </Modal.Body>
      <Modal.Footer>
        {Status === 1 ? (
          <Button
            className="float-right"
            onClick={() => props.onSuccessClose(props.onHide?.())}
          >
            Done
          </Button>
        ) : (
          ""
        )}
        {Status === 2 ? (
          <>
            <Button variant="outline-primary" className="float-right">
              Reupload
            </Button>
            <Button variant="primary" className="float-right">
              Skip and Upload
            </Button>
          </>
        ) : (
          ""
        )}
      </Modal.Footer>
    </>
  );
}

const Verify = (props: any) => {
  React.useEffect(() => {
    setTimeout(() => {
      props.setStatus(1);
      // let isSuccess = window.confirm("Confirm?");
      // if (isSuccess) {
      //   props.setStatus(1);
      // } else {
      //   props.setStatus(2);
      // }
    }, 3000);
  }, [props]);
  return (
    <>
      <Div className="d-inline">
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary f14">
              Verifying Pairings records with employee data
            </Text.H2>
            <Text.P1 className="pb-4 f12">Please wait...</Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary f14">
              Validating Rules across pairings
            </Text.H2>
            <Text.P1 className="pb-4 f12">Up Next</Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};
const OnVerifySuccess = (props: any) => {
  const { SuccessTick } = icons;
  const { showToast } = useToastr();
  React.useEffect(() => {
    showToast({
      variant: "success",
      title: "Pairings Record Added",
      content: "New pairings were added successfully",
    });
    // setTimeout(() => {

    //   // let isSuccess = window.confirm("Confirm?");
    //   // if (isSuccess) {
    //   //   props.setStatus(1);
    //   //   showToast({
    //   //     variant: "success",
    //   //     title: "Pairings Record Added",
    //   //     content: "New pairings were added successfully",
    //   //   });
    //   // } else {
    //   //   props.setStatus(2);
    //   // }
    // }, 3000);
    //eslint-disable-next-line
  }, [props]);
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-success f14">
              All employee records validated
            </Text.H2>
            <Text.P1 className="pb-4 f12">
              All the employees in this pairings file passed through existing
              employee data.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-success f14">
              All pairings validated through the rules
            </Text.H2>
            <Text.P1 className="pb-4 f12">
              All the pairings passed through the pairing rules.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};

const OnVerifyFailure = () => {
  const { DangerAlert } = icons;
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <DangerAlert width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-danger f14">
              Employee records do not match
            </Text.H2>
            <Text.P1 className="pb-4 f12">
              Employees in the pairings file don't match the existing employee
              data.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <DangerAlert width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-danger f14">
              File contains invalid pairings
            </Text.H2>
            <Text.P1 className="pb-4 f12">
              A few pairings in the file did not pass all the rules.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}></Layout.Col>
          <Layout.Col>
            <Text.P1 className="pb-2 text-primary">
              Download the mismatch records here.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};
