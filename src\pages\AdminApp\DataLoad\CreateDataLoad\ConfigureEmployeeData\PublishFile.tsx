import React from "react";
import {
  Text,
  Layout,
  Div,
  Button,
  // FormControl,
  // Form,
  // FormGroup,
  CustomModal as Modal,
} from "unmatched/components";
// import { Modal.Body, ModalFooter } from "../CreateDataLoad.style";
// import data from "../../../../../assets/images/images";
import icons from "assets/icons/icons";
import Loader from "../../../../../assets/images/Loader";

const PublishFile = (props: any) => {
  const { res, onSuccessClose, onClose } = props;
  const [Status, setStatus] = React.useState(0);

  const ProcessViewer = () => {
    switch (Status) {
      case 1:
        return <OnVerifySuccess res={res} />;

      case 2:
        return <OnVerifyFailure />;

      default:
        return <></>;
    }
  };
  return (
    <>
      <Modal.Body>
        <Div
          className="d-flex align-items-center justify-content-center flex-column"
          style={{ minHeight: "50vh" }}
        >
          {Status === 0 ? <Verify setStatus={setStatus} /> : <ProcessViewer />}
          {/* <OnVerifySuccess/> */}
        </Div>
      </Modal.Body>
      <Modal.Footer>
        {Status === 1 ? (
          <Button
            className="float-right"
            onClick={() => {
              onClose();
              onSuccessClose();
            }}
          >
            Done
          </Button>
        ) : (
          ""
        )}
        {Status === 2 ? (
          <Button variant="outline-primary" className="float-right">
            Reupload
          </Button>
        ) : (
          ""
        )}
      </Modal.Footer>
    </>
  );
};

const Verify = (props: any) => {
  React.useEffect(() => {
    setTimeout(() => {
      // let isSuccess = window.confirm("Confirm?");
      // if (isSuccess) {
      //   props.setStatus(1);
      // } else {
      //   props.setStatus(2);
      // }
      props.setStatus(1);
    }, 3000);
  }, [props]);
  return (
    <>
      <Div className="d-inline">
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary f14">
              Verifying required mandatory fields
            </Text.H2>
            <Text.P1 className="pb-4 f12">Please wait...</Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary f14">Verifying data</Text.H2>
            <Text.P1 className="pb-4 f12">
              Checking for invalid and duplicate records
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};
const OnVerifySuccess = (props: any) => {
  const { res } = props;
  const { SuccessTick } = icons;
  // React.useEffect(() => {
  //   setTimeout(() => {
  //     // let isSuccess = window.confirm("Confirm?");
  //     // if (isSuccess) {
  //     //   props.setStatus(1);
  //     // } else {
  //     //   props.setStatus(2);
  //     // }
  //     props.setStatus(1);
  //   }, 3000);
  // }, [props]);
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-success f14">
              Mandatory fields are verified
            </Text.H2>
            <Text.P1 className="pb-4 f12">
              All the fields are intact to upload employee records
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-success f14">
              All records are added/updated
            </Text.H2>
            <Text.P1 className="f12">
              {res.userAdded} new records found and added.
            </Text.P1>
            <Text.P1 className="f12">
              {res.userUpdated} existing records have been updated.
            </Text.P1>
            <Text.P1 className="f12">
              {res.uploadCount} records found and validated in total.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};

const OnVerifyFailure = () => {
  const { DangerAlert } = icons;
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <DangerAlert width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-1 text-danger f14" style={{ fontSize: 14 }}>
              Mandatory fields couldn't get verified!
            </Text.H2>
            <Text.P1 className="pb-4 f12" style={{ fontSize: 12 }}>
              Please make sure that all the records have mandatory fields filled
              in, and try to upload again.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <DangerAlert width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-1 text-danger f14" style={{ fontSize: 14 }}>
              Invalid records
            </Text.H2>
            <Text.P1 className="pb-4 f12" style={{ fontSize: 12 }}>
              The records are invalid.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}></Layout.Col>
          <Layout.Col>
            <Text.P1 className="pb-2">
              Download Sample File{" "}
              <span className="text-primary text-underline pointer mx-2">
                Excel (.xlsx)
              </span>
              <span className="text-primary text-underline pointer mx-2">
                CSV
              </span>
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};

export default PublishFile;
