import React, { useState, useEffect } from "react";
import Manualpairing from "./AddManuallyForm";
import {
  addParticipantFact,
  searchUsersFact,
  // validatePairingFact,
} from "../../participants-api";
import { cloneDeep, debounce } from "lodash";
import useToastr from "unmatched/modules/toastr/hook";
import {
  Layout,
  Text,
  CustomModal as Modal,
  Div,
  Button,
} from "@unmatchedoffl/ui-core";
import icons from "assets/icons/icons";

const { SuccessTick } = icons;
const initParticipantData = [
  {
    id: Math.random().toString().split(".")[1],
    rater: "",
    // target: "",
  },
];
export function AddUserManually(props: any) {

  const [participants, setParticipantsData] = useState(initParticipantData);
  const [errors, setErrors] = useState<any>([]);
  const [userOptions, setUserOptions] = useState<any>([]);
  const { showToast } = useToastr();
  const [pairErrors, setPairErrors] = useState<any>([]);
  const [responseData, setResponseData] = useState<any>(null);

  const { onHide, surveyIndex, setScreen } = props;

  useEffect(() => {
    updateErrors();
  }, [participants]);

  const getComputedUsers = (users: any) => {
    const usersData = users.map((u: any) => ({
      ...u,
      label: u.email,
      value: u.emp_id,
    }));
    return usersData;
  };
  const getUserOptions = debounce((search: string) => {
    searchUsersFact("", { search })
      .then((res) => {
        const data = getComputedUsers(res.data?.results || []);
        setUserOptions(data);
      })
      .catch((err) => console.log(err));
  }, 200);

  const editPairSelect = (id: number, name: string) => {
    setParticipantsData((state: any) => {
      return state.map((el: any) => (el.id === id ? { ...el, name } : el));
    });
  };

  const deletePairSelect = (index: number) => {
    const newPairData = participants.filter((_: any, i: number) => i !== index);
    setParticipantsData(newPairData);
    setPairErrors(pairErrors.filter((_: any, i: number) => i !== index));
  };

  const updateErrors = () => {
    setErrors(
      participants.map((pD: any) => ({
        target: !pD.target ? "Target is required" : "",
        rater: !pD.rater ? "Rater is required" : "",
      }))
    );
  };

  // const checkValidPair = (pair: any, i: number) => {
  //   validatePairingFact({
  //     rater: pair.rater,
  //     target: pair.target,
  //     index: surveyIndex,
  //   })
  //     .then(({ data }) => {
  //       if (data?.isValid) {
  //         return;
  //       }
  //       const err = data?.errors?.non_field_errors?.[0];
  //       const errs = cloneDeep(pairErrors);
  //       errs[i] = err || "";
  //       if (
  //         pairData.some(
  //           (pD: any) => pD.rater === pair.rater && pD.target === pair.target
  //         )
  //       ) {
  //         errs[i] = "Duplicate pair";
  //       }
  //       setPairErrors(errs);
  //     })
  //     .catch((err: any) => console.log(err));
  // };

  const hasError = (errors: any) => {
    return (
      errors.some((err: any) => err.rater || err.target) ||
      pairErrors.some((pE: string) => pE)
    );
  };

  const onSelect = (i: number, name: string) => (user: any) => {
    const newPairData = cloneDeep<any>(participants);
    newPairData[i][name] = user.id;
    setParticipantsData(newPairData);
    // if (newPairData[i].rater && newPairData[i].target)
    // checkValidPair(newPairData[i], i);
  };

  const addParticipants = async (data: any) => {
    try {
      const apiData: any = {};
      // data.index = surveyIndex;
      const raters: any = [];
      data.map((dt: any) => raters.push(dt.rater));
      apiData["raters"] = raters; 
      const record = await addParticipantFact(apiData, { id: surveyIndex });

      showToast({
        variant: "success",
        title: "Success",
        content: "Participants added successfully.",
      });
      setResponseData(record.data);
      // onHide();
      setParticipantsData(initParticipantData);
      // getPairings();
    } catch (err: any) {
      showToast({
        variant: "danger",
        title: "Error",
        content: err.msg,
      });
      console.log(err);
    }
  };

  const renderView = (props: any) => {
    return (
      <>
        {responseData === null ? (
          <Manualpairing
            {...props}
            participants={participants}
            setParticipantsData={setParticipantsData}
            editPairSelect={editPairSelect}
            deletePairSelect={deletePairSelect}
            errors={errors}
            onInputChange={getUserOptions}
            userOptions={userOptions}
            hasError={hasError}
            // updateErrors={updateErrors}
            onSelect={onSelect}
            addParticipants={addParticipants}
            initParticipantData={initParticipantData}
            pairErrors={pairErrors}
            setPairErrors={setPairErrors}
            setScreen={props.setScreen}
          />
        ) : (
          <>
            <Modal.Body>
              <Div className="d-inline">
                <Layout.Row className="my-4">
                  <Layout.Col style={{ maxWidth: 70 }}>
                    <SuccessTick
                      width="24px"
                      height="24px"
                      className="mx-3 my-2"
                    />
                  </Layout.Col>
                  <Layout.Col>
                    <Text.H2 className="pb-2 text-success f14">
                      All records are added/updated
                    </Text.H2>
                    <Text.P1 className="f12">
                      {responseData.added.length} new records found and added.
                    </Text.P1>
                    <Text.P1 className="f12">
                      {responseData.duplicates.length} duplicates ignored.
                    </Text.P1>
                    <Text.P1 className="f12">
                      {responseData.rejected.length} rejected.
                    </Text.P1>
                    <Text.P1 className="f12">
                      {responseData.total_records} records found and
                      validated in total.
                    </Text.P1>
                  </Layout.Col>
                </Layout.Row>
              </Div>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="primary"
                onClick={() => {
                  setResponseData(null);
                  onHide();
                  setScreen(1)
                }}
              >
                Done
              </Button>
            </Modal.Footer>
          </>
        )}
      </>
    );
  };

  return renderView(props);
}
