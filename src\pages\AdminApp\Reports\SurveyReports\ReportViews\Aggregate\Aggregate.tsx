import React from "react";
// import _ from "lodash";
import {
  PageContainer,
  Icon,
  Text,
  Layout,
  ComboBasicFilter,
  <PERSON><PERSON>,
  Div,
  Card,
} from "unmatched/components";
import Header from "../../Header";
import useFilter from "pages/CommonFilters/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import iconsSvgs from "assets/icons/icons";
import useToastr from "unmatched/modules/toastr/hook";

const { Page } = iconsSvgs;
import _ from "lodash";
import {
  getAggregateReports,
  requestAggregateReports,
} from "pages/AdminApp/Reports/reports-api";
// import PropTypes from 'prop-types';

const icons = {
  DOWNLOAD: "fal fa-file-download",
  EMAIL: "fal fa-paper-plane",
  CHECKED: "fal fa-check-circle",
  LOADER: "far fa-circle-notch",
};

// const COHoRTS = [
//   { key: "department", title: "Department" },
//   { key: "location", title: "Location" },
// ];

const Aggregate = (props: any) => {
  const { survey, layout, surveyId } = props;

  const [report, setReport] = React.useState<any>({});

  const [isLoading, setLoading] = React.useState(true);
  const [showNew, setShowNew] = React.useState(false);

  const toastr = useToastr();

  // const [metaFields, setMetaFields] = React.useState([
  //   {
  //     title: "",
  //     key: "",
  //     value: [],
  //   },
  // ]);
  const [selectedMeta, setSelectedMeta] = React.useState<any>({
    cohort1: "",
    applied1: undefined,
    cohort2: "",
    applied2: undefined,
    isSet: false,
  });

  // setFilters
  // const [filters] = React.useState({
  //   showUnflagged: false,
  //   cohort: {
  //     options: COHoRTS,
  //     selected: "department",
  //   },
  //   applied: {
  //     options: [],
  //     selected: "Option1",
  //   },
  // });

  const [metas, setMetas] = React.useState<any>([]);

  const filtersState = useFilter();

  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      _.forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      // arr.push({
      //   title: "All",
      //   value: ["All"],
      // });
      setMetas(arr);
    });
  };

  const initAggregateCall = async () => {
    setShowNew(false);
    try {
      const response = await getAggregateReports({ index_id: surveyId });
      // console.log(response);

      if (response.data.status === "QUE") {
        return setLoading(true);
      }
      setLoading(false);
      setReport(response.data);
    } catch (error) {}
  };

  React.useEffect(() => {
    getFilters();
    initAggregateCall();
    // filtersState.getFilters();
  }, []);

  // const updateFilters = (key: string, selected: string) => {
  //   setFilters((_filters: any) => {
  //     return {
  //       ..._filters,
  //       [key]: {
  //         ..._.get(_filters, key),
  //         selected,
  //       },
  //     };
  //   });
  // };

  const generateReport = async () => {
    if (selectedMeta.cohort1 === "") {
      return toastr.errorToast("Aggregation factor cannot be empty.");
    }
    setLoading(true);
    setShowNew(false);
    try {
      await requestAggregateReports({
        index: surveyId,
        parent: selectedMeta.cohort1,
        child: selectedMeta.cohort2 !== "" ? selectedMeta.cohort2 : undefined,
      });
    } catch (error: any) {
      toastr.onError({
        ...error,
        msg: JSON.stringify(error.msg),
      });
    }

    // setTimeout(() => {
    //   const report = { id: 1, cohort, applied };
    //   setReports([report]);
    //   setLoading(false);
    // }, 3000);
  };

  const getCohortSelected = (cohort: string) => {
    if (cohort === undefined || cohort === null) {
      return "";
    }
    return metas.filter((meta: any) => meta.key === cohort)[0]?.title ?? "";
  };
  // const getCohortValues = (cohort: string) => {
  //   if (cohort === undefined || cohort === null) {
  //     return [];
  //   }
  //   return metas.filter((meta: any) => meta.key === cohort)[0]?.value ?? [];
  // };

  const getCohortItems = (item: any) => {
    return metas.filter((_item: any) => _item.key !== item);
  };

  const filters = {
    showUnflagged: false,
    cohort1: {
      options: getCohortItems(selectedMeta.cohort2),
      selected: getCohortSelected(selectedMeta.cohort1),
    },
    applied1: {
      options: [], //getCohortValues(selectedMeta.cohort1),
      selected: "All", //selectedMeta.applied1,
    },
    cohort2: {
      options: getCohortItems(selectedMeta.cohort1),
      selected: getCohortSelected(selectedMeta.cohort2),
    },
    applied2: {
      options: [], //getCohortValues(selectedMeta.cohort2),
      selected: "All", //selectedMeta.applied2,
    },
  };

  const getFilterTemplate = () => {
    if (isLoading) return null;
    if (Object.keys(report).length > 0 && !showNew) return null;
    return (
      <>
        <Text.P1 className="py-2">
          Search for a particular department or practice group.,etc to create
          agregate report.
        </Text.P1>
        <Layout.Row className="py-3">
          <Layout.Col xl={5} lg={8} sm={12}>
            {/* <ComboFilter
              filters={filtersState.filters}
              selected={filtersState.selected}
              onFilterSelect={(_selected: any) => {
                filtersState.onSelect(_selected);
              }}
              onSubmit={() => ""}
            /> */}
            {/* <ComboFilter
              hideIcon
              cohort={filters.cohort}
              applied={filters.applied}
              onCohortUpdate={updateFilters}
              onAppliedUpdate={updateFilters}
              isAppliedShown={true}
            /> */}
            <Div className="row">
              <Div className="col">
                <ComboBasicFilter
                  cohart={filters.cohort1}
                  applied={filters.applied1}
                  onCohartUpdate={(e: string) =>
                    setSelectedMeta({
                      ...selectedMeta,
                      cohort1: e,
                      applied1: undefined,
                    })
                  }
                  onAppliedUpdate={(e: any) =>
                    setSelectedMeta({ ...selectedMeta, applied1: e.title })
                  }
                  isAppliedShown={false}
                  hideIcon={true}
                />
              </Div>
              <Div className="col">
                <ComboBasicFilter
                  cohart={filters.cohort2}
                  applied={filters.applied2}
                  onCohartUpdate={(e: string) =>
                    setSelectedMeta({
                      ...selectedMeta,
                      cohort2: e,
                      applied2: undefined,
                    })
                  }
                  onAppliedUpdate={(e: any) =>
                    setSelectedMeta({ ...selectedMeta, applied2: e.title })
                  }
                  isAppliedShown={false}
                  hideIcon={true}
                />
              </Div>
            </Div>

            {/* <Div className="mt3 pt-3 w-100" /> */}
          </Layout.Col>
        </Layout.Row>
        <Div>
          <Button onClick={generateReport}>Generate</Button>
        </Div>
      </>
    );
  };

  const getLoadingTemplate = () => {
    if (!isLoading) return null;
    return (
      <Text.P1 className="py-2">
        Please wait until your report is generated.
        <Div className="py-3">
          <Icon spin icon={icons.LOADER} variant="primary" className="fs-30" />
        </Div>
      </Text.P1>
    );
  };

  const getListTemplate = () => {
    if (isLoading || Object.keys(report).length === 0 || showNew) return null;
    return (
      <>
        <Text.P1 className="py-3">
          The following aggregate report has been generated.
        </Text.P1>
        {/* {reports.map((item: any) => { */}
        {/* return ( */}
        <Layout.Row>
          <Layout.Col xl={5}>
            <Card key={report.id}>
              <Card.Body>
                <Div className="row">
                  <Div className="col-6">
                    <Text.P1 className="text-capitalize d-inline-block">
                      {
                        metas.filter(
                          (item: any) =>
                            item.key === Object.keys(report.parameters)[0]
                        )[0]?.title
                      }
                      :
                    </Text.P1>
                    <Text.H3 className="pl-2 d-inline">All</Text.H3>
                  </Div>
                  <Div className="col-6">
                    <Text.P1 className="text-capitalize d-inline-block">
                      {
                        metas.filter(
                          (item: any) =>
                            item.key === Object.keys(report.parameters)[1]
                        )[0]?.title
                      }
                      :
                    </Text.P1>
                    <Text.H3 className="pl-2 d-inline">All</Text.H3>
                  </Div>
                </Div>
              </Card.Body>
              <Card.Footer className="pr-3">
                <Button
                  variant="outline-primary"
                  className="mr-2"
                  onClick={() => window.open(report.file, "_blank")}
                >
                  <Icon icon={icons.DOWNLOAD} className="mr-1" />
                  Download
                </Button>
                <Button variant="primary" disabled>
                  <Icon icon={icons.EMAIL} className="mr-1" />
                  Email Reports
                </Button>
              </Card.Footer>
            </Card>
          </Layout.Col>
        </Layout.Row>
        {/* );
         })}  */}
        <Button
          className="px-0 mt-3"
          variant="link"
          onClick={() => {
            setShowNew(true);
            // setReports([]);
          }}
        >
          Generate another report
        </Button>
      </>
    );
  };

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        breadcrumbs={[
          {
            label: "Reports",
            icon: <Page className="grey-icon__svg" />,
            route: appUrls.admin.reports.default,
          },
          { label: "Aggregate Reports" },
          { label: survey.name },
        ]}
      />
      <Layout.Container fluid>
        <Text.H3 className="pt-5">Generate Aggregate Reports</Text.H3>
        {getFilterTemplate()}
        {getLoadingTemplate()}
        {getListTemplate()}
      </Layout.Container>
    </PageContainer>
  );
};

// Aggregate.propTypes = {

// }

export default Aggregate;
