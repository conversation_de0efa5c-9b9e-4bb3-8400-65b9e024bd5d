import React from "react";
import { Layout, Table } from "unmatched/components";
// import PropTypes from 'prop-types'

const defaultLayout = {
  year: {
    xl: 2,
  },
  type: {
    xl: 2,
  },
  survey: {
    xl: 2,
  },
  group: {
    xl: 2,
  },
  category: {
    xl: 2,
  },
};

const AnalyticsFilters = (props: any) => {
  // hideGroups
  const {
    filters,
    config,
    onYearChange,
    onTypeChange,
    onSurveyChange,
    onGroupChange,
    onCategoryChange,
    hideTypes,
  } = props;

  const layout = {
    ...defaultLayout,
    ...(config.layout || {}),
  };

  const getYearTemplate = () => {
    const [selected] = filters.selected.years;
    return (
      <Layout.Col {...layout.year}>
        <Table.Filter
          title="Year"
          selected={selected}
          options={filters.years}
          onSelect={onYearChange}
        />
      </Layout.Col>
    );
  };

  const getTypeTemplate = () => {
    if (hideTypes) return null;
    let selected = "";
    if (filters.selected.types.length !== 3) {
      selected = filters.selected.types[0] || "";
    }
    let selectedLabel = '';
    if (selected === 'surveyindex360group') {
      selectedLabel = '360 Degree Feedback';
    } else {
      selectedLabel = (
        filters.types.find((item: any) => item.key === selected) || {}
      ).title;
    }
    return (
      <Layout.Col {...layout.type}>
        <Table.Filter
          title="Type"
          selected={selected}
          selectedLabel={selectedLabel}
          options={filters.types}
          onSelect={onTypeChange}
        />
      </Layout.Col>
    );
  };

  const getSurveyTemplate = () => {
    if (config.surveys.multiple) {
      return (
        <Layout.Col {...layout.survey}>
          <Table.FilterMultiSelect
            onOptionsSelect={onSurveyChange}
            selected={filters.selected.surveys || []}
            title={"Surveys"}
            options={filters.surveys || []}
          />
        </Layout.Col>
      );
    }
    const { id, title } =
      filters.surveys.find((item: any) =>
        filters.selected.surveys.includes(item.id)
      ) || {};
    return (
      <Layout.Col {...layout.survey} className="align-self-center">
        <Table.Filter
          title="Survey"
          selected={id}
          selectedLabel={title}
          options={filters.surveys}
          onSelect={(item: any) => {
            onSurveyChange([item.id]);
          }}
        />
      </Layout.Col>
    );
  };

  const getGroupTemplate = () => {
    const filteredTypes = filters.selected.types.filter((item: any) =>
      ["surveyindex360", "surveyindex360group"].includes(item)
    );
    if (!filteredTypes.length || !filters?.groups?.length) return null;
    if (!config.groups?.multiple) {
      const identifier = config.groups?.identifier || "id";
      const { id, title } =
        filters.groups.find((item: any) =>
          filters.selected.groups.includes(item[identifier])
        ) || {};
      return (
        <Layout.Col {...layout.group} className="align-self-center">
          <Table.Filter
            title="Rater Groups"
            selected={id}
            selectedLabel={title}
            options={filters.groups}
            onSelect={(item: any) => {
              onGroupChange([item.id]);
            }}
          />
        </Layout.Col>
      );
    }
    return (
      <Layout.Col {...layout.group} className="align-self-center">
        <Table.FilterMultiSelect
          onOptionsSelect={onGroupChange}
          selected={filters.selected.groups || []}
          title={"Rater Groups"}
          identifier={config.groups?.identifier}
          options={filters.groups || []}
        />
      </Layout.Col>
    );
  };

  const getCategoryTemplate = () => {
    if (!filters.categories.length) return;
    // const [selected] = filters.selected.categories || [];
    const { id, title } =
      filters.categories.find((item: any) =>
        filters.selected.categories.includes(item.id)
      ) || {};
    return (
      <Layout.Col {...layout.category}>
        <Table.Filter
          onSelect={(item: any) => {
            onCategoryChange([item.id]);
          }}
          selected={id || ""}
          selectedLabel={title}
          title={"Category"}
          options={filters.categories || []}
        />
      </Layout.Col>
    );
  };

  return (
    <Layout.Row>
      {getYearTemplate()}
      {getTypeTemplate()}
      {getSurveyTemplate()}
      {getGroupTemplate()}
      {getCategoryTemplate()}
    </Layout.Row>
  );
};

// AnalyticsFilters.propTypes = {}

export default AnalyticsFilters;
