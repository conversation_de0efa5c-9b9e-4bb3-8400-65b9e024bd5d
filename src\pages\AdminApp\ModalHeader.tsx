import React from "react";
import styled from "styled-components";
import { Text, <PERSON>dal, Button, Icon } from "unmatched/components";

const Styled = styled(Modal.Header).attrs({
  className: "py-2 bg-light fs-14",
})``;

export default function ModalHeader(props: any) {
  const { title, onHide } = props;
  return (
    <Styled>
      <Text.P1>{title}</Text.P1>
      <Button
        className="pull-right text-muted"
        onClick={() => (onHide ? onHide() : "")}
        size="sm"
        variant="light"
      >
        <Icon icon="far fa-times" />
      </Button>
    </Styled>
  );
}
