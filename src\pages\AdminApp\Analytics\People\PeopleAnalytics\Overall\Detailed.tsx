import DownloadExcel from "pages/AdminApp/Analytics/DownloadExcel";
import React from "react";
import { Table, Div, Sentiment, Text } from "unmatched/components";
import { useTable } from "unmatched/hooks";
import { getDetailOverallDataFact } from "../../people-api";
// import PropTypes from 'prop-types'

const Detailed = (props: any) => {
  const { userId } = props;
  const [groups, setGroups] = React.useState([]);
  const [group, setGroup]: any = React.useState({});
  const [columns, setColumns] = React.useState([]);
  const tableMeta = useTable({});

  const [list, setList] = React.useState([]);

  const getColumns = () => {
    const _columns = [
      { key: 1, label: "No.", hasSort: false, disableSelfLabel: true },
      {
        key: 2,
        label: "Rater Group",
        hasSort: false,
        disableSelfLabel: true,
      },
      {
        key: 3,
        label: "Reviewee Average",
        hasSort: false,
      },
      { key: 41, label: "Firm Average", hasSort: false },
      ...columns,
      // { key: 8, label: "Self Average", hasSort: false, disableSelf: true },
      { key: 9, label: "Comment Sentiment", hasSort: false, disableSelf: true },
    ];
    return _columns;
  };

  const getRowsTemplate = () => {
    return list.map((item: any, index: number) => {
      return (
        <Table.Row>
          <Table.Data>
            <Text.P1>{index + 1}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.raterGroup}</Text.P1>
          </Table.Data>
          {/* <Table.Data>{item.title}</Table.Data> */}
          <Table.Data>
            <Text.P1>{item.revieweeAverage}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firmAverage}</Text.P1>
          </Table.Data>
          {columns.map((row: any) => {
            return (
              <Table.Data key={row.key}>
                <Text.P1>{item[row.key]}</Text.P1>
              </Table.Data>
            );
          })}
          {/* <Table.Data>{item.selfAverage || "-"}</Table.Data> */}
          <Table.Data>
            <Sentiment sentiment={item.sentiment.toUpperCase()} />
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const getTableData = (params?: any) => {
    getDetailOverallDataFact(userId, params?.group).then((response: any) => {
      setColumns(response.columns);
      setList(response.data);
      setGroups(response.groups);
      setGroup(response.group);
    });
  };

  React.useEffect(() => {
    getTableData();
  }, []);

  if (list?.length === 0) return null;

  return (
    <Div>
      <DownloadExcel
        hideYearFilter
        surveys={{
          selected: group?.title,
          options: groups,
        }}
        onSurveySelect={(item: any) => {
          // setSurvey(item.key);
          getTableData({
            group: item.key,
          });
        }}
        title="360 Degree Average by Rater Groups"
      />
      <Table
        columns={getColumns()}
        isLoading={tableMeta.isLoading}
        rows={list}
        customRows
        render={() => getRowsTemplate()}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={tableMeta.onPageSelect}
      />
    </Div>
  );
};

// Detailed.propTypes = {}

export default Detailed;
