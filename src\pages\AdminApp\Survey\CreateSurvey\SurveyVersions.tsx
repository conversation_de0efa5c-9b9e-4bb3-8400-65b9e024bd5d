import React from "react";
import surveyCore from "unmatched/survey/creation/components";
import { deleteSurveyVersionFact } from "../survey-api";

const SurveyVersionsComponent = surveyCore.SurveyVersions;

const SurveyVersions = (props: any) => {
  const { onTabSelect, activeKey, onClone, onDelete, versions, canEdit } =
    props;

  return (
    <>
      <SurveyVersionsComponent
        onTabSelect={onTabSelect}
        activeKey={activeKey}
        onClone={onClone}
        onDelete={onDelete}
        versions={versions}
        canEdit={canEdit}
        deleteSurveyVersionFact={deleteSurveyVersionFact}
      />
    </>
  );
};

export default SurveyVersions;
