import axios, { AxiosResponse } from "axios";
import _ from "lodash";
import util from "unmatched/utils";
import api from "unmatched/utils/api";
import API_URLS from "unmatched/utils/urls/api-urls";

export const getPeopleAnalyticsFromId = (params?: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(API_URLS.PEOPLE_ANALYTICS, config)
    .then(({ data }: AxiosResponse) => {
      const { count_pages, results } = data;
      const result = results.map((item: any) => {
        return {
          overallAverage: item.overall_average,
          selfAverage: item.self_average,
          categoryAverage: item.category_average,
          frequency: item.frequency,
          target: {
            id: item.target.id,
            empId: item.target.emp_id,
            firstName: item.target.first_name,
            lastName: item.target.last_name,
            email: item.target.email,
            metadata: item.target.metadata,
          },
          index: {
            id: item.index.id,
            title: item.index.title,
          },
        };
      });
      const categories =
        !_.isEmpty(results) && !_.isEmpty(result[0].categoryAverage)
          ? Object.keys(result[0].categoryAverage)
          : [];

      return {
        data: {
          totalPages: count_pages,
          results: result,
          categories,
        },
      };
    });
};

const getUserMeta = (user: any, columns: any) => {
  let output = {};
  columns.forEach((item: any) => {
    output = {
      ...output,
      [item.key]:
        typeof user[item.key] === "object"
          ? user[item.key].value
          : user[item.key],
    };
  });
  return output;
};

const getCategoryMeta = (cat: any, columns: any) => {
  let output = {};
  columns.forEach((item: any) => {
    output = {
      ...output,
      [item.key]: cat[item.key] || "0",
    };
  });
  return output;
};

const getCategories = ({ upward, self }: any) => {
  // const { standalone, linked } = self;
  const output = [...upward, ...self].map((item: any) => ({
    key: item.id,
    label: item.name,
    categoryKey: item.ordering,
  }));
  return output;
};

export const getOverallRankingsFact = (params?: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: 10,
      search: params.search,
      ordering: params.sort,
      f: params.isDownload ? "xlsx" : undefined,
    },
    meta
  );

  if (params.isDownload) {
    config["responseType"] = "arraybuffer";
  }

  return axios
    .post(
      `/analytics/overall-ranking/`,
      util.lib.omitBy(
        {
          index_ids: [...(params?.surveys || []), ...(params?.groups || [])],
          year: params.year[0],
          resource_type: params.type,
          // rater_groups: ,
        },
        (item: any) => !item
      ),
      config
    )
    .then(({ data }: AxiosResponse) => {
      if (params.isDownload) {
        // console.log(data);
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "overall-ranking-list.xlsx");
        document.body.appendChild(link);
        return link.click();
      }
      const { count_pages, results, count_items } = data;
      const columns = (meta || []).map((item: any) => ({
        key: item.field,
        label: item.display_name,
      }));
      const result = results.map((item: any) => {
        return {
          id: item.id,
          empId: item.emp_id,
          email: item.email,
          name: item.name,
          overallAverage: item.overall_avg,
          selfAverage: item.self_avg,
          frequency: item.frequency,
          ...getUserMeta(item.user_metadata, columns),
        };
      });

      return {
        data: {
          totalPages: count_pages,
          totalElements: count_items,
          results: result,
          // categories,
        },
        columns,
      };
    });
};

export const getCategoryRankingFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: 10,
      search: params.search,
      ordering: params.sort,
      f: params.isDownload ? "xlsx" : undefined,
    },
    meta
  );
  if (params.isDownload) {
    config["responseType"] = "arraybuffer";
  }

  let surveys = [];
  if (params.groups.length) {
    surveys = params.groups;
  } else if (params.surveys.length) {
    surveys = params.surveys;
  } else {
    throw new Error("No Surveys Found");
  }
  return axios
    .post(
      `/analytics/category-ranking/`,
      util.lib.omitBy(
        {
          index_id: surveys[0],
          year: params.year[0],
          resource_type: params.type,
        },
        (item: any) => !item
      ),
      config
    )
    .then(({ data }: AxiosResponse) => {
      if (params.isDownload) {
        // console.log(data);
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "category-ranking-list.xlsx");
        document.body.appendChild(link);
        link.click();
        return {
          data: {
            totalPages: data.count_pages,
            totalElements: data.count_items,
            results: [],
            categories: {},
          },
          userColumns: [],
          categoryColumns: [],
        };
      } else {
        const {
          count_pages,
          results,
          // selected_year,
          // years,
          // metadata_lables,
          categories,
          count_items,
          // selected_surveys,
          // surveys,
        } = data;
        const userColumns = meta.map((item: any) => ({
          key: item.field,
          label: item.display_name,
        }));
        const categoryColumns = getCategories(categories);
        // const mappedSurveys =
        //   surveys.map((item: any) => ({
        //     id: item.id,
        //     key: item.id,
        //     title: item.title,
        //   })) || [];
        const result = results.map((item: any) => {
          const meta = getUserMeta(item.metadata_snapshot, userColumns);
          // debugger;
          return {
            id: item.user_id,
            empId: item.emp_id,
            email: item.email,
            name: item.name,
            // overallAverage: item.overall_avg,
            // selfAverage: item.self_avg,
            // frequency: item.frequency,
            ...meta,
            ...getCategoryMeta(
              { ...item.category_average, ...item.self_avgs },
              categoryColumns
            ),
          };
        });
        return {
          data: {
            totalPages: count_pages,
            totalElements: count_items,
            results: result,
            categories,
          },
          userColumns,
          categoryColumns,
        };
      }
    });
};
