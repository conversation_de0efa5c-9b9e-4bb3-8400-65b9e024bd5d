import React, { useState } from "react";
import { useTable } from "unmatched/hooks";
// Components
import { Text, Div, Table, Dropdown } from "unmatched/components";
import {
  downloadPairFileInfoFact,
  getAllPairFileFact,
  deletePairFileFact,
} from "../pairings-api";
import { useParams } from "react-router-dom";
import { util } from "@unmatchedoffl/ui-core";
import styled from "styled-components";
import { get, isEqual, keys, map } from "lodash";
import ViewResults from "./ViewResults/ViewResults";
import icons from "assets/icons/icons";
import WIP from "assets/images/load.gif";
import useToastr from "unmatched/modules/toastr/hook";

const FilesList = ({ survey, show, canDelete }: any) => {
  const tableMeta = useTable({});
  const { id } = useParams<any>();
  const [showResultID, setShowResultID] = useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [ordering, setOrdering] = React.useState("-updated_at");
  const [pairFiles, setPairFiles] = React.useState<any>([]);
  const { DangerAlert, SuccessTick } = icons;
  const toastr = useToastr();
  const [nIntervId, setNIntervId] = useState<any>(null);
  const [filters] = React.useState<any>({});
  React.useEffect(() => {
    getPairingFiles();

    // return () => {
    //   debugger
    //   clearInterval(nIntervId);
    //   setNIntervId(null);
    // };
  }, []);

  React.useEffect(() => {
    if (
      pairFiles.some((file: any) => file?.task?.status === "QUE") &&
      !nIntervId
    ) {
      const nIntervIdL = setInterval(async () => {
        try {
          const response = await getAllPairFileFact({
            page_size: 100,
            ordering,
            index_id: survey.id,
          });
          if (!isEqual(pairFiles, response.results)) {
            getPairingFiles();
            toastr.onSucces({
              title: "Verification successfull",
              content: "Verification was successfull. Check table below, for results",
            });
          }
        } catch (err) {
          console.log(err);
        }
      }, 10000);
      setNIntervId(nIntervIdL);
    } else if (!pairFiles.some((file: any) => file?.task?.status === "QUE")) {
      clearInterval(nIntervId);
      setNIntervId(null);
    }
  }, [pairFiles]);

  React.useEffect(() => {
    if (!show) {
      getPairingFiles();
    }
  }, [show]);


  const getPairingFiles = async (params?: any) => {
    setIsLoading(true);
    try {
      const response = await getAllPairFileFact({
        page_size: 100,
        ordering,
        ...params,
        index_id: survey.id,
      });
      setPairFiles(response.results);
      // setUserData(userResponse.data);
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      console.log(err);
    }
  };

  const onPairFileDelete = async (file_id: string) => {
    try {
      await deletePairFileFact(file_id);
      toastr.onSucces({
        title: "Success",
        content: "Pair File Deleted Successfully",
      });
      getPairingFiles();
    } catch (error: any) {
      toastr.onError(error);
      // console.log(error);
    }
  };

  const onPageSelect = async (page: number) => {
    // setFilters((_filters: any) => ({
    //   ..._filters,
    //   page,
    // }));
    try {
      setIsLoading(true);
      const obj = {};
      Object.keys(filters).map((key: any) =>
        Object.assign(obj, { [key]: filters[key].selected.join(",") })
      );
      const response = await getAllPairFileFact({
        file_id: id,
        page_size: 10,
        page,
        ...obj,
        ...(ordering && { ordering }),
      });
      setPairFiles(response.results);
      tableMeta.updatePagination({
        totalPages: response.totalPages,
        page,
      });
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  const getColumns = () => {
    return [
      { key: 1, label: "No.", hasSort: false },

      {
        key: 3,
        label: "Title",
        hasSort: false,
        sortKey: "title",
        sortValue: "",
      },
      {
        key: 4,
        label: "Total Records",
        hasSort: false,
        sortKey: "records",
        sortValue: "",
      },
      {
        key: 4,
        label: "Rejected",
        hasSort: false,
        sortKey: "rejected",
        sortValue: "",
      },
      { key: 5, label: "Tags", hasSort: false },
      {
        key: 6,
        label: "Uploaded On",
        hasSort: false,
      },
      {
        key: 7,
        label: "Last Updated On",
        hasSort: false,
        sortKey: "updated_at",
        sortValue: "dsc",
      },
      { key: 2, label: "Upload Results", hasSort: false },
      { key: 8, label: "Action", hasSort: false },
    ];
  };

  const [columnsData, setColumnsData] = React.useState<any>(getColumns()); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL

  const getCompColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const hasRejectedRecords = (item: any) => {
    const file = pairFiles.filter((pf: any) => pf.id === item.id)[0];
    if (file) {
      const d = file.task?.result;
      return d?.success && d?.error_log_file;
    }
    return false;
  };

  const onDownloadRejectedRecords = (id: string) => {
    downloadPairFileInfoFact(id, "ERROR", () =>
      toastr.errorToast("No Rejected Records Found")
    );
  };

  const getRows = () => {
    return pairFiles.map((item: any, index: number) => {
      // const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          even={!isEven}
          key={index}
        >
          <Table.Data width="30px">
            <Text.P1>{index + 1}.</Text.P1>
          </Table.Data>

          <Table.Data width="20%">
            <Text.P1>
              <Div className="w-100 d-block">
                {item.title ?? <i className="text-muted">No Name</i>}{" "}
              </Div>
              <ViewResults
                size="lg"
                onHide={() => {
                  setShowResultID(null);
                }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                dialogClassName="modal-90w"
                backdrop="static"
                show={!!showResultID && showResultID === item.id}
                selected={item}
                onDownloadRejectedRecords={onDownloadRejectedRecords}
                hasRejectedRecords={hasRejectedRecords(item)}
              />
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.records}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {(() => {
                const rejected = item.task?.result?.rejected;
                return rejected
                  ? Object.values(rejected).reduce((a: any, b: any) => a + b) ||
                      0
                  : 0;
              })()}
            </Text.P1>
          </Table.Data>

          <Table.Data>
            <Text.P1>
              {item.tags.length > 0 ? (
                item.tags.join(", ")
              ) : (
                <span className="text-muted">-</span>
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.uploaded_on,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.updated_at,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data style={{ textAlign: "center" }}>
            {item.task?.status === "QUE" || !item.task ? (
              <img className="ml-2 pl-1" width={15} src={WIP} />
            ) : (
              <span
                onClick={(e) => {
                  e.stopPropagation();
                  setShowResultID(item.id);
                }}
                className="ml-2 pl-1 cursor-pointer"
              >
                {hasRejectedRecords(item) ? (
                  <DangerAlert width={15} className="fs-15" />
                ) : (
                  <SuccessTick width="15px" className="fs-15" />
                )}
              </span>
            )}
          </Table.Data>
          <Table.Data>
            <Text.P1 className="text-center">
              <StyledActionsDropdown
                onClick={(e: any) => e.stopPropagation()}
                style={{ position: "absolute" }}
              >
                <OptionButton
                  className="d-flex align-items-center justify-content-center btn-block no-shadow"
                  id={"dropdown-button-drop-start"}
                  style={{ height: "100%" }}
                >
                  ...
                </OptionButton>

                <Dropdown.Menu
                  align="right"
                  className="text-right shadow-lg"
                  style={{ zIndex: 1 }}
                >
                  <Dropdown.Item
                    href="#"
                    onClick={() => {
                      downloadPairFileInfoFact(item.id, "ERROR", () =>
                        toastr.errorToast("No Rejected Records Found")
                      );
                    }}
                  >
                    Rejected Records
                  </Dropdown.Item>
                  <Dropdown.Item
                    href="#"
                    onClick={() => {
                      downloadPairFileInfoFact(item.id, "DOWNLOAD", () => "");
                    }}
                  >
                    Download
                  </Dropdown.Item>
                  {canDelete && <Dropdown.Item
                    className="text-danger"
                    href="#"
                    onClick={() => onPairFileDelete(item.id)}
                  >
                    Delete
                  </Dropdown.Item>}
                </Dropdown.Menu>
              </StyledActionsDropdown>
            </Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  return (
    <>
      {pairFiles.length > 0 && <Table
        columns={getCompColumns()}
        isLoading={isLoading}
        rows={pairFiles}
        customRows
        render={() => getRows()}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={(d: any) => {
          tableMeta.onPageSelect(d);
          onPageSelect(d);
        }}
        onSort={(item: any) => {
          const label = util.label.getSortingLabel(
            item.sortKey,
            item.sortValue
          );
          setColumnsData((_columns: any) => {
            return Object.values(tableMeta.resetColumns(_columns, item));
          });
          setOrdering(label);
          getPairingFiles({ ordering: label });
        }}
        {...(filters.search && {
          notFoundMsg: util.noSearchRecordsFoundMsg,
        })}
      />}
    </>
  );
};

export default FilesList;

const StyledActionsDropdown = styled(Dropdown)`
  position: absolute;
  width: 60px;
  height: 38px;
  display: flex;
  margin-left: -8px;
  margin-top: -10px;
`;
export const OptionButton = styled(Dropdown.Toggle)`
  height: 10px;
  background: none !important;
  color: #c4c4c4 !important;
  padding: 0;
  border: none;
  position: relative;
  z-index: 0;
  &:focus,
  &:hover,
  &:active {
    background: none;
    color: #000 !important;
    outline: none;
    box-shadow: none !important;
  }
  box-shadow: none !important;
  border: none !important;
`;
