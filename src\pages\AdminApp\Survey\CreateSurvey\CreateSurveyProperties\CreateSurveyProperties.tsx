import React from "react";
import util from "unmatched/utils";
import { useCreateSurveyContext } from "../Provider";
import CreateExitProperties from "./CreateExitProperties/CreateExitProperties";
import CreateUpwardReview from "./CreateUpwardReview/CreateUpwardReview";

const withSurveyData = (props: any) => {
  const { survey, updateValidators, updateBuilderStatus } =
    useCreateSurveyContext();
  const surveyId = survey.data?.id;
  const isExitSurvey =
    survey?.data && survey.data.type === util.enums.Survey.Exit;

  return isExitSurvey ? (
    <CreateExitProperties
      surveyId={surveyId}
      survey={survey}
      updateValidators={updateValidators}
      updateBuilderStatus={updateBuilderStatus}
    />
  ) : (
    <CreateUpwardReview
      survey={survey}
      surveyId={surveyId}
      updateValidators={updateValidators}
      updateBuilderStatus={updateBuilderStatus}
      breadcrumbs={props.breadcrumbs}
    />
  );
};

export default withSurveyData;
