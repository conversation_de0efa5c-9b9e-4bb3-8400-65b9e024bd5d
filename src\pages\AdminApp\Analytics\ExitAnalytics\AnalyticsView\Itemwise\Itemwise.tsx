import React from "react";
import {
  Div,
  Layout,
  Text,
  // FormControl,
  ComboBasicFilter,
  ComboFilter,
  Table,
  Button,
  Icon,
  Dropdown,
  Sentiment,
} from "unmatched/components";
import { useTable } from "unmatched/hooks";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
import styled from "styled-components";
import util from "unmatched/utils";
import _ from "lodash";

// import Offcanvas from 'react-bootstrap/Offcanvas'
// import PropTypes from 'prop-types';

const FixedContent: any = styled.div`
  position: fixed;
  background: #fff;
  z-index: 1;
  width: 100%;
  left: 60px;
  padding-right: 106px;
  border-bottom: 1px solid rgb(222, 226, 230);
`;

// const getFilterData = (_filters: any, _demographics: any) => {
//   let demographicFilters = {};
//   _demographics.forEach((item: any) => {
//     demographicFilters = {
//       ...demographicFilters,
//       [`demographics__${item.key}`]: {
//         label: item.title,
//         values: item.options,
//       },
//     };
//   });
//   return {
//     ..._filters,
//     ...demographicFilters,
//   };
// };

const Itemwise = (props: {
  user: any;
  comments: any;
  getComments: Function;
  currentQuestionDetails: any;
  sectionAndQuestion: any;
  setCurrentQuestion: Function;
  filtersState: any;
  onFilterUpdate: Function;
  currentQuestion: any;
  // demographicSchema?: any;
  isLoading: boolean;
  commentFilters: any;
  setCommentFilter: Function;
  flagComment: Function;
}) => {
  const {
    getComments,
    comments,
    currentQuestionDetails,
    sectionAndQuestion,
    setCurrentQuestion,
    filtersState,
    onFilterUpdate,
    currentQuestion,
    // demographicSchema,
    isLoading,
    commentFilters,
    setCommentFilter,
    flagComment,
  } = props;
  const tableMeta = useTable({});
  // const { user } = props;
  // const [positionY, setPositionY] = React.useState<any>(0);

  // const getScrollPosition = () => {
  //   setPositionY(window.scrollY);
  // };

  // React.useEffect(() => {
  //   window.addEventListener("scroll", getScrollPosition);
  //   return () => {
  //     window.removeEventListener("scroll", getScrollPosition);
  //   };
  // }, []);

  const comboFiltersData = filtersState.filters;

  const [filters, setFilters] = React.useState({
    page: 1,
    totalPages: 0,
    section: "",
  });

  const [showDropdown, setShowDropdown] = React.useState(false);

  React.useEffect(() => {
    setFilters({
      ...filters,
      totalPages: comments?.count_pages,
      section:
        filters.section.length === 0
          ? sectionAndQuestion.length > 0
            ? sectionAndQuestion[0].key
            : ""
          : filters.section,
    });
    // eslint-disable-next-line
  }, [comments]);

  const onPageSelect = (page: number) => {
    setFilters({ ...filters, page });
    getComments(page);
  };

  const arr =
    currentQuestionDetails.length > 0
      ? Object.keys(currentQuestionDetails[0]?.distribution || {}).map(
          (key: any) => ({
            key,
            value: currentQuestionDetails[0].distribution[key],
          })
        )
      : [];

  const getSectionSelected = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return "";
    }
    return (
      sectionAndQuestion.filter((sec: any) => sec.key === cohert)[0]?.title ??
      ""
    );
  };

  const qtFilters = {
    showUnflagged: false,
    cohart: {
      options: sectionAndQuestion,
      selected: getSectionSelected(filters.section),
    },
    applied: {
      options: [],
      selected: "",
    },
  };

  const sentiments = [
    {
      title: "Positive",
      id: 1,
      value: "POSITIVE",
    },
    {
      title: "Negative",
      id: 2,
      value: "NEGATIVE",
    },
    {
      title: "Neutral",
      id: 3,
      value: "NEUTRAL",
    },
    {
      title: "Mixed",
      id: 4,
      value: "MIXED",
    },
    {
      title: "All",
      id: 5,
      value: null,
    },
  ];
  const flags = [
    {
      title: "Flagged",
      id: 1,
      value: true,
    },
    {
      title: "Unflagged",
      id: 2,
      value: false,
    },
    {
      title: "All",
      id: 5,
      value: null,
    },
  ];

  function Navigation(): {
    isNext: boolean;
    isPrev: boolean;
    onNext: Function;
    onPrev: Function;
  } {
    const currentSection =
      sectionAndQuestion.filter(
        (sec: any) => sec.key === currentQuestion.section
      ) ?? [];
    const currentQuestionIndex = currentSection[0]?.questions?.findIndex(
      (sec: any) => sec.key === currentQuestion.id
    );

    const isNext =
      currentQuestionIndex < currentSection[0]?.questions.length - 1;
    const isPrev = currentQuestionIndex > 0;

    const onNext = () => {
      if (!isNext) return;
      setCurrentQuestion((_old: any) => {
        return {
          id: currentSection[0].questions[currentQuestionIndex + 1].key,
          name: currentSection[0].questions[currentQuestionIndex + 1].title,
          section: _old.section,
        };
      });
    };
    const onPrev = () => {
      if (!isPrev) return;
      setCurrentQuestion((_old: any) => {
        return {
          id: currentSection[0].questions[currentQuestionIndex - 1].key,
          name: currentSection[0].questions[currentQuestionIndex - 1].title,
          section: _old.section,
        };
      });
    };

    return {
      isNext: isNext,
      isPrev: isPrev,
      onNext,
      onPrev,
    };
  }

  const getFirstQuestion = (id: number | string) => {
    const currentSection =
      sectionAndQuestion.filter((sec: any) => sec.key === id) ?? [];
    // return currentSection[0]?.questions[0] ?? {};
    setCurrentQuestion({
      id: currentSection[0].questions[0].key,
      name: currentSection[0].questions[0].title,
      section: id,
    });
  };
  const getFiltersTemplate = () => {
    const getSectionQuestions = (_id: string) => {
      if (_id === undefined || _id === null) {
        return [];
      }
      return sectionAndQuestion.filter((sec: any) => sec.key === _id) ?? [];
    };
    return (
      <Div className="pt-4 pl-5">
        <Div className="row" style={{ maxWidth: 360 }}>
          <Div className="col pr-0" style={{ maxWidth: 130 }}>
            <Text.P2 className="my-1 mr-1 text-left">Choose Section</Text.P2>
          </Div>
          <Div className="col p-0">
            <ComboBasicFilter
              cohart={qtFilters.cohart}
              applied={qtFilters.applied}
              onCohartUpdate={(e: string) => {
                getFirstQuestion(e);
                setFilters({ ...filters, section: e });
              }}
              onAppliedUpdate={() => ""}
              isAppliedShown={false}
              hideIcon
            />
          </Div>
        </Div>
        <Div
          className="border rounded-sm my-2 row mx-0"
          style={{ background: "#FCFCFC" }}
        >
          <Div className="pr-0 col" style={{ maxWidth: "calc(100% - 177px)" }}>
            <Div className="d-flex m-0 position-relative">
              <Div
                className="p-0"
                style={{
                  maxWidth: "calc(100% - 30px)",
                  minWidth: "calc(100% - 30px)",
                }}
              >
                
                <Text.P1
                  className="text-truncate flex-grow-1 fs-14  text-left"
                  onClick={() => setShowDropdown(!showDropdown)}
                  style={{ marginTop: "0.7rem" }}
                >
                  Q: <span className="font-weight-bold">{currentQuestion.name}</span>
                </Text.P1>
              </Div>
              <Div className="p-0" style={{ maxWidth: 30 }}>
                <Button
                  onClick={() => setShowDropdown(!showDropdown)}
                  variant="link"
                  className="pb-0 px-0 border-0 rounded-0"
                  style={{ marginTop: "0.3rem" }}
                >
                  <Icon icon="far fa-chevron-down" />
                </Button>
              </Div>
              <Dropdown style={{ position: "static" }} show={showDropdown}>
                <Dropdown.Toggle
                  variant="link"
                  className="px-0 py-0 mt-4"
                  id="dropdown-basic"
                  style={{ opacity: 0 }}
                ></Dropdown.Toggle>

                <Dropdown.Menu className="shadow w-100 mt-2 py-0" align="right">
                  {getSectionQuestions(filters.section).map((item: any) => {
                    return item.questions.map(
                      (question: any, index: number) => {
                        return (
                          <Dropdown.Item
                            key={index}
                            className={`py-2 fs-12 ${
                              item.questions?.length === index + 1
                                ? ""
                                : "border-bottom"
                            } `}
                            // href="#/"
                            as={Button}
                            onClick={() => {
                              setCurrentQuestion({
                                id: question.key,
                                name: question.title,
                                section: filters.section,
                              });
                              setShowDropdown(false);
                            }}
                          >
                            {question.title}
                          </Dropdown.Item>
                        );
                      }
                    );
                  })}
                </Dropdown.Menu>
              </Dropdown>
            </Div>
          </Div>
          <Div
            style={{ maxWidth: 177, minWidth: 177 }}
            className="text-right px-0 col"
          >
            <Button
              variant="outline-primary"
              className="font-weight-normal fs-12 rounded-0 border-left border-top-0 border-bottom-0 border-right-0"
              disabled={!Navigation().isPrev}
              style={{ padding: ".7rem 1rem" }}
              onClick={() => Navigation().onPrev()}
            >
              <Icon icon="far fa-chevron-left mr-2" /> Previous
            </Button>
            <Button
              variant="outline-primary"
              className="font-weight-normal fs-12 rounded-0 border-left border-top-0 border-bottom-0 border-right-0"
              disabled={!Navigation().isNext}
              style={{ padding: ".7rem 1rem" }}
              onClick={() => Navigation().onNext()}
            >
              Next <Icon icon="far fa-chevron-right ml-2" />
            </Button>
          </Div>
        </Div>
      </Div>
    );
  };


  const [commentsColumnsData, setCommentsColumnsData] = React.useState<any>({
    sNo: { label: "No." },
    // title: { label: "Title", hasSort: true, sortValue: "asc" },
    // startDate: { label: "Start Date", hasSort: true, sortValue: "dsc" },
    // endDate: { label: "End Date", hasSort: true, sortValue: "dsc" },
    // activeUsers: { label: "Total Eligible", hasSort: true, sortValue: "" },
    // inactiveUsers: { label: "Total Reports", hasSort: true, sortValue: "" },

    comments: {
      label: "Comment",
      hasSort: true,
    },
    flags: {
      label: <Icon icon="far fa-flag" />,
      hasSort: false,
    },
    sentiment: {
      label: (
        <Div className="position-relative">
          <OverlayTrigger
            placement="bottom"
            overlay={
              <ModTooltip id="button-tooltip">
                <Div className="row">
                  <Div className="col pr-0 pt-2" style={{ maxWidth: 10 }}>
                    <Icon icon="fal fa-info-circle fs-12" />
                  </Div>
                  <Div className="col">
                    <Text.P1 className="text-left fs-10 fw-300 p-2">
                      The sentiments are computed generated using advanced
                      machine learning algorithms, and may not be 100% accurate
                      all the time. Please use your own judgment while reviewing
                      the comments.
                    </Text.P1>
                  </Div>
                </Div>
              </ModTooltip>
            }
          >
            {({ ref, ...triggerHandler }) => (
              <>
                <span className="mr-1">Sentiment</span>
                <i
                  className="far fa-info-circle"
                  ref={ref}
                  {...triggerHandler}
                />
              </>
            )}
          </OverlayTrigger>
        </Div>
      ),
      hasSort: true,
      sortValue: "",
    },
  });

  const getCommentsColumns = () => {
    const coumnsList = _.keys(commentsColumnsData);
    return _.map(coumnsList, (key: string) => ({
      ..._.get(commentsColumnsData, key),
      key,
    }));
  };
  // const getCommentsColumnsData = () => {
  //   return [
  //     { key: 2, label: "No.", hasSort: false },
  //     {
  //       key: 3,
  //       label: "Comment",
  //       hasSort: true,
  //     },
  //     {
  //       key: 5,
  //       label: <Icon icon="far fa-flag" />,
  //       hasSort: false,
  //     },
  //     {
  //       key: 4,
  //       label: (
  //         <Div className="position-relative">
  //           <OverlayTrigger
  //             placement="bottom"
  //             overlay={
  //               <ModTooltip id="button-tooltip">
  //                 <Div className="row">
  //                   <Div className="col pr-0 pt-2" style={{ maxWidth: 10 }}>
  //                     <Icon icon="fal fa-info-circle fs-12" />
  //                   </Div>
  //                   <Div className="col">
  //                     <Text.P1 className="text-left fs-10 fw-300 p-2">
  //                       The sentiments are computed generated using advanced
  //                       machine learning algorithms, and may not be 100%
  //                       accurate all the time. Please use your own judgment
  //                       while reviewing the comments.
  //                     </Text.P1>
  //                   </Div>
  //                 </Div>
  //               </ModTooltip>
  //             }
  //           >
  //             {({ ref, ...triggerHandler }) => (
  //               <>
  //                 <span className="mr-1">Sentiment</span>
  //                 <i
  //                   className="far fa-info-circle"
  //                   ref={ref}
  //                   {...triggerHandler}
  //                 />
  //               </>
  //             )}
  //           </OverlayTrigger>
  //         </Div>
  //       ),
  //       hasSort: true,
  //       sortValue: "",
  //     },
  //   ];
  // };

  const getCommentsRowsTemplate = () => {
    return comments?.results.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.key}>
          <Table.Data width="70px">
            <Text.P1>{item.serial_no}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.answer.split("\n").map((item: string, i: number) => {
                if (item.trim() === "") {
                  return <br key={i} />;
                }
                return (
                  <span key={i}>
                    {item}
                    <br />
                  </span>
                );
              })}
            </Text.P1>
          </Table.Data>
          <Table.Data width="30px">
            <Text.P1>
              <Button
                variant="link"
                className="text-dark p-0 fs-12"
                onClick={() => flagComment(item.id, !item.flag)}
              >
                <Icon icon={`${item.flag ? "fas" : "far"} fa-flag`} />
              </Button>
            </Text.P1>
          </Table.Data>
          <Table.Data width="150px">
            <Sentiment sentiment={item.sentiment} />
          </Table.Data>
        </Table.Row>
      );
    });
  };

  return (
    <Layout.Container fluid className="pt-3">
      <FixedContent className="text-right">
        <Div>{getFiltersTemplate()}</Div>
      </FixedContent>
      <hr className="my-3" />

      <Text.H2 className="my-3"></Text.H2>
      <Div className="row mt-5 mb-3">
        <Div className="col-md-8 pt-5">
          <Text.P2 className="my-2">Filter by groups</Text.P2>
          <ComboFilter
            filters={comboFiltersData}
            selected={filtersState.selected}
            onFilterSelect={(_selected: any) => {
              filtersState.onSelect(_selected);
              onFilterUpdate(_selected);
            }}
            onSubmit={() => ""}
          />
        </Div>
      </Div>
      <Div className="my-3">
        <Table
          columns={[
            { key: 0, label: "Total Participants", hasSort: false },
            { key: "8T57", label: "Filtered Group", hasSort: false },
            { key: "0T55E7", label: "Participated", hasSort: false },
            { key: "7U35Y2", label: "Not Participated", hasSort: false },
            ...arr.map((obj: any, index: number) => {
              return { key: index + 1, label: obj.key, hasSort: false };
            }),
          ]}
          isLoading={isLoading}
          rows={[{}]}
          customRows
          render={() => {
            return (
              <Table.Row>
                <Table.Data>
                  <Text.P1>
                    {currentQuestionDetails[0]
                      ? currentQuestionDetails[0].totalInviteSent
                      : 0}
                  </Text.P1>
                </Table.Data>
                <Table.Data>
                  <Text.P1>
                    {currentQuestionDetails[0]
                      ? currentQuestionDetails[0].totalGroups
                      : 0}
                  </Text.P1>
                </Table.Data>
                <Table.Data>
                  <Text.P1>
                    {currentQuestionDetails[0]
                      ? currentQuestionDetails[0].participated
                      : 0}
                  </Text.P1>
                </Table.Data>
                <Table.Data>
                  <Text.P1>
                    {currentQuestionDetails[0]
                      ? currentQuestionDetails[0].notParticipated
                      : 0}
                  </Text.P1>
                </Table.Data>
                {arr.map((obj: any) => (
                  <Table.Data width="220px">
                    <Text.P1>
                      {currentQuestionDetails[0]
                        ? currentQuestionDetails[0].distribution[obj.key]
                        : 0}
                    </Text.P1>
                  </Table.Data>
                ))}
              </Table.Row>
            );
          }}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>

      {!isLoading && currentQuestionDetails.length === 0 ? (
        <Text.P1>
          <span className="text-danger">*</span> Results redacted as the filter
          is too narrow and might be identifying a person or a group.
        </Text.P1>
      ) : (
        <></>
      )}

      <Div className="my-4">
        <Div className="col-lg-8 col-md-10 col-12">
          {arr.map((obj: any) => (
            <Div className="row my-3">
              <Div className="col pr-0" style={{ maxWidth: 170 }}>
                <Text.P1 className="text-right">{obj.key}</Text.P1>
              </Div>
              <Div className="col-7">
                <Div className="d-flex ">
                  <Div
                    className="py-1 bg-success"
                    style={{
                      marginTop: "0.4rem",
                      width: `${
                        (currentQuestionDetails[0].distribution[obj.key] /
                          currentQuestionDetails[0].totalGroups) *
                        100
                      }%`,
                    }}
                  />
                  <Div className="flex-grow-1 position-relative">
                    <Text.P1
                      className="position-absolute text-success"
                      style={{ left: 10 }}
                    >
                      {(
                        (currentQuestionDetails[0].distribution[obj.key] /
                          currentQuestionDetails[0].totalGroups) *
                        100
                      ).toFixed(2)}
                      %
                    </Text.P1>
                  </Div>
                </Div>
              </Div>
            </Div>
          ))}
        </Div>
      </Div>
      {comments?.results?.length > 0 && <Div className="mt-5">
        {/* {!isLoading && comments?.results?.length > 0 && ( */}
        <>
          <Text.H3 className="my-3">Comments</Text.H3>
          <Div className="row my-3">
            <Div className="col d-flex justify-content-end align-items-center">
              <Icon icon="far fa-filter fs-12 mx-2" />
              <Div className="d-flex" style={{ maxWidth: 360, minWidth: 360 }}>
                <Div className="w-100 ml-2">
                  <Table.CompareFilter
                    title="Sentiment"
                    selected={
                      commentFilters?.sentiment
                        ?.split(" ")
                        .map(
                          (w: any) =>
                            w[0].toUpperCase() + w.substring(1).toLowerCase()
                        )
                        .join(" ") || ""
                    }
                    options={sentiments}
                    placeholder={"All"}
                    onSelect={(item: any) =>
                      setCommentFilter((_d: any) => {
                        return { ..._d, sentiment: item.value };
                      })
                    }
                  />
                </Div>

                <Div className="w-100 ml-2">
                  <Table.CompareFilter
                    title={<Icon icon="far fa-flag fs-12 mx-2" />}
                    selected={
                      commentFilters.flag
                        ? "Flagged"
                        : commentFilters.flag === false
                        ? "Unflagged"
                        : ""
                    }
                    options={flags}
                    placeholder={"All"}
                    onSelect={(item: any) =>
                      // setCommentFilter((_d: any) => {
                      //   return { ..._d, sentiment: item.value };
                      // });
                      setCommentFilter((_d: any) => {
                        return { ..._d, flag: item.value };
                      })
                    }
                  />
                </Div>
              </Div>
            </Div>
            {/* <Div className="col d-flex justify-content-end">
              <FormControl.Checkbox>
                <FormControl.Checkbox.Label>
                  Show flagged comments only
                </FormControl.Checkbox.Label>
                <FormControl.Checkbox.Input
                  checked={commentFilters.flag || false}
                  onChange={(e: any) =>
                    setCommentFilter((_d: any) => {
                      return { ..._d, flag: e.target.checked };
                    })
                  }
                />
              </FormControl.Checkbox>
            </Div> */}
          </Div>
          <Table
            columns={getCommentsColumns()}
            isLoading={isLoading}
            rows={comments?.results}
            customRows
            render={() => getCommentsRowsTemplate()}
            hasPagination
            activePage={filters.page}
            pages={filters.totalPages}
            onPageSelect={onPageSelect}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.key,
                item.sortValue
              );
              setCommentsColumnsData((_columns: any) =>
                tableMeta.resetColumns(_columns, item)
              );
              setCommentFilter((_d: any) => {
                return { ..._d, ordering: label };
              });
            }}
          />{" "}
        </>
        {/* )} */}
      </Div>}
    </Layout.Container>
  );
};

Itemwise.propTypes = {};

export default Itemwise;
const ModTooltip = styled(Tooltip)`
  .tooltip-inner {
    max-width: 400px;
  }
`;
