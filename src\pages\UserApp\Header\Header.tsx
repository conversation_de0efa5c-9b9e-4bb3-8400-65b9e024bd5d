import React from "react";
import { Link, NavLink, useHistory } from "react-router-dom";
// import styled from "styled-components";
import {
  Navbar,
  Image,
  Layout,
  Text,
  Dropdown,
  Div,
} from "unmatched/components";
import useSession from "unmatched/modules/session/hook";
import util from "unmatched/utils";
import headerData from "../user-app-meta";
import appUrls from "unmatched/utils/urls/app-urls";
import { useWindowSize } from "react-use";

const Header = (props: any) => {
  const history = useHistory();
  const { user, client } = useSession();
  const media = useWindowSize();

  const isAdmin = util.canAccess(
    user.role || "",
    util.enums.Modules.Administration,
    "canAccess"
  );
  const { togglePasswordModal } = props;

  const name = user && user.firstName ? user.firstName[0] : "U";

  const getOptionsTemplate = () => {
    if (props.hideOptions) return null;
    return (
      <Layout.Col
        xl={2}
        lg={2}
        md={2}
        sm={2}
        xs={2}
        className="text-right pr-0 align-items-center py-0 d-flex flex-row-reverse"
      >
        {/* <Button onClick={onLogout}>{name}</Button> */}
        <Dropdown>
          <Dropdown.Toggle
            variant="primary"
            size="sm"
            style={{ width: 28, height: 28 }}
          >
            {name}
          </Dropdown.Toggle>
          <Dropdown.Menu style={{ width: 200 }} align="right" className="mt-2">
            <Div className="font-weight-bold my-1 pl-md-3 text-break pr-3">
              <Text.H3>{`${user?.firstName} ${user?.lastName}`}</Text.H3>
            </Div>
            <Div className="fs-12 pl-md-3 pr-3 text-break text-muted text-truncate">
              {user?.email}
            </Div>
            <hr className="my-2" />
            {isAdmin && (
              <Link
                className={util.getUtilClassName({ textColor: "dark" })}
                to={util.appUrls.admin.default}
              >
                <Dropdown.Item className="fs-14 py-1 px-3" as={Div}>
                  Switch to Admin
                </Dropdown.Item>
              </Link>
            )}
            <a href="/">
              <Dropdown.Item className="fs-14 py-1 px-3" as={Div}>
                Go to Launchpad
              </Dropdown.Item>
            </a>
            <Dropdown.Item
              onClick={() => togglePasswordModal(true)}
              className="cursor-pointer fs-14 py-1 px-3"
              as={Div}
            >
              {user.isPasswordSet ? "Change Password" : "Set Password"}
            </Dropdown.Item>
            <Link
              to={util.appUrls.logout}
              className={util.getUtilClassName({ textColor: "danger" })}
            >
              <Dropdown.Item className="fs-14 py-1 px-3" as={Div}>
                Logout
              </Dropdown.Item>
            </Link>
          </Dropdown.Menu>
        </Dropdown>
      </Layout.Col>
    );
  };

  return (
    <Navbar
      fixed="top"
      collapseOnSelect
      expand="lg"
      variant="dark"
      className="py-0 bg-user-dark"
      style={{ height: 46 }}
    >
      <Layout.Row className="w-100 h-100">
        <Layout.Col
          xl={2}
          lg={2}
          md={2}
          sm={3}
          xs={3}
          className="align-self-center py-1"
        >
          <Link to={appUrls.user.default}>
            <Image src={client.darkLogo} width="100%" height={"20px"} lazy />
          </Link>
        </Layout.Col>
        <Layout.Col
          xl={8}
          lg={8}
          md={8}
          sm={7}
          xs={7}
          className="align-self-center py-0 px-0"
          style={{ height: 40 }}
        >
          <Layout.Flex className="w-100 h-100 justify-content-center">
            {headerData.map((item: any) => {
              const isActive = history.location.pathname.includes(
                item.getRoute()
              );
              return (
                <Text.P1
                  className={
                    isActive
                      ? "border-bottom border-primary pt-2 border-botom-3"
                      : "pt-2"
                  }
                  key={item.id}
                >
                  <NavLink
                    className={util.getUtilClassName({
                      textColor: "white",
                      px: 4,
                    })}
                    to={item.getRoute()}
                  >
                    {media.width >= 596 ? item.title : item.icon}
                  </NavLink>
                </Text.P1>
              );
            })}
          </Layout.Flex>
        </Layout.Col>
        {getOptionsTemplate()}
      </Layout.Row>
    </Navbar>
  );
};

export default Header;
