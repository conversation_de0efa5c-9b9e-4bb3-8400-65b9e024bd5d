import appUrls from "unmatched/utils/urls/app-urls";
import Pairings from "./Pairings/Pairings";
import TakeSurvey from "./TakeSurvey/TakeSurvey";

const routes = [
  {
    name: "Take Survey",
    path: appUrls.user.dashboard.upwardReview.getTakeSurveyUrl(
      ":id",
      ":surveyId"
    ),
    isExact: false,
    isPrivate: true,
    component: TakeSurvey,
  },
  {
    name: "Pairings",
    path: appUrls.user.dashboard.upwardReview.getPairingsUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: Pairings,
  },
];

export default routes;
