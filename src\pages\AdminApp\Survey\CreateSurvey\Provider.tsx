import _ from "lodash";
import React from "react";
import { useXHR } from "unmatched/hooks";
import util from "unmatched/utils";
import appUrls from "unmatched/utils/urls/app-urls";
import { patchSurveyBuilderStatusFact } from "../survey-api";

const LINKS = {
  SURVEY_PROPERTIES: "surveyProperties",
  // ADD_USERS: "addUsers",
  RULES: "rules",
  EDIT_SURVEY: "editSurvey",
  ADD_PAIRINGS: "addPairings",
  SURVEY_PREVIEW: "preview",
  SEND_SURVEY: "publish",
  FAQ: "faq"
};

const defaultLinksList = _.values(LINKS);
const engagementLinks = defaultLinksList.filter((el) => el !== "rules" && el !== "addPairings");
engagementLinks.splice(2, 0, 'addPairingsCustomTitle');



const linksList = {
  [util.enums.Survey.Upward]: defaultLinksList,
  [util.enums.Survey.Engagement]: engagementLinks,
  [util.enums.Survey.Self]: engagementLinks,
  [util.enums.Survey._360Degree]: defaultLinksList,
  [util.enums.Survey.Exit]: [
    LINKS.SURVEY_PROPERTIES,
    LINKS.EDIT_SURVEY,
    "addPairingsCustomTitle",
    LINKS.SURVEY_PREVIEW,
    LINKS.SEND_SURVEY,
    LINKS.FAQ
  ],
};

const sidebarData = {
  list: linksList,
  content: {
    surveyProperties: {
      title: "Properties",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getUpwardReviewUrl,
    },
    // addUsers: {
    //   title: "Participants",
    //   active: false,
    //   completed: false,
    //   disabled: false,
    //   getRoute: appUrls.admin.survey.create.getUsersUrl,
    // },
    rules: {
      title: "Participants",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getRulesUrl,
    },
    addPairings: {
      title: "Pairings",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getParticipantsUrl,
    },
    addPairingsCustomTitle: {
      title: "Participants",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getParticipantsUrl,
    },
    editSurvey: {
      title: "Questionnaire(s)",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getQuestionsUrl,
    },
    preview: {
      title: "Preview",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getPreviewUrl,
    },
    publish: {
      title: "Email Invites",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getPublishUrl,
    },
    faq: {
      title: "FAQs",
      active: false,
      completed: false,
      disabled: false,
      getRoute: appUrls.admin.survey.create.getFAQUrl,
    },
  },
};

const sampleSurvey = {
  id: 0,
  name: "",
  type: util.enums.Survey.Upward,
  description: "",
  startDate: "",
  endDate: "",
  pairings: [],
  enablePairings: false,
  enableVersions: true,
  enableMinResponses: false,
  enableCustomWaviewer: false,
  enableRatingsCommentsWaveOff: false,
  enableRatingsWaveOff: false,
  hasDemographics: false,
  demographicsId: 0,
};

// const getCompleted = (sidebarState: any) => {
//   const { Properties, Send, Questionnaire, AddPairings, Preview } =
//     util.enums.SurveyStatus;
//   const { SURVEY_PROPERTIES, ADD_PAIRINGS, SURVEY_PREVIEW, EDIT_SURVEY } =
//     LINKS;
//   return util.lib.get(
//     {
//       [Properties]: [],
//       [Questionnaire]: [SURVEY_PROPERTIES],
//       [AddPairings]: [SURVEY_PROPERTIES, EDIT_SURVEY],
//       [Preview]: [SURVEY_PROPERTIES, EDIT_SURVEY, ADD_PAIRINGS],
//       [Send]: [SURVEY_PROPERTIES, EDIT_SURVEY, ADD_PAIRINGS, SURVEY_PREVIEW],
//     },
//     sidebarState
//   );
// };

const Context = React.createContext<any>({});

const Provider = (props: any) => {
  const [content, setContent] = React.useState(sidebarData.content);
  const [isDemoActive, setDemographics] = React.useState(false);
  // setCompleted
  const [completed] = React.useState<Array<any>>([]);
  const [activeVersionId, setActiveVersionId] = React.useState(0);
  const [version, setVersion] = React.useState({ id: 0 });
  const [validators, setValidators] = React.useState<Array<any>>([]);
  const [errors, setErrors] = React.useState({ preview: true });
  // const toastr = useToastr();
  // const [versions, setVersions] = React.useState<Array<any>>([]);
  // const [sections, setSections] = React.useState([]);
  // const [questions, setQuestions] = React.useState([]);
  const survey = useXHR(
    {
      defaultResponse: sampleSurvey,
    },
    "data",
    { isLoading: true }
  );

  const email = useXHR(
    {
      defaultResponse: {},
    },
    "data",
    { isLoading: true }
  );

  const versionsData = useXHR(
    {
      defaultResponse: [],
    },
    "data",
    { isLoading: true }
  );

  const questionsData = useXHR(
    {
      defaultResponse: [],
    },
    "data",
    { isLoading: true }
  );

  const sectionsData = useXHR(
    {
      defaultResponse: [],
    },
    "data",
    { isLoading: true }
  );

  React.useEffect(() => {
    setVersion(
      versionsData.data.find((item: any) => item.id === activeVersionId) || {}
    );
  }, [activeVersionId, versionsData.data]);

  // const [survey, setSurvey] = React.useState({ id: 0, });
  const [width, setWidth] = React.useState(250);

  const updateValidators = (validate: {
    key: string;
    validateForm: Function;
  }) => {
    setValidators((_validators: any) => {
      let list = [];
      const hasValidator = _validators.find(
        (item: any) => item.key === validate.key
      );
      if (hasValidator) {
        list = _validators.map((item: any) =>
          item.key === validate.key ? validate : item
        );
      } else {
        list = [..._validators, validate];
      }
      return list;
    });
  };

  const updateBuilderStatus = (state: any) => {
    patchSurveyBuilderStatusFact(survey.data, state).then(() => {
      survey.setData({
        ...survey.data,
        bulderStatus: state,
      });
      // setCompleted(getCompleted(state));
    });
    // if (survey.data.id && survey.data.builderStatus !== state) {
    // }
  };

  const getLinks = (_type: string) => {
    return linksList[_type];
  };

  const getLinkContent = (key: any) => {
    return _.get(content, key);
  };

  const setLinkContent = (key: any, item: any) => {
    setContent((_content: any) => ({
      ..._content,
      [key]: item,
    }));
  };

  const validateForms = () => {
    const promises = validators
      .filter((item: any) => item.validate)
      .map((item: any) => item.validate());
    return Promise.all(promises);
  };

  const onDeleteSection = (_sectionId: any) => {
    sectionsData.setData((_data: any) =>
      _data.filter((item: any) => item.id !== _sectionId)
    );
    questionsData.setData((_data: any) =>
      _data.filter((item: any) => item.sectionId !== _sectionId)
    );
  };

  return (
    <Context.Provider
      value={{
        isDemoActive,
        setDemographics,
        survey,
        versionsData,
        validators,
        setValidators,
        version,
        versionId: version && version.id,
        setVersion,
        sectionsData,
        questionsData,
        setActiveVersionId,
        activeVersionId,
        getLinks,
        getLinkContent,
        setLinkContent,
        width,
        setWidth,
        updateValidators,
        validateForms,
        updateBuilderStatus,
        completed,
        email,
        onDeleteSection,
        errors,
        setErrors,
        setCompleted: () => {
          // setCompleted(getCompleted(state));
        },
      }}
    >
      {props.children}
    </Context.Provider>
  );
};

export const useCreateSurveyContext = () => {
  const context = React.useContext(Context);
  return context;
};

export default Provider;
