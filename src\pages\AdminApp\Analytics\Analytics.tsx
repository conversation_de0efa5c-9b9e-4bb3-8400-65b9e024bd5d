import React from "react";
import { Route, Redirect } from "react-router-dom";
import AppRoutes from "../../AppRoutes";
import ANALYTICS_ROUTES from "./analytics-routes";
import appUrls from "unmatched/utils/urls/app-urls";
// import { Helmet } from "react-helmet";

const Analytics = () => {
  return (
    <AppRoutes routes={ANALYTICS_ROUTES}>
      {/* <Helmet><title>Analytics - Unmatched</title></Helmet> */}

      <Route exact path={appUrls.admin.analytics.default}>
        <Redirect to={appUrls.admin.analytics.statistics.default} />
      </Route>
    </AppRoutes>
  );
};
export default Analytics;
