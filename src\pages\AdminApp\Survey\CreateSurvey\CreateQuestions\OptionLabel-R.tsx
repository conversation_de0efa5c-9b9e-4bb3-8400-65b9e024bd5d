import React, { useState } from "react";
// import { useFormik } from "formik";
// import * as yup from "yup";
// import { isFieldInvalid } from "../../../utils/formik";
import {
  FormControl,
  FormGroup,
  Div,
  Text,
  util,
  OverlayTrigger,
  Tooltip,
} from "@unmatchedoffl/ui-core";
// import { useCreateSurveyContext } from "../../Provider";
// import util from "../../../utils";

const { isFieldInvalid } = util.formik;

const OptionLabel = ({
  onSubmit,
  option,
  viewOnly,
  // updateValidators,
  formik,
}: {
  text: string;
  onSubmit: Function;
  option: any;
  viewOnly?: any;
  updateValidators: Function;
  formik: any;
}) => {
  const [focus, setFocus] = useState(false);

  const { submitForm, values, handleBlur, handleChange, errors } = formik;

  const getTextClassName = () => {
    return focus || errors["option"] || !values["option"]
      ? ""
      : "border-none bg-white";
  };

  const onBlur = (e: any) => {
    setFocus(false);
    handleBlur(e);
    submitForm().then(() => {
      onSubmit(values, option);
    });
  };

  const onEnter = (e: any) => {
    const isEnter = util.preventEnterKey(e);
    if (isEnter) {
      submitForm().then(() => {
        onSubmit(values, option);
      });
    }
  };

  const input = () => {
    return (
      <FormControl.Text
        name={"option"}
        isInvalid={isFieldInvalid(formik, "option")}
        value={values["option"]}
        onBlur={onBlur}
        onKeyDown={onEnter}
        onKeyUp={util.preventEnterKey}
        className={getTextClassName()}
        onChange={handleChange}
        placeholder={!option.id ? "Add option" : "Label"}
        onFocus={() => {
          setFocus(true);
        }}
        style={{ width: 300 }}
      />
    );
  };

  return (
    <Div>
      {viewOnly ? (
        <Text.P1 className="pt-2">{values["option"]}</Text.P1>
      ) : values["option"] && values["option"].length > 45 ? (
        <OverlayTrigger
          key="bottom"
          placement="bottom"
          overlay={
            <Tooltip id="`tooltip-bottom" className="fs-10">
             {values["option"]}
            </Tooltip>
          }
        >
          {input()}
        </OverlayTrigger>
      ) : (
        input()
      )}

      <FormGroup.InValidFeedback>
        {isFieldInvalid(formik, "option") && errors["option"]}
      </FormGroup.InValidFeedback>
    </Div>
  );
};

export default OptionLabel;
