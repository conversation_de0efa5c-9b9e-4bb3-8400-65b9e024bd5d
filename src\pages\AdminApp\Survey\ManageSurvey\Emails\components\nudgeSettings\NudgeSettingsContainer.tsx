import { useEffect, useState } from "react";
import NudgeSettings, { days } from "./NudgeSettings";
import {
  getNudgeSettingsFact,
} from "pages/AdminApp/Survey/survey-api";
import { Note } from "../../components/Note";
import { Div, Text } from "@unmatchedoffl/ui-core";

const NudgeSettingsContainer = (props: any) => {
  const { survey, onFilterChange } = props;
  const [nudgeSettings, setNudgeSettings] = useState<any>([]);
  const [rawNudgeSettings, setRawNudgeSettings] = useState<any>([]);

  const nudgeInitObj = {
    index: survey.indexID,
    reminder_type: "ACTIVATION",
    frequency: null,
    time: "00:00",
    days: [],
  };

  useEffect(() => {
    getNudgeSettings();
  }, []);

  const getNudgeSettings = async () => {
    const res = await getNudgeSettingsFact({}, survey.indexID);
    setNudgeSettings(res?.results || []);
    setRawNudgeSettings(res?.results || []);
  };

  const handleChange = (type: string) => (key: any, val: any) => {
    const setting = nudgeSettings.find((el: any) => el.reminder_type === type);
    if (setting) {
      const newSetting = {
        ...setting,
        [key]: val,
        ...(key === 'frequency' && val === 'DAILY' && { days })
      };
      setNudgeSettings((settings: any) =>
        settings.map((s: any) => {
          if (s.reminder_type === type) {
            return newSetting;
          }
          return s;
        })
      );
    } else {
      const freshSetting = {
        ...nudgeInitObj,
        reminder_type: type,
        [key]: val,
        ...(key === 'frequency' && val === 'DAILY' && { days })
      };
      setNudgeSettings((settings: any) =>
        ([...settings, freshSetting])
      );
    }
  };

  return (
    <>
      <Note>
        <Text.P1 style={{ fontWeight: 500, fontSize: 14 }}>
          <b>Note :</b> You can edit the email templates{" "}
          <span
            style={{
              cursor: "pointer",
              color: "#1C69FF",
              textDecorationLine: "underline",
            }}
            onClick={() => onFilterChange("templates")}
          >
            here
          </span>
        </Text.P1>
      </Note>
      <Div className="py-2" />
      {
        ["ACTIVATION", "GENERAL", "SPECIAL"].map((type: string) => {
          return <NudgeSettings
            key={type}
            settings={
              nudgeSettings.find((el: any) => el.reminder_type === type) ||
              nudgeInitObj
            }
            type={type.toLowerCase()}
            survey={survey}
            handleChange={handleChange(type)}
            onFilterChange={onFilterChange}
            notIsNew={!!rawNudgeSettings.find((el: any) => el.reminder_type === type)}
            getNudgeSettings={getNudgeSettings}
          />
        })
      }
    </>
  );
};

export default NudgeSettingsContainer;
