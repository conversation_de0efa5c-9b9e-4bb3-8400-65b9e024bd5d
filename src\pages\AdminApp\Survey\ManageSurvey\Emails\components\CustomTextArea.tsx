import React, { useRef, useEffect } from "react";
import { Div, Layout, FormGroup, FormControl } from "unmatched/components";
import styled from "styled-components";
import { Editor, EditorState, Modifier } from "draft-js";
import "draft-js/dist/Draft.css";
import { OrderedSet } from "immutable";

const CustomTextArea = ({ insertOptions = [] }: any) => {
  const textAreaInput = useRef<HTMLTextAreaElement>(null);
  const [editorState, setEditorState] = React.useState(() =>
    EditorState.createEmpty()
  );
  const editor = React.useRef(null);

  function insertCharacter(
    textToInsert: string,
    editorState: any,
    cStyle?: string
  ) {
    const currentContent = editorState.getCurrentContent(),
      currentSelection = editorState.getSelection();
    let newContent = null;

    newContent = Modifier.insertText(
      currentContent,
      currentSelection,
      textToInsert,
      cStyle ? OrderedSet.of(cStyle) : undefined
    );

    const newEditorState = EditorState.push(
      editorState,
      newContent,
      "insert-characters"
    );
    return EditorState.forceSelection(
      newEditorState,
      newContent.getSelectionAfter()
    );
  }

  useEffect(() => {
    (HTMLTextAreaElement.prototype as any).insertAtCaret = function (
      text: any
    ) {
      text = text || "";
      if ((document as any).selection) {
        // IE
        this.focus();
        const sel = (document as any).selection.createRange();
        sel.text = text;
      } else if (this.selectionStart || this.selectionStart === 0) {
        // Others
        const startPos = this.selectionStart;
        const endPos = this.selectionEnd;
        this.value =
          this.value.substring(0, startPos) +
          text +
          this.value.substring(endPos, this.value.length);
        this.selectionStart = startPos + text.length;
        this.selectionEnd = startPos + text.length;
      } else {
        this.value += text;
      }
    };
  }, []);

  // const onOptionClick = (title: string) => () => {
  //   const el = textAreaInput?.current;
  //   if (el) {
  //     (el as any).insertAtCaret(`<span><${title}></span>`);
  //     (el as any).focus();
  //     //   el.setRangeText(
  //     //     title,
  //     //     el["selectionStart"],
  //     //     el["selectionEnd"],
  //     //     "select"
  //     //   );
  //   }
  // };

  const onOptionClickDraft = (title: string) => () => {
    // setEditorState((es: any) => insertCharacter1(`<${title}>`, es));
    const es = insertCharacter(`<${title}>`, editorState, "textblue");
    const esNew = insertCharacter(" ", es);
    setEditorState(esNew);
  };

  function focusEditor() {
    editor?.current && (editor.current as any).focus();
  }

  const styleMap = {
    textblue: {
      color: "#518cff",
    },
  };

  return (
    <>
      <InputOptsWrap className="d-flex">
        <FormGroup.Label>Insert: &nbsp;</FormGroup.Label>
        <Layout.Row className="align-items-center">
          {insertOptions.map((item: any) => {
            return (
              <CustomCol onClick={onOptionClickDraft(item.title)} key={item.id}>
                <InputOpt>{item.title}</InputOpt>
              </CustomCol>
            );
          })}
        </Layout.Row>
      </InputOptsWrap>
      <div
        style={{ border: "1px solid black", minHeight: "6em", cursor: "text" }}
        onClick={focusEditor}
      >
        <Editor
          ref={editor}
          editorState={editorState}
          onChange={setEditorState}
          placeholder="Write something!"
          customStyleMap={styleMap}
        />
      </div>
      <FormControl.Textarea ref={textAreaInput} rows={10} />
    </>
  );
};

export default CustomTextArea;

const InputOpt = styled(Div)`
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  width: 90px;
  padding: 2px 5px;
  text-align: center;
  background: #fbfbfb;
  border: 1px solid #dadada;
  border-radius: 2px;
  cursor: pointer;
`;

const CustomCol = styled(Div)`
  padding: 0px;
  margin: 0 12px;
`;

const InputOptsWrap = styled(Div)`
  background-color: #fcfcfc;
  border-bottom: transparent;
  padding: 7px;
  border: 1px solid #f0f0f1;
  border-radius: 4px;
`;
