import React from "react";
import _ from "lodash";
import appUrls from "unmatched/utils/urls/app-urls";
import useToastr from "unmatched/modules/toastr/hook";
import { useDebounce, useTable } from "unmatched/hooks";

// Components
import {
  Text,
  Layout,
  Button,
  Div,
  Table,
  FormControl,
  FormGroup,
  Placeholder,
} from "unmatched/components";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import ModifyUserDetails from "./ModifyUserDetails";
import ViewUserDetails from "./ViewUserDetails";
import AddEmployeeModal from "./AddEmployeeModal/AddEmployeeModal";
import { fetchUserFileInfoFact, getAllUserFromFileFact } from "../dataload-api";
import { useParams } from "react-router-dom";
import useFilter from "pages/CommonFilters/hook";
import iconsSvgs from "assets/icons/icons";

const { Group } = iconsSvgs;

interface User {
  key: string;
  firstName: string;
  lastName: string;
  empId: string;
  email: string;
  location?: string;
  department?: string;
  results?: Array<any>;
  metadata?: any;
}

enum ModalState {
  VIEW = "VIEW",
  MODIFY = "MODIFY",
}

// const getUsersFact = () =>
//   new Promise((resolve: Function) => {
//     window.setTimeout(() => {
//       resolve();
//     }, 400);
//   });

const ManageEmployeeData = () => {
  const { showToast } = useToastr();
  const tableMeta = useTable({});
  const { id } = useParams<any>();
  const filtersState = useFilter();

  const [metas, setMetas] = React.useState<any>([]);
  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      _.forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      arr.push({
        title: "All",
        value: ["All"],
      });
      setMetas(arr);
    });
  };
  const [selectAll, setSelectAll] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);
  const [showAddModal, setShowAddModal] = React.useState(false);
  const [fileInfo, setFileInfo] = React.useState<any>({});
  const [users, setUsers] = React.useState<any>([]);
  const [search, setSearch] = React.useState("");
  const [filters, setFilters] = React.useState<any>({});
  const [selected, setSelected] = React.useState<Array<string>>([]);
  const [selectedUser, setSelectedUser] = React.useState({
    empId: "",
    state: ModalState.VIEW,
  });

  React.useEffect(() => {
    getFilters();
    getEmployees();
    //eslint-disable-next-line
  }, []);

  const getEmployees = async () => {
    // setPairings(JSON_DATA);
    try {
      setIsLoading(true);
      const fileResponse = await fetchUserFileInfoFact(id);
      const response = await getAllUserFromFileFact({
        file_id: id,
        page_size: 10,
        search: search.length > 0 ? search : undefined,
      });
      setFileInfo(fileResponse.data);
      setUsers(response.data);
      // setFilters({ ...filters, totalPages: response.data?.totalPages });
      tableMeta.updatePagination({
        totalPages: response.data?.totalPages,
        page: 1,
      });
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };
  const onOptionsSelect = (_selected: any, type: string) => {
    setFilters((_filtes: any) => {
      const current = _.get(_filtes, type);
      return {
        ..._filtes,
        [type]: {
          ...current,
          selected: _selected,
        },
      };
    });
  };

  // const getUsers = async () => {
  //   try {
  //     setLoading(true);
  //     await getUsersFact();
  //     setUsers(JSON_DATA);
  //     setLoading(false);
  //   } catch (err) {
  //     setLoading(false);
  //   }
  // };

  const getUserByEmpId = (empId: string) => {
    return empId
      ? users.results.find((item: any) => item.emp_Id === empId)
      : {};
  };

  const onViewUser = () => {
    const [empId] = selected;
    setSelectedUser({
      empId,
      state: ModalState.VIEW,
    });
  };

  const onModifyUser = (user: User) => {
    setUsers(() => {
      const copy = users;
      const index = users.results.findIndex(
        (item: any) => item.emp_id === user.empId
      );
      copy.splice(index, 1, user);
      return [...copy];
    });
    onModalHide();
    showToast({
      variant: "success",
      title: "Successful Edit",
      content: "Contact details were modified.",
    });
  };

  const onDeleteUser = () => "";

  const onDeactivateUser = () => "";

  const onCheckAll = (checked: boolean) => {
    setSelectAll(checked);
    setSelected(() =>
      checked ? users.results.map((item: any) => item.empId) : []
    );
  };

  const onSelectUser = (item: User) => {
    if (checkSelected(item)) {
      setSelected((_selected) => {
        const copy = _selected;
        const index = copy.findIndex((o) => o === item.empId);
        copy.splice(index, 1);
        return [...copy];
      });
    } else {
      setSelected((_selected) => {
        const copy = _selected;
        copy.push(item.empId);
        return [...copy];
      });
    }
  };

  const checkSelected = (item: User) => {
    return selected.includes(item.empId);
  };

  // const setSearch = (search: string) => {
  //   setFilters((_filters: any) => ({
  //     ..._filters,
  //     search,
  //   }));
  // };

  const onSearch = (term: string) => {
    setSearch(term);
  };

  // const onDepartmentFilter = () => { };

  // const onLocationFilter = () => { };

  // const onLevelFilter = () => { };

  const onModalHide = () => {
    setSelectedUser({
      empId: "",
      state: ModalState.VIEW,
    });
  };

  const onPageSelect = async (page: number) => {
    // setFilters((_filters: any) => ({
    //   ..._filters,
    //   page,
    // }));
    try {
      setIsLoading(true);
      const obj = {};
      Object.keys(filters).map((key: any) =>
        Object.assign(obj, { [key]: filters[key].selected.join(",") })
      );
      const response = await getAllUserFromFileFact({
        file_id: id,
        page_size: 10,
        page,
        search: search.length > 0 ? search : undefined,
        ...obj,
      });
      setUsers(response.data);
      tableMeta.updatePagination({
        totalPages: response.data?.totalPages,
        page,
      });
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  useDebounce(
    () => {
      // if (!search.touched) return;
      onPageSelect(1);
    },
    500,
    [search, filters]
  );

  const getColumns = () => {
    const metasColums = () => {
      const arr: any = [];
      metas.map((item: any) => {
        if (item.key) {
          return arr.push({ key: item.key, label: item.title, hasSort: true });
        } else return 0;
      });
      return arr;
    };
    const basic = [
      {
        key: 1,
        renderItem: () => (
          <FormGroup className="pb-1">
            <FormControl.Checkbox>
              <FormControl.Checkbox.Input
                checked={selectAll}
                onChange={(evt: any) => onCheckAll(evt.target.checked)}
              />
            </FormControl.Checkbox>
          </FormGroup>
        ),
        hasSort: false,
      },
      { key: 2, label: "No.", hasSort: false },
    ];

    return [
      ...basic,
      { key: 3, label: "First Name", hasSort: true },
      { key: 4, label: "Last Name", hasSort: true },
      { key: 5, label: "Employee ID", hasSort: true },
      { key: 6, label: "Email", hasSort: true },
      ...metasColums(),
      // { key: 7, label: "Location", hasSort: true },
      // { key: 8, label: "Department", hasSort: true },
    ];
  };

  const getMetaColumns = () => {
    const arr: any = [];
    metas.map((item: any) => {
      if (item.key) {
        return arr.push(item.key);
      } else return 0;
    });
    return arr;
  };
  const getRows = () => {
    return users.results.map((item: User, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.key} selected={checked}>
          <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectUser(item)}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data>
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firstName}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.lastName}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.empId}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.email}</Text.P1>
          </Table.Data>
          {getMetaColumns().map((_i: any) => (
            <Table.Data>
              <Text.P1>
                {item?.metadata && item?.metadata[_i]
                  ? item?.metadata[_i].value
                  : "-"}
              </Text.P1>
            </Table.Data>
          ))}
          {/* <Table.Data>
            <Text.P1>{item.location}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.department}</Text.P1>
          </Table.Data> */}
        </Table.Row>
      );
    });
  };

  const getFilterLayout = () => {
    if (selected.length) {
      return (
        <Layout.Row className="pb-3">
          <Layout.Col xl={8}>
            <Text.H3 className="text-primary">
              {selected.length} User{selected.length > 1 ? "s" : ""} Selected
            </Text.H3>
          </Layout.Col>
          <Layout.Col xl={4} className="text-right">
            {selected.length === 1 && (
              <Button
                onClick={onViewUser}
                className="ml-2"
                variant="outline-primary"
              >
                View
              </Button>
            )}
            <Button
              onClick={onDeactivateUser}
              className="ml-2"
              variant="outline-primary"
            >
              Deactivate
            </Button>
            <Button
              onClick={onDeleteUser}
              className="ml-2"
              variant="outline-danger"
            >
              Delete
            </Button>
          </Layout.Col>
        </Layout.Row>
      );
    }
    return (
      <Layout.Row className="py-3">
        <Layout.Col className="py-1" xl={4} lg={4} md={12}>
          <FormControl.Search
            value={search}
            onChange={(e: any) => {
              setSearch(e.target.value);
            }}
            onSearch={(value: string) => {
              onSearch(value);
            }}
            placeholder="Search for name, email or emp id"
          />
        </Layout.Col>
        <Layout.Col xl={8} lg={8} md={12}></Layout.Col>
        <Layout.Col xl={12} lg={12} md={12}>
          <Layout.Row className="pt-3">
            {metas.slice(0, metas.length - 1).map((_i: any) => (
              <Layout.Col className="" xl={2} lg={3} md={6}>
                <Table.FilterMultiSelect
                  onOptionsSelect={(_selected: any) =>
                    onOptionsSelect(_selected, _i.key)
                  }
                  selected={filters[_i.key]?.selected || []}
                  title={_i.title}
                  options={_i.value}
                />
              </Layout.Col>
            ))}
          </Layout.Row>
        </Layout.Col>

        {/* <Layout.Col className="py-1" xl={2} lg={3} md={6}>
          <Table.FilterMultiSelect
            onOptionsSelect={(_selected: any) =>
              onOptionsSelect(_selected, "location")
            }
            selected={filters.location.selected}
            title="Location"
            options={filters.location.options}
          />
        </Layout.Col>
        <Layout.Col className="py-1" xl={2} lg={2} md={6}>
          <Table.FilterMultiSelect
            onOptionsSelect={(_selected: any) =>
              onOptionsSelect(_selected, "level")
            }
            selected={filters.level.selected}
            title="Level"
            options={filters.level.options}
          />
        </Layout.Col> */}
      </Layout.Row>
    );
  };

  const getViewModifyTemplate = () => {
    const canView: boolean =
      !!selectedUser.empId && selectedUser.state === ModalState.VIEW;
    const canEdit: boolean =
      !!selectedUser.empId && selectedUser.state === ModalState.MODIFY;
    const userData: any = getUserByEmpId(selectedUser.empId);

    return (
      <>
        {canView && (
          <ViewUserDetails
            show={canView}
            user={userData}
            onModify={() => {
              setSelectedUser({
                empId: userData.empId,
                state: ModalState.MODIFY,
              });
            }}
            onHide={onModalHide}
          />
        )}
        {canEdit && (
          <ModifyUserDetails
            show={canEdit}
            user={userData}
            onSave={onModifyUser}
            onHide={onModalHide}
          />
        )}
      </>
    );
  };

  return (
    <Div className="bg-white px-3 pb-5">
      <CustomHeader
        metaItem={
          !selected.length ? (
            <>
              <Button
                type="button"
                className="mx-1"
                variant="primary"
                size="lg"
                onClick={() => setShowAddModal(true)}
              >
                Add Users
              </Button>
              <Button variant="outline-primary" className="mx-1" title="">
                ...
              </Button>
            </>
          ) : (
            ""
          )
        }
        title={
          <Div>
            {!selected.length && (
              <Text.H2 className="pt-2 d-block text-primary">
                {isLoading ? <Placeholder width="col-12" /> : fileInfo.title}
              </Text.H2>
            )}
          </Div>
        }
        breadcrumbs={[
          {
            label: "Dataload",
            icon: <Group className="grey-icon__svg" />,
            route: appUrls.admin.dataLoad.default,
          },
          { label: "Employees" },
          { label: fileInfo.title },
        ]}
      />
      <Layout.Container fluid className="px-0 pt-3">
        {getFilterLayout()}
        <Table
          columns={getColumns()}
          isLoading={isLoading}
          rows={users.results}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={(d: any) => {
            tableMeta.onPageSelect(d);
            onPageSelect(d);
          }}
        />
      </Layout.Container>
      {getViewModifyTemplate()}
      <AddEmployeeModal
        show={showAddModal}
        size="lg"
        onHide={() => {
          setShowAddModal(false);
        }}
        aria-labelledby="contained-modal-title-vcenter"
        centered
        dialogClassName="modal-90w"
      />
    </Div>
  );
};

export default ManageEmployeeData;
