import React from "react";
// useXHR
import { useState } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import {
  getSurveyResponsesFact,
  submitSurveyResponseFact,
} from "../../dashboard-api";

interface sectionItem {
  id: number;
  title: string;
}

const useSurvey = () => {
  const [section, setSection] = useState<sectionItem | any>({
    id: 0,
    title: "",
  });

  const toastr = useToastr();

  const [isLoading, setLoading] = React.useState(false);
  const [isSaving, setSaving] = React.useState(false);
  const [invalidResponses, setInvalidResponses] = React.useState<Array<any>>(
    []
  );
  // const sectionsData = useXHR({
  //   defaultResponse: [],
  // });
  // const questionsData = useXHR({
  //   defaultResponse: [],
  // });
  // const sections = sectionsData.data;
  // const setSections = sectionsData.setData;
  // const questions = questionsData.data;
  // const setQuestions = questionsData.setData;
  const [sections, setSections] = useState<sectionItem[]>([]);
  const [questions, setQuestions] = useState<any>([]);
  const [confirmSubmit, setConfirmSubmit] = React.useState<boolean>(false);
  const [analytics, setAnalytics] = React.useState<any>({
    total: 0,
    completed: 0,
    sections: {},
  });
  const [meta, setMeta] = React.useState<any>({
    indexId: 0,
    title: "",
    hideBack: true,
    hideNext: true,
    nextTitle: "",
    buttonType: "",
    lastModfied: "",
    endDate: "",
  });

  React.useEffect(() => {
    const _i = [...sections];
    const index = _i.findIndex(
      (item: any) => item.id === (section && section.id)
    );

    const isLast = index === sections.length - 1;
    setMeta((_meta: any) => ({
      ..._meta,
      hideBack: !index,
      canSubmit: isLast,
      // nextTitle: isLast ? "Finish and Submit" : "Next",
      nextTitle: isLast ? "Submit my response" : "Next",
      buttonType: isLast ? "submit" : "button",
    }));
  }, [section]);

  React.useEffect(() => {
    const _i = [...sections];
    const index = _i.findIndex(
      (item: any) => item.id === (section && section.id)
    );
    const isLast = index === sections.length - 1;
    setMeta((_meta: any) => ({
      ..._meta,
      hideBack: !index,
      canSubmit: isLast,
    }));
  }, [sections]);

  React.useEffect(() => {
    calculateAnalytics();
  }, [questions]);

  const onGetResponses = (response: any) => {
    setLoading(false);
    const _section =
      response.sections && response.sections.length && response.sections[0];
    setSections(response.sections);
    setSection(_section);
    setQuestions(response.questions);
    setMeta((_meta: any) => ({
      ..._meta,
      indexId: response.survey.id,
      title: response.survey.surveyFor
        ? `${response.survey.title} - ${response.survey.surveyFor}`
        : response.survey.title,
      surveyFor: response.survey.surveyFor || "",
      lastModified: response.survey.lastModified,
      thankyouText: response.survey.thankyouText,
      wavierId: response.wavierId,
      eligibilityId: response.eligibilityId,
      endDate: response.survey.endDate,
      hideNoBasisOption: response.hide_no_basis_option ?? false
    }));
  };

  const getSurveyData = (id: any, surveyId: any) => {
    setLoading(true);
    getSurveyResponsesFact(id, surveyId).then(
      (response: any) => {
        onGetResponses(response);
      },
      (err: any) => {
        toastr.onError(err);
        setLoading(false);
        setQuestions([]);
      }
    );
  };

  const getSectionsCount = (_sections: any, item: any) => {
    const count = util.lib.get(_sections, item.categoryID) || 0;
    return {
      ..._sections,
      [item.categoryID]: count + 1,
    };
  };

  const removeFromInvalidResponse = (_id: any) => {
    setInvalidResponses((_invalid: any) =>
      _invalid.filter((item: any) => {
        return item !== _id;
      })
    );
  };

  const calculateAnalytics = () => {
    const { QUESTION } = util.enums;
    const validQuestions = questions.filter(
      //!item.isDemographic &&
      (item: any) =>  item.type !== QUESTION.Paragraph
    );
    const _meta = {
      total: validQuestions.length,
      totalMandatory: 0,
      completed: 0,
      hasCompleted: false,
      sections: {},
    };
    validQuestions.forEach((item: any) => {
      switch (item.type) {
        case QUESTION.Radio:
          if (item.radio.selected.length) {
            _meta.sections = getSectionsCount(_meta.sections, item);
            _meta.completed += 1;
            removeFromInvalidResponse(item.id);
            // _meta.hasCompleted = (_meta.completed === _meta.total);
          }
          break;
        case QUESTION.Checkbox:
          if (item.checkbox.selected.length) {
            _meta.sections = getSectionsCount(_meta.sections, item);
            _meta.completed += 1;
            removeFromInvalidResponse(item.id);
            // _meta.hasCompleted = (_meta.completed === _meta.total);
          }
          break;
        case QUESTION.Input:
          if (item.feedback) {
            _meta.completed += 1;
            // _meta.hasCompleted = (_meta.completed === _meta.total);
            _meta.sections = getSectionsCount(_meta.sections, item);
            removeFromInvalidResponse(item.id);
          }
          break;
        case QUESTION.Rating:
          if (item.rating.selected || item.rating.selected === 0) {
            _meta.completed += 1;
            _meta.sections = getSectionsCount(_meta.sections, item);
            removeFromInvalidResponse(item.id);
          }
          break;
        default:
          return;
      }
    });
    setAnalytics(_meta);
  };

  const updateLastModified = () => {
    setMeta((_m: any) => {
      return {
        ..._m,
        lastModified: util.date.getFormatedTime(
          new Date(),
          "MMM dd, yyyy, HH:mm"
        ),
      };
    });
  };

  const getActiveQuestions = () => {
    if (section && section.id) {
      return questions.filter((item: any) => item.categoryID === section.id);
    }
    return [];
  };

  const onQuestionUpdate = (updated: any) => {
    setQuestions((_questions: any) =>
      _questions.map((item: any) => (item.id === updated.id ? updated : item))
    );
    updateLastModified();
  };

  const getCompletion = () => {
    const { total, completed } = analytics;
    const percentage = (completed / total) * 100;
    // return `${percentage} %`;
    return `${util.math.roundOff(percentage)}%`;
  };

  const onDeleteResponse = (_question: any, responseId: any) => {
    const payload = {
      ..._question,
      responseId: 0,
      radio: {
        selected: "",
        options: _question.radio.options || [],
      },
      checkbox: {
        selected: [],
        options: _question.checkbox.options || [],
      },
      rating: {
        selected: "",
        tags: _question.rating.tags || {},
      },
      input: {
        selected: "",
      },
    };
    setQuestions(
      questions.map((item: any) =>
        item.responseId === responseId ? payload : item
      )
    );
  };

  const onSubmitSurvey = (id: any) => {
    return submitSurveyResponseFact(id)
      .then(() => {
        toastr.onSucces({ content: "Survey submitted successfully!" });
        return true;
      })
      .catch((err: any) => {
        const invalidList = err.reason ? err.reason.unanswered_questions : [];
        toastr.onError(
          invalidList && invalidList.length
            ? {
                msg: "Please answer all the mandatory questions before submitting.",
              }
            : err
        );
        setInvalidResponses(invalidList.map((item: any) => item.id));
        setConfirmSubmit(false);
        if (invalidList.length) {
          const index = sections.findIndex((item: any) => {
            const sectionId = invalidList[0].section;
            return Number(sectionId) === item.id;
          });
          if (index !== -1 && sections[index]) {
            const _section = util.lib.cloneDeep(sections[index]);
            setSection(_section);
          }
        }
        return false;
      });
  };

  return {
    isLoading,
    setLoading,
    isSaving,
    setSaving,
    questions,
    invalidResponses,
    setInvalidResponses,
    section,
    setQuestions,
    setSection,
    setSections,
    sections,
    getActiveQuestions,
    meta,
    setMeta,
    onQuestionUpdate,
    confirmSubmit,
    setConfirmSubmit,
    setAnalytics,
    analytics,
    getCompletion,
    onDeleteResponse,
    getSurveyData,
    onSubmitSurvey,
    onGetResponses,
  };
};

export default useSurvey;
