import React from "react";
import { Button, Div, Text, Icon } from "unmatched/components";
import styled from 'styled-components';

const StyledButton = styled(Button)`
    float: right;
    margin: 77px 0px 0px 0px;
`;

export const SetPasswordSuccess = (props: any) => {
  const origin = window.location.origin;
  return (
    <Div style={{ height: 200 }}>
      <Icon
        icon="fas fa-check-circle bg-white rounded-circle shadow"
        className="fs-24 mt-2"
        variant="success"
      />
      <Text.H3 className="mt-2 mb-2">Password Successfully Set</Text.H3>
      <Text.P1>
        Your password has been successfully set up. You may login using the
        passwordless login link or using your password at {origin.substring(origin.indexOf('/') + 2)}/auth/login
      </Text.P1>
      <StyledButton onClick={props.onDone}>Done</StyledButton>
    </Div>
  );
};
