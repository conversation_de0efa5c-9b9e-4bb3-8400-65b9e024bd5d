import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

const getEligibleCategories = () => {
  return [];
};

const getEligibleRaterGroups = (surveys: any) => {
  const filtered = surveys.filter((item: any) => {
    return item.rater_groups && item.rater_groups.length;
  });
  let groups: any = [];
  filtered.forEach((item: any) => {
    const selected = item.rater_groups.map((group: any) => ({
      id: group.id,
      key: group.id,
      title: group.title,
      groupId: item.id,
      groupKey: group["rater_group"],
    }));
    groups = [...groups, ...selected];
  });
  return [{ id: null, key: null, title: 'All' }, ...groups];
};

export const getAnalyticsFiltersFact = (params: any) => {
  return axios
    .get("/analytics/surveys/", {
      params,
    })
    .then(({ data }: AxiosResponse) => {
      const surveys = data.surveys.map((item: any) => ({
        ...item,
        key: item.id,
        type: item.resource_type,
      }))
      return {
        meta: data.metadata_labels,
        categories: getEligibleCategories(),
        groups: getEligibleRaterGroups(data.surveys),
        surveys,
        surveysData: surveys,
        years:
          data.years.map((item: any) => ({
            id: item,
            key: item,
            title: item,
          })) || [],
        types: util.lib
          .entries(data.resource_types)
          .map(([key, value]: any) => ({
            id: value,
            key: value,
            title: (key || "").split(" ").map((item: any) => util.lib.capitalize(item)).join(" "),
          })),
        selected: {
          years: [data.selected_year],
          surveys: data.selected_surveys || [],
          types: data.selected_resource_types || [],
          groups: [],
          // categories: data.selected_categories || [],
        },
      };
    });
};
