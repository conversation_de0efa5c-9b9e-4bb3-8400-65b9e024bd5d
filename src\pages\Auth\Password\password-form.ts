import * as yup from "yup";

const hasNumber = new RegExp("^(?=.*[0-9]).+$");
const hasAlphabet = new RegExp("^(?=.*[a-zA-Z]).+$");

const passowrdCheck = yup
  .string()
  .required()
  .min(8, "At least 8 characters")
  .matches(hasNumber, "At least 1 letter")
  .matches(hasAlphabet, "At least 1 number");

export interface PasswordType {
  password: string;
  confirmPassword: string;
}

export const validatePassword = (password: string) => {
  return {
    number: hasNumber.test(password),
    alphabet: hasAlphabet.test(password),
    min: password.length >= 8,
  };
};

export default yup.object().shape({
  password: passowrdCheck,
  confirmPassword: yup
    .string()
    .required("Enter a valid password")
    .oneOf(
      [yup.ref("password"), null],
      "Passwords do not match. Enter the same password as above"
    ),
});

export const ChangePasswordSchema = yup.object().shape({
  oldPassword: yup.string().required("Old password is required."),
  password: passowrdCheck.test("is-same", "Cannot be same as old paswword", function(pass): any {
    const { oldPassword } = this.parent;
    return oldPassword !== pass;
  }),
  confirmPassword: yup
    .string()
    .required("Enter a valid password")
    .oneOf(
      [yup.ref("password"), null],
      "Passwords do not match. Enter the same password as above"
    ),
});
