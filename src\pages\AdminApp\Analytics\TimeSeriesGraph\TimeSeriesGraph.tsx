import React from "react";
import { Text, Layout, Div, ComboFilter } from "unmatched/components";
import CompareChart, { getColors } from "../LineChart/LineChart";
import Legend from "../Legend";
import LegendFor from "../LegendFor";
import { getUsersFact } from "unmatched/modules/session/api";
import { SurveyOptions } from "./SurveyOptions";
// import Item from '../Item/Item';

const getFilterData = (_filters: any, custom: any) => {
  // let demographicFilters = {};
  // _demographics.forEach((item: any) => {
  //   demographicFilters = {
  //     ...demographicFilters,
  //     [`demographics__${item.key}`]: {
  //       label: item.title,
  //       values: item.options,
  //     },
  //   };
  // });
  return {
    ..._filters,
    ...custom,
  };
};

const TimeSeriesGraph = (props: any) => {
  const {
    data,
    labels,
    filtersState,
    onFilterSelect,
    isLoading,
    resourceTypes,
  } = props;

  const onSearchPeople = (_val: string) => {
    return getUsersFact(_val).then((response: any) => {
      return response.map((item: any) => `${item.name}(${item.empId})`);
    });
  };

  const comboFiltersData = getFilterData(filtersState.filters, {
    people: {
      label: "People",
      values: [],
      isDynamic: true,
      onSearch: onSearchPeople,
    },
  });

  const getGraphTemplate = () => {
    if (isLoading) {
      return <Div className="py-5 my-5">Loading Graph Data....</Div>;
    } else if (!data.length) {
      return <Div className="py-5 my-5">No Data Found</Div>;
    }

    const legends = getColors(labels.length).map(
      (item: string, index: number) => {
        return { key: item, color: item, label: labels[index] };
      }
    );

    return (
      <>
        <CompareChart data={data} labels={labels} />
        <LegendFor legends={legends} />
      </>
    );
  };

  return (
    <Div>
      <Div className="my-4">
        <Text.H3 className="my-2">Compare Against</Text.H3>
        <ComboFilter
          filters={comboFiltersData}
          selected={filtersState.selected}
          onFilterSelect={(_selected: any) => {
            filtersState.onSelect(_selected);
            onFilterSelect(_selected);
          }}
          onSubmit={() => ""}
        />
      </Div>
      <Div>
        <Text.H3>Time Series Graph</Text.H3>
        <Layout.Row className="pt-4 mx-0">
          <Layout.Col xl={10} className="border p-4">
            {getGraphTemplate()}
          </Layout.Col>
          <Layout.Col>
            {resourceTypes && <SurveyOptions options={resourceTypes} />}
          </Layout.Col>
        </Layout.Row>
        <Legend items={resourceTypes?.legends} />
      </Div>
    </Div>
  );
};

export default TimeSeriesGraph;
