import React from "react";
import { Div, Layout, Text, Placeholder, Button } from "unmatched/components";
import styled from "styled-components";

interface StatusProps {
  // total: number;
  toDo: number;
  inProgress: number;
  completed: number;
  declined: number;
  selected: string;
  // setFilters?: any;
  // filters: any;
  setParameters: Function;
  isLoading?: boolean;
  isPairsEditable?: boolean;
  parameters: any;
  setShowSidebar?: (item: boolean) => void;
  isDeclinable: boolean;
}

const SurveyStatus = (props: StatusProps) => {
  const {
    toDo,
    inProgress,
    completed,
    declined,
    selected,
    // setFilters,
    // filters,
    setParameters,
    isLoading,
    isPairsEditable,
    setShowSidebar,
    isDeclinable,
  } = props;
  return (
    <>
      <Layout.Row className="mx-0 mb-3 px-1">
        <Layout.Col className="text-left pt-3 px-3" xs={12}>
          <Text.H3 className="text-secondary-title fs-12">
            Survey Status
          </Text.H3>
        </Layout.Col>
      </Layout.Row>
      <SurveyStatusDesign
        className={`d-flex flex-row justify-content-between align-items-center ${
          selected === "TODO" ? "active" : ""
        }`}
        onClick={() => {
          setParameters((p: any) => {
            return { ...p, status: "TODO" };
          });
          setShowSidebar && setShowSidebar(false);
          // setFilters({ ...filters, method: "TO_DO" })
        }}
      >
        <p className="m-0 label">To-Do</p>
        <p className="m-0 font-weight-bold">
          {" "}
          {isLoading ? <Placeholder width="col-1" /> : toDo}
        </p>
      </SurveyStatusDesign>
      <SurveyStatusDesign
        className={`d-flex flex-row justify-content-between align-items-center ${
          selected === "PROG" ? "active" : ""
        }`}
        onClick={() => {
          setParameters((p: any) => {
            return { ...p, status: "PROG", appliedMetaFilters: {}, search: "" };
          });
          setShowSidebar && setShowSidebar(false);
          // setFilters({ ...filters, method: "PROG" })
        }}
      >
        <p className="m-0 label">In-Progress</p>
        <p className="m-0 font-weight-bold">
          {" "}
          {isLoading ? <Placeholder width="col-1" /> : inProgress}
        </p>
      </SurveyStatusDesign>
      <SurveyStatusDesign
        className={`d-flex flex-row justify-content-between align-items-center ${
          selected === "SUBM" ? "active" : ""
        }`}
        onClick={() => {
          setParameters((p: any) => {
            return { ...p, status: "SUBM", appliedMetaFilters: {}, search: "" };
          });
          setShowSidebar && setShowSidebar(false);
          // setFilters({ ...filters, method: "COMP" })
        }}
      >
        <p className="m-0 label">Completed</p>
        <p className="m-0 font-weight-bold">
          {" "}
          {isLoading ? <Placeholder width="col-1" /> : completed}
        </p>
      </SurveyStatusDesign>
      {isDeclinable && (
        <SurveyStatusDesign
          className={`d-flex flex-row justify-content-between align-items-center ${
            selected === "DECL" ? "active" : ""
          }`}
          onClick={() => {
            setParameters((p: any) => {
              return {
                ...p,
                status: "DECL",
                appliedMetaFilters: {},
                search: "",
              };
            });
            setShowSidebar && setShowSidebar(false);
            // setFilters({ ...filters, method: "DECL" })
          }}
        >
          <p className="m-0 label">Declined</p>
          <p className="m-0 font-weight-bold">
            {" "}
            {isLoading ? <Placeholder width="col-1" /> : declined}
          </p>
        </SurveyStatusDesign>
      )}

      <hr className="my-0" />
      {isPairsEditable && (
        <>
          {/* <SurveyStatusDesign
            className={`${selected === "ADD" ? "active" : ""}`}
            onClick={() => {
              setParameters((p: any) => {
                return {
                  ...p,
                  status: "ADD",
                  appliedMetaFilters: {},
                  search: "",
                };
              });
              // setFilters({ ...filters, method: "ADD" })
            }}
          >
            <p className="m-0 text-primary label">Add Reviewee</p>
          </SurveyStatusDesign> */}
          <Div className="px-3 mt-3">
            <Button
              className="d-block w-100 fs-12"
              disabled={selected === "ADD"}
              onClick={() => {
                setParameters((p: any) => {
                  return {
                    ...p,
                    status: "ADD",
                    appliedMetaFilters: {},
                    search: "",
                  };
                });
                setShowSidebar && setShowSidebar(false);
                // setFilters({ ...filters, method: "ADD" })
              }}
            >
              Add Reviewee
            </Button>
            <Div className="mt-3 p-2" style={{ background: "#FCEED0" }}>
              <Text.P1 className="fs-12">
                Please click <b>Add Reviewee </b> to add a new person to rate.
              </Text.P1>
            </Div>
          </Div>
        </>
      )}
    </>
  );
};

const SurveyStatusDesign = styled(Div)`
  font-size: 14px;
  /* max-width: 200px; */
  background: ${(props) => (props.isActive ? "#F2F2F2" : "none")};
  border-left-width: 2px;
  box-sizing: border-box;
  margin: 5px 0;
  padding: 7px 20px;
  cursor: pointer;
  .label {
    font-size: 12px;
  }
  &.active {
    background: #ebeff7 !important;
    position: relative;
    color: #2f2f2f !important;
    border-radius: 0;
    &::after {
      border-bottom: 2px solid #518cff;
      position: absolute;
      content: "";
      z-index: 999;
      width: 10%;
      left: 20px;
      bottom: 0;
    }
  }
`;

export default SurveyStatus;
