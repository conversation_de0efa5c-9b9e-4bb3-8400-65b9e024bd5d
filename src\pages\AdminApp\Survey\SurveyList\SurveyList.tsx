import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Div,
  FormControl,
  Text,
  Layout,
  Tab,
  <PERSON><PERSON>,
  PageContainer,
  Modal,
  Nav,
  Table,
} from "unmatched/components";
import { useQuery, useHistory } from "unmatched/hooks";
// import useToastr from "unmatched/modules/toastr/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import ModalHeader from "../../ModalHeader";
import ChooseType from "./ChooseType";
import CloneSurvey from "./CloneSurvey";
import SurveysView from "./SurveysView";
import DraftSurveys from "./DraftSurveys";
import LinkSurvey from "./LinkSurvey";
import utilModule from "unmatched/utils";
import { UMTab } from "unmatched/components/Tabs";

export const SURVEY_TYPES = {
  ON_GOING: "ON_GOING",
  SCHEDULED: "SCHEDULED",
  ENDED: "ENDED",
  DRAFT: "DRAFT",
};

const Surveylist = () => {
  const history = useHistory();
  const queryParams = useQuery();

  const [filter, setFilter] = useState(
    queryParams.get("filter") || SURVEY_TYPES.ON_GOING
  );
  const [search, setSearch] = useState("");
  const [temp, setTemp] = useState("");

  const [showModal, setModal] = useState(false);
  const [modalState, setModalState] = useState("");
  const [resourceType, setResourceType] = useState("SurveyIndexUpward");

  const onSurveyCreate = (id: number) => {
    history.push(appUrls.admin.survey.create.getUpwardReviewUrl(id));
  };

  const onFilterChange = (_filter: string) => {
    history.push(appUrls.admin.survey.getSurveysUrl(_filter));
    setFilter(_filter);
  };

  const onModalHide = () => {
    setModalState("");
    setModal(false);
  };

  const onModalShow = () => {
    setModal(true);
  };

  const [surveyID, setSurveyID] = useState("");

  const getFilterLink = (key: string, title: string) => {
    return (
      <Link
        className={filter === key ? "text-primary" : ""}
        to={appUrls.admin.survey.getSurveysUrl(key)}
      >
        {title}
      </Link>
    );
  };

  const onNextSelfStep = (id: string) => {
    setModalState("LINK");
    setSurveyID(id);
  };

  const getActiveFilter = (_key: string) => {
    return filter === _key;
  };

  const getNavItem = (title: string, key: string) => {

    return <UMTab
      eventKey={key}
      activeKey={filter}
      onClick={() => onFilterChange(key)}
    >
      {getFilterLink(key, title)}
    </UMTab>;
  };

  const getTabsTemplate = () => {
    const resourceOptions = Object.entries(utilModule.getSurveyTypes);
    return (
      <Div className="custom-tabs-2 surveys-list">
        <Tab.Container activeKey={filter}>
       
          <Div className="mt-3" style={{ width: 250 }}>
            <Table.Filter
              onSelect={(o: any) => {
                setResourceType(o.key);
              }}
              selected={
                resourceOptions.find(([k]: any) => resourceType === k)?.[1]
              }
              title={"Survey Type"}
              options={resourceOptions.map(([k, v]: any) => ({
                title: v,
                key: k,
              }))}
            />
          </Div>
          <Tab.Content>
            <Tab.Pane eventKey={SURVEY_TYPES.ON_GOING}>
              <SurveysView
                search={search}
                filter={SURVEY_TYPES.ON_GOING}
                isActive={getActiveFilter(SURVEY_TYPES.ON_GOING)}
                resourceType={resourceType}
              />
            </Tab.Pane>
            {/* <Tab.Pane eventKey={SURVEY_TYPES.SCHEDULED}>
              <SurveysView
                search={search}
                filter={SURVEY_TYPES.SCHEDULED}
                isActive={getActiveFilter(SURVEY_TYPES.SCHEDULED)}
                resourceType={resourceType}
              />
            </Tab.Pane> */}
            <Tab.Pane eventKey={SURVEY_TYPES.ENDED}>
              <SurveysView
                search={search}
                filter={SURVEY_TYPES.ENDED}
                isActive={getActiveFilter(SURVEY_TYPES.ENDED)}
                resourceType={resourceType}
                showReports
              />
            </Tab.Pane>
            <Tab.Pane eventKey={SURVEY_TYPES.DRAFT}>
              <DraftSurveys
                search={search}
                filter={SURVEY_TYPES.DRAFT}
                isActive={getActiveFilter(SURVEY_TYPES.DRAFT)}
                resourceType={resourceType}
              />
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Div>
    );
  };

  const getSearchInput = () => {
    return (
      <Layout.Flex>
        <Layout.FlexItem>
          <FormControl.Search
            placeholder="Search for surveys"
            value={temp}
            size="sm"
            onChange={(evt: any) => setTemp(evt.target.value)}
            onSearch={setSearch}
          />
        </Layout.FlexItem>
        <Layout.FlexItem className="pl-3">
          <Button onClick={onModalShow}>Create New Survey</Button>
        </Layout.FlexItem>
      </Layout.Flex>
    );
  };

  const getModalTemplate = () => {
    if (modalState === "LINK") {
      return (
        <LinkSurvey
          onCreateSelfEvaluation={onSurveyCreate}
          surveyID={surveyID}
          toggleClone={() => setModalState("")}
        />
      );
    } else if (modalState === "CLONE") {
      return (
        <CloneSurvey
          onTempleteSelect={() => ""}
          toggleClone={() => setModalState("")}
        />
      );
    }
    return (
      <ChooseType
        onTempleteSelect={onSurveyCreate}
        onSelectSelfEvaluation={(id: string) => onNextSelfStep(id)}
        toggleClone={() => setModalState("CLONE")}
      />
    );
  };

  return (
    <PageContainer>
      <CustomHeader
        title={
          <Div>
            <Text.H1 className="pb-2">Surveys</Text.H1>
            <Div className="sticky-tabs-container" style={{ position: 'absolute' }}>
              <Nav className="nav-tabs sticky">
                {getNavItem("Ongoing", SURVEY_TYPES.ON_GOING)}
                {/* {getNavItem("Scheduled", SURVEY_TYPES.SCHEDULED)} */}
                {getNavItem("Ended", SURVEY_TYPES.ENDED)}
                {getNavItem("Drafts", SURVEY_TYPES.DRAFT)}
              </Nav>
            </Div>
          </Div>
        }
        metaItem={getSearchInput()}
        style={{ padding: '25px 30px 30px' }}
      />
      <Layout.Container fluid className="pt-5">
        <Div>{getTabsTemplate()}</Div>
      </Layout.Container>
      <Modal show={showModal} size="xl" centered>
        <ModalHeader title="Create new survey" onHide={onModalHide} />
        <Modal.Body>{getModalTemplate()}</Modal.Body>
      </Modal>
    </PageContainer>
  );
};

export default Surveylist;
