import appUrls from "unmatched/utils/urls/app-urls";
// import Statistics from "./Statistics/Statistics";
import People from "./People/People";
import Aggregate from "./Aggregate/Aggregate";
import EngagementAnalytics from "./EngagementAnalytics/EngagementAnalytics";
import RankingList from "./RankingList/RankingList";
import ExitAnalytics from "./ExitAnalytics/ExitAnalytics";

const routes = [
  // {
  //   name: "Statistics",
  //   path: appUrls.admin.analytics.statistics.default,
  //   isExact: false,
  //   component: Statistics,
  // },
  {
    name: "People Analytics",
    path: appUrls.admin.analytics.people.default,
    isExact: false,
    component: People,
  },
  {
    name: "Aggregate Analytics",
    path: appUrls.admin.analytics.getAggregateUrl(),
    isExact: false,
    component: Aggregate,
  },
  {
    name: "Ranking List",
    path: appUrls.admin.analytics.geRankingListUrl(),
    isExact: false,
    component: RankingList,
  },
  {
    name: "Engagement Analytics",
    path: appUrls.admin.analytics.engagement.default,
    isExact: false,
    component: EngagementAnalytics,
  },
  {
    name: "Exit Analytics",
    path: appUrls.admin.analytics.exit.default,
    isExact: false,
    component: ExitAnalytics,
  },
];

export default routes;
