import React from "react";
import { Route, Redirect } from "react-router-dom";
import AppRoutes from "../../../AppRoutes";
import EXIT_ROUTES from "./exit-routes";
import appUrls from "unmatched/utils/urls/app-urls";

const ExitAnalytics = () => {
  return (
    <AppRoutes routes={EXIT_ROUTES}>
      <Route exact path={appUrls.admin.analytics.exit.default}>
        <Redirect to={appUrls.admin.analytics.exit.list} />
      </Route>
    </AppRoutes>
  );
};
export default ExitAnalytics;
