import React from "react";
import { Route, Redirect } from "react-router-dom";
import AppRoutes from "../../../AppRoutes";
import PEOPLE_ROUTES from "./people-routes";
import appUrls from "unmatched/utils/urls/app-urls";

const People = () => {
  return (
    <AppRoutes routes={PEOPLE_ROUTES}>
      <Route exact path={appUrls.admin.analytics.people.default}>
        <Redirect to={appUrls.admin.analytics.people.list} />
      </Route>
    </AppRoutes>
  );
};
export default People;
