import icons from "assets/icons/icons";
import React from "react";
import {
  Div,
  CustomModal as Mo<PERSON>,
  <PERSON>ton,
  Text,
  Layout,
} from "unmatched/components";

export default function ConfigurePairingData(props: any) {
  const { SuccessTick, DangerAlert } = icons;
  return (
    <Modal
      {...props}
      onHide={props.onHide}
      children={
        <>
          <Modal.Header closeButton>
            <Modal.Title>Pairing File Results</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Text.H2>{props.selected?.title}</Text.H2>
            <Div
              className="d-flex  flex-column pt-2"
              style={{ minHeight: "50vh" }}
            >
              {(() => {
                const result = props.selected?.task?.result;
                return (
                  <Div className="mt-2">
                    <Text.H3 className="py-2">Successfull records</Text.H3>
                    <Layout.Row className="my-1">
                      <Layout.Col style={{ maxWidth: 70 }}>
                        <SuccessTick
                          width="24px"
                          height="24px"
                          className="mx-3  "
                        />
                      </Layout.Col>
                      <Layout.Col>
                        <Text.H2 className="pb-2 f14">
                          <span className="text-success">
                            Successfull Pairings:{" "}
                          </span>
                          <Text.P1
                            style={{ fontSize: 15, color: "#00000 !important" }}
                            className="f12"
                          >
                            {result?.success_upload_count} Pairings were
                            successfully created
                          </Text.P1>
                        </Text.H2>
                      </Layout.Col>
                    </Layout.Row>
                    <Layout.Row className="my-1">
                      <Layout.Col style={{ maxWidth: 70 }}>
                        <SuccessTick
                          width="24px"
                          height="24px"
                          className="mx-3  "
                        />
                      </Layout.Col>
                      <Layout.Col>
                        <Text.H2 className="pb-2  f14">
                          <span className="text-success">Total Pairings: </span>
                          <Text.P1
                            style={{ fontSize: 15, color: "#00000 !important" }}
                            className="f12"
                          >
                            {result?.total_file_records} Total file records
                          </Text.P1>
                        </Text.H2>
                      </Layout.Col>
                      
                    </Layout.Row>

                    {props.hasRejectedRecords && (
                      <>
                        <Text.H3 className="py-2">Rejected records</Text.H3>
                        {[
                          {
                            key: "duplicates",
                            label: "Duplicates",
                            text: "Duplicate records were found",
                          },
                          {
                            key: "emp_not_found",
                            label: "Employees Not Found",
                            text: "Employees were not found",
                          },
                          {
                            key: "invalid_format",
                            label: "Invalid Format",
                            text: "Records were found invalid",
                          },
                          {
                            key: "duplicate_abstract",
                            label: "Duplicates from last uploaded files",
                            text: "Records are duplicate from prevoius files",
                          },
                          {
                            key: "pairing_rule",
                            label: "Pairing rule",
                            text: "Records did not pass the pairing rules",
                          },
                        ].map((el) => {
                          return (
                            <Layout.Row key={el.key} className="my-1">
                              <Layout.Col style={{ maxWidth: 70 }}>
                                <DangerAlert
                                  width="24px"
                                  height="24px"
                                  className="mx-3  "
                                />
                              </Layout.Col>
                              <Layout.Col>
                                <Text.H2 className="pb-2  f14">
                                  <span className="text-danger">
                                    {el.label}
                                  </span>
                                  :
                                  <Text.P1
                                    style={{
                                      fontSize: 15,
                                      color: "#00000 !important",
                                    }}
                                    className="f12"
                                  >
                                    {" "}
                                    {result?.rejected[el.key]} {el.text}
                                  </Text.P1>
                                </Text.H2>
                              </Layout.Col>
                            </Layout.Row>
                          );
                        })}
                        <Div
                          onClick={() =>
                            props.onDownloadRejectedRecords(props.selected.id)
                          }
                        >
                          <Text.P2
                            className="cursor-pointer pt-3"
                            style={{
                              color: "#518CFF",
                              textDecoration: "underline",
                            }}
                          >
                            Download the mismatched records here.
                          </Text.P2>
                        </Div>
                      </>
                    )}
                  </Div>
                );
              })()}
            </Div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={props.onHide}
              variant="primary"
              className="float-right"
            >
              Close
            </Button>
          </Modal.Footer>
        </>
      }
    />
  );
}
