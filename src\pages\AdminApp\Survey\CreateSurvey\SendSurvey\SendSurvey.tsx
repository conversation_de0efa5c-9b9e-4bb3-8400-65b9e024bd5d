// Node modules
import React from "react";
import _ from "lodash";
import "react-quill/dist/quill.snow.css";
import styled from "styled-components";
import { useFormik } from "unmatched/hooks";
import Header from "../Header";
import {
  Text,
  Layout,
  Form,
  // FormGroup,
  FormControl,
  FormikAutoSave,
  Div,
  Icon,
  RichEditor,
} from "unmatched/components";
import util from "unmatched/utils";
import { useCreateSurveyContext } from "../Provider";
import { patchEmailTemplateFact } from "../../survey-api";
import {
  CUSTOM_EMAIL_INSERT_OPTIONS,
  emailTemplateVarMap,
  emailTemplateVarMap2,
  emailTemplateVarMapColorSub,
  emailTemplateVarMapText,
  emailTemplateVarMapText2,
} from "../../ManageSurvey/manage-survey-meta";
import { surveyProperties } from "unmatched/survey/creation/components";
import { Note } from "../../ManageSurvey/Emails/components/Note";
import { DateTime } from "luxon";
// import RichEditor from "../../../../../unmatched/components/RichEditor";

const SwitchGroup = surveyProperties.SwitchGroup;

const { isFieldInvalid, yup } = util.formik;

const getValue = (obj: any, key: string) => _.get(obj, key);

export const getComputedValue = (val: any, bool = true) => {
  let computedVal = Object.entries(emailTemplateVarMap).reduce(
    (acc, [k, v]) => {
      acc = bool ? acc.replaceAll(v, k) : acc.replaceAll(k, v);
      return acc;
    },
    val || ""
  );
  // work around for non standard templates already stored in DB
  computedVal = Object.entries(emailTemplateVarMap2).reduce((acc, [k, v]) => {
    acc = bool ? acc.replaceAll(v, k) : acc.replaceAll(k, v);
    return acc;
  }, computedVal || "");

  computedVal = bool
    ? computedVal.replaceAll(
        '<span style="color: black;">',
        '<span style="color: rgb(81, 140, 255);">'
      )
    : computedVal;

  return computedVal;
};

export const getComputedValueColorSub = (val: any) => {
  const computedVal = Object.entries(emailTemplateVarMapColorSub).reduce(
    (acc, [k, v]) => {
      acc = acc.replaceAll(k, v);
      return acc;
    },
    val || ""
  );
  return computedVal;
};

export const getComputedValueText = (val: any, bool = true) => {
  let computedVal = Object.entries(emailTemplateVarMapText).reduce(
    (acc, [k, v]) => {
      acc = bool ? acc.replaceAll(v, k) : acc.replaceAll(k, v);
      return acc;
    },
    val || ""
  );

  computedVal = Object.entries(emailTemplateVarMapText2).reduce(
    (acc, [k, v]) => {
      acc = bool ? acc.replaceAll(v, k) : acc.replaceAll(k, v);
      return acc;
    },
    computedVal || ""
  );

  return computedVal;
};

const FormControlTemplate = surveyProperties.FormControlTemplate;

const Pannel = styled.div`
  padding: 15px 0px 5px 0px;
`;

const SendSurvey = (props: any) => {
  const { updateBuilderStatus, survey, email } = useCreateSurveyContext();
  const [isSaving, setIsSaving] = React.useState(false);

  const onSubmit = (_values: any) => {
    if (props.viewOnly) return;
    setIsSaving(true);
    let body = _values.body?.replaceAll(`rgb(81, 140, 255)`, "black");
    body = getComputedValue(body, false);
    patchEmailTemplateFact(
      {
        ..._values,
        text: getComputedValueText(_values.text, false),
        subject: _values.subject,
        body,
      },
      email.data.id
    ).then(
      (repsone: any) => {
        setIsSaving(false);
        email.setData(repsone);
        survey.setData({
          ...survey.data,
          updatedAt: util.date.getFormatedTime(
            new Date(),
            " MMMM dd, yyyy, HH:mm"
          ),
        });
      },
      () => setIsSaving(false)
    );
  };

  const formikOptions = {
    initialValues: {
      subject: email.data.subject,
      body: email.data.body,
      text: email.data.text,
    },
    validationSchema: yup.object().shape({
      subject: yup.string().required(),
      body: yup.string().required(),
    }),
    onSubmit,
  };

  const formik = useFormik(formikOptions);

  React.useEffect(() => {
    !props.viewOnly && updateBuilderStatus(util.enums.SurveyStatus.Send);
  }, []);

  const onChangeRichText = (html: any, text: any) => {
    formik.setFieldValue("body", html);
    formik.setFieldValue("text", text);
  };

  const { values, handleSubmit, handleReset, setFieldValue } = formik;

  return (
    <Form
      onReset={handleReset}
      onSubmit={handleSubmit}
      className="mb-3 mr-xl-5 pr-xl-5"
    >
      <FormikAutoSave
        initialValues={formikOptions.initialValues}
        values={values}
        onSave={onSubmit}
      >
        <Header
          metaItem={
            <Text.P1>
              {isSaving ? (
                <>
                  <Icon spin icon="fal fa-spinner-third" /> Saving
                </>
              ) : (
                DateTime.fromISO(
                  new Date(survey.data.updatedAt).toISOString()
                ).toFormat(" LLL dd, yyyy, hh:mm a")
              )}
            </Text.P1>
          }
          title={<Text.H1 className="pb-2">{survey.data.name}</Text.H1>}
          breadcrumbs={props.breadcrumbs}
        />
        <Layout.Container className="pt-4" style={{ marginTop: 20 }} fluid>
          <Text.H3 className="mb-2">Email Invites</Text.H3>
          <SwitchGroup label="Send email invite to participants on launch">
            <FormControl.Switch
              name={"Enable email invitation"}
              checked={props.sendInvite}
              onChange={() => {
                props.setSendInvite((s: any) => !s);
              }}
            />
          </SwitchGroup>

          {props.sendInvite ? (
            <Layout.Row>
              <Layout.Col xl={10}>
                <Text.H3 className="mt-3">Survey invitation email</Text.H3>
                <Pannel>
                  <Layout.Row>
                    <Layout.Col xl={12}>
                      <FormControlTemplate
                        formik={formik}
                        options={{
                          label: "Email Subject",
                          key: "subject",
                        }}
                        input={
                          <>
                            {/* <FormControl.Text
                            name={"subject"}
                            isInvalid={isFieldInvalid(formik, "subject")}
                            onBlur={handleBlur}
                            value={getValue(values, "subject")}
                            onChange={handleChange}
                            placeholder={"You are invited ..."}
                          ></FormControl.Text> */}
                            <Div className="survey-quill-subject">
                              <RichEditor
                                onChange={(html: string, text: string) => {
                                  let val = getComputedValueText(text, false);
                                  val = val.replaceAll("\n", "");
                                  setFieldValue("subject", val);
                                }}
                                value={getComputedValueColorSub(
                                  getComputedValue(
                                    getValue(values, "subject"),
                                    true
                                  )
                                )}
                                insertOptions={(
                                  CUSTOM_EMAIL_INSERT_OPTIONS || []
                                )
                                  .filter(
                                    (el: any) => el.id !== 1 && el.id !== 2 && el.id !== 7 && el.id !== 10
                                  )
                                  .filter((el: any) => {
                                    return !(
                                      survey?.data.type === "SurveyIndexExit" &&
                                      [5, 6, 7, 10].some(
                                        (sel: any) => sel === el.id
                                      )
                                    );
                                  })
                                  .filter((el: any) => {
                                    return !(
                                      survey?.data.type !== "SurveyIndexExit" &&
                                      el.id === 11
                                    );
                                  })}
                                placeholder={"You are invited ..."}
                                disabled={props.viewOnly}
                              />
                              {(isFieldInvalid(formik, "subject") ||
                                !values.subject) && (
                                <Text.P1 className="text-danger">
                                  Subject is a required field
                                </Text.P1>
                              )}
                            </Div>
                          </>
                        }
                      />
                    </Layout.Col>
                  </Layout.Row>
                </Pannel>
                <Pannel>
                  <Layout.Row>
                    <Layout.Col xl={12}>
                      <FormControlTemplate
                        formik={formik}
                        options={{
                          label: "Email Body",
                          key: "body",
                        }}
                        input={
                          <Div className="survey-quill">
                            <RichEditor
                              onChange={onChangeRichText}
                              value={getComputedValue(values.body, true)}
                              insertOptions={(CUSTOM_EMAIL_INSERT_OPTIONS || [])
                                .filter(
                                  (el: any) => el.id !== 7 && el.id !== 10
                                )
                                .filter((el: any) => {
                                  return !(
                                    survey?.data.type === "SurveyIndexExit" &&
                                    [5, 6, 7, 10].some(
                                      (sel: any) => sel === el.id
                                    )
                                  );
                                })
                                .filter((el: any) => {
                                  return !(
                                    survey?.data.type !== "SurveyIndexExit" &&
                                    el.id === 11
                                  );
                                })}
                              disabled={props.viewOnly}
                            />
                            <Div>
                              <FormControl.Textarea
                                className="email-body-foot"
                                disabled
                                value={`This is an automated email. Please do not respond to this email.
If you have any technical issues, please contact our support <NAME_EMAIL>

Unmatched by Survey Research Associates
Professional Development | People Management | Employee Engagement software`}
                                rows={5}
                                style={{
                                  color: "#6D6D6D",
                                  fontSize: 14,
                                  resize: "none",
                                }}
                              />
                              {(isFieldInvalid(formik, "body") ||
                                !values.body) && (
                                <Text.P1 className="text-danger">
                                  Email body is a required field
                                </Text.P1>
                              )}
                            </Div>
                          </Div>
                        }
                      />
                    </Layout.Col>
                  </Layout.Row>
                </Pannel>
              </Layout.Col>
            </Layout.Row>
          ) : (
            <Note>
              <Text.P1 style={{ fontWeight: 500, fontSize: 14 }}>
                <b>Note :</b> It is advised to send out email invites on each
                survey launch as participants will be instantly notified about
                it. If you choose not to send invites now, you can send emails
                post launch from’manage survey’. page.
              </Text.P1>
            </Note>
          )}
        </Layout.Container>
      </FormikAutoSave>
    </Form>
  );
};

export default SendSurvey;
