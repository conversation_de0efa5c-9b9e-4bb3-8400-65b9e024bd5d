import React from "react";
import { useTable, useXHR } from "unmatched/hooks";
import {
  Div,
  PageContainer,
  Text,
  Button,
  Icon,
  Table,
  Layout,
  Placeholder,
  FormControl,
  Sentiment,
  OverlayTrigger,
  Tooltip,
  ComboFilter,
  // ComboMultiFilter,
} from "unmatched/components";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
// import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import { useParams, useHistory } from "react-router-dom";
// import CommentHistory from "./CommentHistory";
import EditComment from "./EditComment";
import {
  getAllComments, //patchBookmark,
  patchReview,
} from "../survey-api";
import { useDebounce } from "react-use";
import { patchCommentFlag } from "pages/AdminApp/Analytics/EngagementAnalytics/engagement-analytics-api";
import styled from "styled-components";
import { getAdminSurveyInfoFactV2 } from "pages/UserApp/Dashboard/dashboard-api";
import icons from "assets/icons/icons";
import useFilter from "pages/CommonFilters/hook";

const { Paste } = icons;

// const COHARTS = [
//   { key: "questions", title: "Questions" },
//   { key: "comments", title: "Comments" },
// ];

// const APPLIED_DATA = [
//   { key: "Option1", title: "Option1" },
//   { key: "Option2", title: "Option2" },
// ];

const ViewComments = () => {
  const params = useParams<any>();
  const history = useHistory();

  const tableMeta = useTable({
    totalPages: 10,
    size: 10,
    page: 1,
  });

  const filtersState = useFilter();

  React.useEffect(() => {
    filtersState.getFilters();
  }, []);

  // const [cohart] = React.useState({
  //   cohart: {
  //     options: COHARTS,
  //     selected: "Question",
  //   },
  //   applied: {
  //     options: APPLIED_DATA,
  //     selected: [],
  //   },
  // });

  // const toastr = useToastr();

  const comments = useXHR({ defaultResponse: [] });

  // const survey = useXHR({
  //   defaultResponse: {
  //     name: "Upward Review March 2020",
  //     id: "",
  //   },
  // });

  const [columnsData, setColumns] = React.useState<any>({
    qNo: { label: "No." },
    comment: { label: "Comment" },
    target_full_name: {
      label: "Receiver",
      hasSort: true,
    },
    sentiment: {
      label: (
        <Div className="position-relative">
          <OverlayTrigger
            placement="bottom"
            overlay={
              <ModTooltip id="button-tooltip">
                <Div className="row">
                  <Div className="col pr-0 pt-2" style={{ maxWidth: 10 }}>
                    <Icon icon="fal fa-info-circle fs-12" />
                  </Div>
                  <Div className="col">
                    <Text.P1 className="text-left fs-10 fw-300 p-2">
                      The sentiments are computed generated using advanced
                      machine learning algorithms, and may not be 100% accurate
                      all the time. Please use your own judgment while reviewing
                      the comments.
                    </Text.P1>
                  </Div>
                </Div>
              </ModTooltip>
            }
          >
            {({ ref, ...triggerHandler }) => (
              <>
                <span className="mr-1">Sentiment</span>
                <i
                  className="far fa-info-circle"
                  ref={ref}
                  {...triggerHandler}
                />
              </>
            )}
          </OverlayTrigger>
        </Div>
      ),
    },
    flag: { label: <Icon icon="far fa-flag text-muted px-2" /> },
    actions: { label: "Actions" },
  });

  // const [viewComment, setViewComment] = React.useState(false);

  const [editComment, setEditComment] = React.useState(false);

  const [selected, setSelected] = React.useState<any>({});

  const [commentsData, setCommentsData] = React.useState<any>([]);

  const [commentMeta, setCommentMeta] = React.useState<any>({
    title: "",
    next: "",
    previous: "",
  });
  // const [isUnreviewed, setUnreviewed] = React.useState(undefined);
  // const [isFlagged, setFlagged] = React.useState(undefined);
  const [searchTerm, setSearchTerm] = React.useState<any>(null);
  const [filters, setFilters] = React.useState<any>({
    isUnreviewed: undefined,
    isEdited: undefined,
    isFlagged: undefined,
    sentiment: undefined,
  });
  const [isEditing, setEditing] = React.useState(false);
  const [pageNav, setPageNav] = React.useState(0);
  const [survey, setSurvey] = React.useState<any>({});

  const getColumns = () => {
    const coumnsList = util.lib.keys(columnsData);
    return util.lib.map(coumnsList, (key: string) => ({
      ...util.lib.get(columnsData, key),
      key,
    }));
  };

  useDebounce(
    () => {
      if (isEditing) return getComments({});
    },
    1500,
    [searchTerm]
  );

  React.useEffect(() => {
    const getSurvey = async (id: any) => {
      const res = await getAdminSurveyInfoFactV2(id);
      setSurvey(res?.data || {});
    };
    getSurvey(params.id);
  }, []);

  const [ordering, setOrdering] = React.useState<undefined | string>(undefined);

  const getComments = (_params?: any, _filters?: any) => {
    comments.setLoading(true);
    getAllComments({
      index_id: params.id,
      question_id: params.qID,
      page_size: 10,
      is_reviewed: filters.isUnreviewed,
      is_edited: filters.isEdited,
      flag: filters.isFlagged,
      sentiment: filters.sentiment,
      // page: _params?.page || tableMeta?.page,
      search: searchTerm?.length > 0 ? searchTerm : undefined,
      // ordering: _params?.ordering ?? ordering,
      ...filtersState.getParams(_filters || filtersState.selected),
      ..._params,
    })
      .then((res: any) => {
        tableMeta.setPagination({
          totalPages: res.totalPages,
          page: _params?.page || 1,
          size: 10,
          totalItems: res.totalElements,
        });
        comments.onSuccess(res.results);
        setCommentsData(res.results);
        setCommentMeta({
          title: res.title,
          next: res.next,
          previous: res.previous,
        });

        setPageNav(pageNav + 1);

        if (editComment && _params.next) {
          onEditComment(res.results[0]);
        }
        if (editComment && _params.prev) {
          onEditComment(res.results[res.results.length - 1]);
        }
      })
      .catch((err: any) => {
        comments.onError(err);
      });
    // getCommentsFact(_params).then(
    //   (response: any) => {
    //     comments.onSuccess(COMMENTS_DATA);
    //   },
    //   (err: any) => {
    //     comments.onError(err);
    //     toastr.onError(err);
    //   }
    // );
  };

  // const downloadComments = () => '';

  // const onViewComment = (item: any) => {
  //   setSelected(item);
  //   setViewComment(true);
  // };

  // const onHideView = () => {
  //   setSelected({});
  //   setViewComment(false);
  // };

  const onEditComment = (item: any) => {
    setSelected(item);
    setEditComment(true);
  };

  const onHideEdit = () => {
    setSelected({});
    setEditComment(false);
  };

  const onPageMount = () => {
    setCommentMeta({
      title: "",
      next: "",
      previous: "",
    });
    setEditing(false);
    setSearchTerm(null);
    getComments();
  };

  React.useEffect(onPageMount, [params, filters]);

  const getHeaderTitleTemplate = () => {
    return (
      <>
        <Text.H1 className="mb-2">{survey.title}</Text.H1>
        {/* <Text.P1 className={util.getUtilClassName({ pb: 2, pt: 1 })}>
          <Link
            className={util.getUtilClassName({ textColor: "dark" })}
            to={util.appUrls.admin.survey.editComments.getQuestions(params.id)}
          >
            <Icon icon="fal fa-chevron-left" /> Back{" "}
          </Link>
        </Text.P1> */}
      </>
    );
  };

  // const getHeaderMetaTemplate = () => {
  //   return (
  //     <Button variant="outline-primary" onClick={downloadComments}>
  //       <Icon icon="fas fa-file-download" /> Download
  //     </Button>
  //   );
  // };

  const sentiments = [
    {
      title: "Positive",
      id: 1,
      value: "POSITIVE",
    },
    {
      title: "Negative",
      id: 2,
      value: "NEGATIVE",
    },
    {
      title: "Neutral",
      id: 3,
      value: "NEUTRAL",
    },
    {
      title: "Mixed",
      id: 4,
      value: "MIXED",
    },
    {
      title: "All",
      id: 5,
      value: undefined,
    },
  ];

  const commentsOptions = [
    {
      title: "Reviewed",
      id: 1,
      value: "review_true",
    },
    {
      title: "Unreviewed",
      id: 2,
      value: "review_false",
    },
    {
      title: "Edited",
      id: 3,
      value: "edit_true",
    },
    {
      title: "Un-edited",
      id: 4,
      value: "edit_false",
    },
    {
      title: "All",
      id: 5,
      value: undefined,
    },
  ];

  const flagsOptions = [
    {
      title: "Flagged",
      id: 1,
      value: true,
    },
    {
      title: "Unflagged",
      id: 2,
      value: false,
    },
    {
      title: "All",
      id: 5,
      value: undefined,
    },
  ];

  const comFilter = () => {
    if (filters.isUnreviewed === null && filters.isEdited === null) {
      // return filters.isEdited ? "Edited" : "Un-edited";
      return "";
    }

    if (filters.isUnreviewed === null) {
      console.log(filters);
      if (filters.isEdited === "true") {
        return "Edited";
      }
      if (filters.isEdited === "false") {
        return "Un-edited";
      }
      // return filters.isEdited ? "Edited" : "Un-edited";
    }
    if (filters.isEdited === null) {
      // return filters.isUnreviewed ? "Reviewed" : "Unreviewed";
      if (filters.isUnreviewed === "true") {
        return "Reviewed";
      }
      if (filters.isUnreviewed === "false") {
        return "Unreviewed";
      }
    }

    // return filters.isEdited ?
    //   ? "Edited"
    //   : filters.isUnreviewed === false
    //   ? "Unreviewed"
    //   : "";
    // return "Reviewied";
  };

  const getFiltersTemplate = () => {
    return (
      <>
        <Div
          className="border rounded-sm mb-3 mt-1 d-flex"
          style={{ background: "#f8fafc" }}
        >
          <Div className="flex-grow-1 p-2">
            <Text.P1>
              {comments.isLoading ? (
                <Placeholder width="col-6" />
              ) : (
                `Q: ${commentMeta.title}`
              )}
            </Text.P1>
          </Div>
          <Button
            variant="outline-primary"
            disabled={commentMeta.previous === null || comments.isLoading}
            onClick={() =>
              history.push(
                util.appUrls.admin.survey.editComments.viewComments(
                  params.id,
                  commentMeta.previous
                )
              )
            }
            style={{ minWidth: 120 }}
            className="border-left border-top-0 border-right-0 border-bottom-0 rounded-0"
          >
            <Icon icon="far mr-3 fa-arrow-left" /> Previous
          </Button>
          <Button
            variant="outline-primary"
            disabled={commentMeta.next === null || comments.isLoading}
            onClick={() =>
              history.push(
                util.appUrls.admin.survey.editComments.viewComments(
                  params.id,
                  commentMeta.next
                )
              )
            }
            style={{ minWidth: 110 }}
            className="border-left border-top-0 border-right-0 border-bottom-0 rounded-0"
          >
            Next <Icon icon="far ml-3 fa-arrow-right" />
          </Button>
        </Div>
        <Div className="justify-content-between row">
          <Div className="col-12 col-lg-3 col-md-12 my-2">
            {/* <FormControl.Checkbox className="mt-1 mr-3">
              <FormControl.Checkbox.Label className="text-black fs-12">
                <Text.P2>Show flagged comments only</Text.P2>
              </FormControl.Checkbox.Label>
              <FormControl.Checkbox.Input
                checked={filters.isFlagged}
                onChange={(evt: any) => {
                  setFlagged(evt.target.checked);
                  // getComments({});
                }}
              />
            </FormControl.Checkbox>
            <FormControl.Checkbox className="mt-1 mr-3">
              <FormControl.Checkbox.Label className="text-black fs-12">
                <Text.P2>Show Unreviewed only</Text.P2>
              </FormControl.Checkbox.Label>
              <FormControl.Checkbox.Input
                checked={isUnreviewed}
                onChange={(evt: any) => {
                  setUnreviewed(evt.target.checked);
                  // getComments({});
                }}
              />
            </FormControl.Checkbox> */}
            <Div style={{ minWidth: 250 }}>
              <FormControl.Search
                placeholder="Search for receiver"
                value={searchTerm}
                onChange={(evt: any) => {
                  setEditing(true);
                  setSearchTerm(evt.target.value);
                }}
              />
            </Div>
          </Div>
          <Div className="col-12 col-lg-9 col-md-12 my-2 d-flex d-flex justify-content-end">
            {/* <ComboMultiFilter
              cohart={cohart.cohart}
              applied={cohart.applied}
              onCohartUpdate={() => ""}
              onAppliedUpdate={() => ""}
            /> */}
            <Icon icon="far fa-filter fs-14 mt-2" />
            <Div style={{ minWidth: 200 }} className="ml-3">
              <Table.CompareFilter
                title="Comments"
                selected={
                  comFilter()
                  // () => {
                  //   // if (filters.isUnreviewed === undefined) {
                  //   //   return filters.isEdited ?
                  //   //     ? "Edited"
                  //   //     : filters.isUnreviewed === false
                  //   //     ? "Unreviewed"
                  //   //     : "";
                  //   // }
                  //   return "Reviewed";
                  // }
                  // filters.isUnreviewed === undefined
                  //   ? "Reviewed"
                  //   : filters.isUnreviewed === false
                  //   ? "Unreviewed"
                  //   : ""
                }
                options={commentsOptions}
                placeholder={"All"}
                onSelect={(item: any) => {
                  if (!item?.value) {
                    return setFilters((_d: any) => {
                      return {
                        ..._d,
                        isUnreviewed: null,
                        isEdited: null,
                      };
                    });
                  }
                  const type = item.value.split("_");

                  if (type[0] === "review") {
                    return setFilters((_d: any) => {
                      return {
                        ..._d,
                        isEdited: null,
                        isUnreviewed: type[1],
                      };
                    });
                  }
                  if (type[0] === "edit") {
                    return setFilters((_d: any) => {
                      return {
                        ..._d,
                        isUnreviewed: null,
                        isEdited: type[1],
                      };
                    });
                  }

                  // console.log(item.value);
                }}
              />
            </Div>

            <Div style={{ minWidth: 200 }} className="ml-3">
              <Table.CompareFilter
                title="Sentiment"
                selected={
                  filters.sentiment
                    ?.split(" ")
                    .map(
                      (w: any) =>
                        w[0].toUpperCase() + w.substring(1).toLowerCase()
                    )
                    .join(" ") || ""
                }
                options={sentiments}
                placeholder={"All"}
                onSelect={(item: any) =>
                  setFilters((_d: any) => {
                    return { ..._d, sentiment: item.value };
                  })
                }
              />
            </Div>
            <Div style={{ minWidth: 150 }} className="ml-3">
              <Table.CompareFilter
                title={<Icon icon="far fa-flag fs-14" />}
                selected={
                  filters.isFlagged
                    ? "Flagged"
                    : filters.isFlagged === false
                    ? "Unflagged"
                    : ""
                }
                options={flagsOptions}
                placeholder={"All"}
                onSelect={(item: any) => {
                  setFilters((_d: any) => {
                    return { ..._d, isFlagged: item.value };
                  });
                }}
              />
            </Div>
          </Div>
        </Div>
      </>
    );
  };

  const onSetFlag = async (id: string, isFlagged: boolean) => {
    const oldData = commentsData;
    const newData = oldData.map((item: any) => {
      if (item.id === id) {
        const obj = Object.assign({}, item);
        obj["isFlagged"] = isFlagged;
        return obj;
      }
      return item;
    });

    try {
      patchCommentFlag({ id, flag: isFlagged });
      setCommentsData(newData);
    } catch (err) {
      setCommentsData(oldData);
    }
    // patchBookmark({ commentId: id })
    //   .then(() => "")
    //   .catch(() => {});
  };

  const onMarkReview = (id: string, comment: string, isReviewed: boolean) => {
    const oldData = commentsData;
    const newData = oldData.map((item: any) => {
      if (item.id === id) {
        const obj = Object.assign({}, item);
        obj["isReviewed"] = isReviewed;
        obj["comment"] = comment;
        return obj;
      }
      return item;
    });
    setCommentsData(newData);

    patchReview({ id, comment, isReviewed })
      .then((data) => {
        if (selected) {
          setSelected({
            ...selected,
            comment,
            editedBy: data.editedBy,
            updatedAt: data.updatedAt,
            isReviewed: data.isReviewed,
          });
        }
      })
      .catch((err) => {
        console.log(err);
        setCommentsData(oldData);
      });
  };

  const onCommentEdit = (comment: string, review: boolean) => {
    onMarkReview(selected.id, comment, review);
  };

  const onSave = async (id: string, comment: string, isReviewed: boolean) => {
    try {
      await onMarkReview(id, comment, isReviewed);
      setSelected({ ...selected, isReviewed });
    } catch (err: any) {
      new Error(err?.message || "Error");
    }
  };
  const getFlagCellTemplate = (item: any) => {
    // const variant = item.isFlagged ? "" : "muted";
    const icon = item.isFlagged ? "fas fa-flag" : "fal fa-flag";
    return <Icon icon={icon} />;
  };

  const getIndex = (id: string) => {
    const index = commentsData.findIndex((obj: any) => obj.id === id);
    return index + 1;
  };
  const onNext = (id: string) => {
    const index = getIndex(id);
    if (
      index === commentsData.length % 10 &&
      tableMeta.totalPages === tableMeta.page
    ) {
      return;
    }
    if (index % 10 === 0) {
      if (tableMeta.totalPages === tableMeta.page) return;
      getComments({ page: tableMeta.page + 1, next: true });
      tableMeta.onPageSelect(tableMeta.page + 1);
      return;
    }
    setPageNav(pageNav + 1);
    return onEditComment(commentsData[index]);
  };

  const onPrev = (id: string) => {
    const index = getIndex(id);
    if (index === 1 && tableMeta.page === 1) {
      return;
    }
    if (index === 1 && tableMeta.page > 1) {
      // if(tableMeta.totalPages === tableMeta.page) return;
      getComments({ page: tableMeta.page - 1, prev: true });
      tableMeta.onPageSelect(tableMeta.page - 1);
      return;
    }
    setPageNav(pageNav + 1);
    return onEditComment(commentsData[index - 2]);
  };
  const isPrev = (id: string) => {
    const index = getIndex(id);
    if (index === 1 && tableMeta.page === 1) {
      return true;
    }
    return false;
  };
  const isNext = (id: string) => {
    const index = getIndex(id);
    if (
      index === commentsData.length % 10 &&
      tableMeta.totalPages === tableMeta.page
    ) {
      return true;
    }
    return false;
  };
  return (
    <PageContainer>
      <CustomHeader
        breadcrumbs={[
          {
            label: "Surveys list",
            icon: <Paste className="grey-icon__svg" />,
            route: `${util.appUrls.admin.survey.default}?filter=ENDED`,
          },
          {
            label: "Comment Review",
            route: util.appUrls.admin.survey.editComments.getQuestions(
              params.id
            ),
          },
          {
            label: "View Comments",
          },
        ]}
        title={getHeaderTitleTemplate()}
        metaItem={<></>}
      />
      <Layout.Container
        fluid
        className={util.getUtilClassName({ pt: 5 })}
        style={{ marginTop: 10 }}
      >
       
        <Div>{getFiltersTemplate()}</Div>
        <Layout.Row className="mt-2">
          <Layout.Col xl={6}>
            <ComboFilter
              filters={filtersState.filters}
              selected={filtersState.selected}
              onFilterSelect={(_selected: any) => {
                filtersState.onSelect(_selected);
                if (
                  !Object.keys(_selected).length ||
                  !Object.values(_selected).every((el: any) => !el.length)
                ) {
                  getComments({}, _selected);
                }
              }}
              onSubmit={() => ""}
            />
          </Layout.Col>
        </Layout.Row>
        
        <Div className="mt-3">
          <Table
            columns={getColumns()}
            isLoading={comments.isLoading}
            type="striped"
            rows={commentsData}
            render={(item: any, index: number) => {
              return (
                <>
                  <Table.Data centered width="20px">
                    <Text.P1>{tableMeta.page * 10 - 10 + index + 1}</Text.P1>
                  </Table.Data>

                  <Table.Data>
                    <Text.P1>{item.comment}</Text.P1>
                  </Table.Data>
                  <Table.Data width="150px">
                    <Text.P1>{item.target}</Text.P1>
                  </Table.Data>
                  <Table.Data width="100px">
                    <Text.P1>
                      <Sentiment sentiment={item.sentiment}></Sentiment>
                    </Text.P1>
                  </Table.Data>
                  <Table.Data centered width="30px">
                    <Text.P1
                      onClick={() => onSetFlag(item.id, !item.isFlagged)}
                    >
                      {getFlagCellTemplate(item)}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data width="220px">
                    <Div className="row m-0">
                      <Div className="col-8 px-0">
                        <Button
                          className={`${util.getUtilClassName({
                            py: 0,
                            px: 0,
                          })}`}
                          onClick={() =>
                            onMarkReview(
                              item.id,
                              item.comment,
                              !item.isReviewed
                            )
                          }
                          variant="link"
                        >
                          <Icon
                            icon={`fal fa-check-circle mr-2 ${
                              item.isReviewed ? "text-success" : "text-primary"
                            }`}
                          />{" "}
                          <span className="text-black fs-12">
                            {item.isReviewed ? "Reviewed" : "Mark as reviewed"}
                          </span>
                        </Button>
                      </Div>
                      <Div className="col-4 pr-0 ">
                        <Button
                          className={`${util.getUtilClassName({
                            py: 0,
                            px: 0,
                          })}`}
                          onClick={() => onEditComment(item)}
                          variant="link"
                        >
                          <Icon icon="fal fa-pencil mr-2" />{" "}
                          <span className="text-black fs-12">Edit</span>
                        </Button>
                      </Div>
                    </Div>
                  </Table.Data>
                </>
              );
            }}
            onPageSelect={(number: number) => {
              tableMeta.onPageSelect(number);
              getComments({ page: number, ordering });
            }}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.key,
                item.sortValue
              );

              setOrdering(label);
              setColumns(tableMeta.resetColumns(columnsData, item) || {});
              // setColumns()

              // util.label.getSortingLabel(item.key, item.sortValue)
              getComments({
                ordering: util.label.getSortingLabel(item.key, item.sortValue),
              });

              // debugger
            }}
            totalItems={tableMeta.totalItems}
          />
        </Div>
      </Layout.Container>
      {editComment ? (
        <EditComment
          show={editComment}
          onHide={onHideEdit}
          selected={selected}
          setSelected={setSelected}
          onCommentChange={onCommentEdit}
          onSave={onSave}
          onNext={onNext}
          onPrev={onPrev}
          loading={comments.isLoading}
          isNext={isNext}
          isPrev={isPrev}
          pageNav={pageNav}
        />
      ) : (
        ""
      )}
      {/* <CommentHistory
        show={viewComment}
        onHide={onHideView}
        selected={selected}
      /> */}
    </PageContainer>
  );
};

// Comments.propTypes = {

// }

export default ViewComments;
const ModTooltip = styled(Tooltip)`
  .tooltip-inner {
    max-width: 400px;
  }
`;
