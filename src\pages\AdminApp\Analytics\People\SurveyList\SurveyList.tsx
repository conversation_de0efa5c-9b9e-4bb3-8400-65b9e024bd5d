import React from "react";
import _ from "lodash";
// import { Link } from "react-router-dom";
import {
  Div,
  FormControl,
  Text,
  Layout,
  Tabs,
  Tab,
  Table,
  PageContainer,
} from "unmatched/components";
// useHistory
import { useTable, useState, useQuery, useXHR } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
// import SURVEYS from "./survey-list-meta";
// import appUrls from "unmatched/utils/urls/app-urls";
import CustomHeader from "../../../Shared/CustomHeader/CustomHeader";
import { getSurveysFact } from "pages/AdminApp/Survey/survey-api";
import util from "unmatched/utils";
// import { Link } from "react-router-dom";

const SURVEY_TYPES = {
  ON_GOING: "SURVEYS",
  // ENDED: "ENDED",
};

const SurveyList = () => {
  // const history = useHistory();

  const queryParams = useQuery();
  const tableMeta = useTable({
    totalPages: 0,
    size: 10,
    page: 1,
  });
  React.useEffect(() => {
    getSurveys();
  }, []);

  const surveyData = useXHR({ defaultResponse: [] }, "surveys");
  const toastr = useToastr();
  const [params, setParams] = useState({
    search: "",
    filter: queryParams.get("filter") || SURVEY_TYPES.ON_GOING,
    sort: [],
    size: 0,
    page: 1,
    totalElements: 20,
    totalPages: 4,
  });

  // const [surveys] = useState(SURVEYS);

  const [columnsData, setColumnsData] = useState<any>({
    sNo: { label: "No." },
    title: { label: "Title", hasSort: true, sortValue: "asc" },
    startDate: { label: "Start Date", hasSort: true, sortValue: "dsc" },
    endDate: { label: "End Date", hasSort: true, sortValue: "dsc" },
    // activeUsers: { label: "Total Eligible", hasSort: true, sortValue: "" },
    // inactiveUsers: { label: "Total Reports", hasSort: true, sortValue: "" },
  });

  const getSurveys = (params?: any) => {
    surveyData.setLoading(true);
    getSurveysFact(
      {
        // search,
        filter: "ENDED",
        page: tableMeta.page,
        size: tableMeta.size,
        ...(params || {}),
      },
      surveyData.getMetaConfig()
    ).then(
      ({ data, totalPages }: any) => {
        tableMeta.updatePagination({ totalPages });
        surveyData.setLoading(false);
        surveyData.setSurveys(data);
      },
      (err: any) => {
        surveyData.onError(err);
        toastr.onError(err);
      }
    );
  };

  const getColumns = () => {
    const coumnsList = _.keys(columnsData);
    return _.map(coumnsList, (key: string) => ({
      ..._.get(columnsData, key),
      key,
    }));
  };

  const onFilterChange = () => {
    // history.push(appUrls.admin.analytics.statistics.getListUrl(filter));
    // setParams((_params: any) => ({
    //   ..._params,
    //   filter,
    // }));
  };

  const onSearchChange = (search: string) => {
    setParams((_params: any) => ({
      ..._params,
      search,
    }));
  };

  // const onPageChange = (page: number) => {
  //   setParams((_params: any) => ({
  //     ..._params,
  //     page,
  //   }));
  // };

  const getFilterLink = (key: string, title: string) => {
    return title;
  };

  const getTabsTemplate = () => {
    return (
      <Div className="custom-tabs-2">
        <Tabs activeKey={params.filter} onSelect={() => onFilterChange()}>
          <Tab
            eventKey={SURVEY_TYPES.ON_GOING}
            title={getFilterLink(SURVEY_TYPES.ON_GOING, "Surveys")}
          ></Tab>
        </Tabs>
      </Div>
    );
  };

  const getSearchInput = () => {
    return (
      <FormControl.Search
        placeholder="Search for surveys"
        value={params.search}
        onChange={(evt: any) => onSearchChange(evt.target.value)}
      />
    );
  };

  return (
    <PageContainer>
      <CustomHeader
        title={<Text.H1 className="pb-2">People Analytics</Text.H1>}
        metaItem={getSearchInput()}
      />
      <Layout.Container fluid className="pt-3">
        <Div>{getTabsTemplate()}</Div>
        <Div className="pt-4">
          <Table
            isLoading={surveyData.isLoading}
            columns={getColumns()}
            type="striped"
            rows={surveyData.surveys}
            render={(item: any, index: number) => {
              return (
                <>
                  <Table.Data width="30px">
                    <Text.P1>{index + 1}</Text.P1>
                  </Table.Data>
                  <Table.Data style={{ width: "70%" }}>
                    <Text.P1 className="text-black">
                      {/* <Link
                        to={appUrls.admin.analytics.people.getListView(item.id)}
                      >
                        {item.title}
                      </Link> */}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data width="150px">
                    <Text.P1>
                      {util.date.getBrowserTime(item.startDate)}
                    </Text.P1>
                  </Table.Data>
                  <Table.Data width="150px">
                    <Text.P1>{util.date.getBrowserTime(item.endDate)}</Text.P1>
                  </Table.Data>
                </>
              );
            }}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.key,
                item.sortValue
              );
              setColumnsData((_columns: any) =>
                tableMeta.resetColumns(_columns, item)
              );
              getSurveys({ sort: label });
            }}
            onPageSelect={(number: number) => {
              tableMeta.onPageSelect(number);
              getSurveys({ page: number });
            }}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
          />
        </Div>
      </Layout.Container>
    </PageContainer>
  );
};

export default SurveyList;
