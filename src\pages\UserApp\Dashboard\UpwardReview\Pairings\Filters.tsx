import React, { useContext } from "react";
import {
  Div,
  Accordion,
  Text,
  Layout,
  Icon,
  FormGroup,
  FormControl,
} from "unmatched/components";
import { useAccordionToggle } from "react-bootstrap/AccordionToggle";
import AccordionContext from "react-bootstrap/AccordionContext";
import styled from "styled-components";
const Filters = () => {
  const accordionType = [
    {
      id: 1,
      name: "Location",
    },
    {
      id: 2,
      name: "Department",
    },
    {
      id: 3,
      name: "Practice Group",
    },
    {
      id: 4,
      name: "Title",
    },
  ];
  return (
    <Div>
      <Layout.Row className="mx-0 mt-3 mb-2 px-1">
        <Layout.Col className="text-left px-3" xs={12}>
          <Text.H3 className="text-secondary-title  fs-12">
            Filter Contacts By
          </Text.H3>
        </Layout.Col>
      </Layout.Row>
      <Accordion>
        {accordionType.map((data) => (
          <>
            <ContextAwareToggle eventKey={`${data.id}`}>
              {data.name}
            </ContextAwareToggle>
            <Accordion.Collapse eventKey={`${data.id}`} className="px-1">
              <Div className="px-3">
                <FormGroup className="px-1">
                  <FormControl.Radio>
                    <FormControl.Radio.Label className="fs-12 pt-0">
                      Option 1
                    </FormControl.Radio.Label>
                    <FormControl.Radio.Input />
                  </FormControl.Radio>
                  <FormControl.Radio>
                    <FormControl.Radio.Label className="fs-12 pt-0">
                      Option 2
                    </FormControl.Radio.Label>
                    <FormControl.Radio.Input />
                  </FormControl.Radio>
                  <FormControl.Radio>
                    <FormControl.Radio.Label className="fs-12 pt-0">
                      Option 3
                    </FormControl.Radio.Label>
                    <FormControl.Radio.Input />
                  </FormControl.Radio>
                  <FormControl.Radio>
                    <FormControl.Radio.Label className="fs-12 pt-0">
                      Option 4
                    </FormControl.Radio.Label>
                    <FormControl.Radio.Input />
                  </FormControl.Radio>
                </FormGroup>
              </Div>
            </Accordion.Collapse>
          </>
        ))}
      </Accordion>
    </Div>
  );
};
function ContextAwareToggle({ children, eventKey, callback }: any) {
  const currentEventKey = useContext(AccordionContext);

  const decoratedOnClick = useAccordionToggle(
    eventKey,
    () => callback && callback(eventKey)
  );

  const isCurrentEventKey = currentEventKey === eventKey;

  return (
    <ToggleButton
      // style={{ backgroundColor: isCurrentEventKey ? "pink" : "lavender" }}
      onClick={decoratedOnClick}
    >
      <Layout.Row>
        <Layout.Col className="text-left" xs={8}>
          {children}
        </Layout.Col>
        <Layout.Col className="text-right">
          <Icon
            icon={`far ${isCurrentEventKey ? "fa-angle-up" : "fa-angle-down"}`}
          />
        </Layout.Col>
      </Layout.Row>
    </ToggleButton>
  );
}

const ToggleButton = styled.button`
  width: 100%;
  background: none;
  color: #518cff;
  border: none;
  padding: 0 20px;
  font-size: 13px;
  margin-top: 10px;
  &:focus {
    outline: none;
  }
`;
export default Filters;
