import React from "react";
import { Div, Text, Button } from "unmatched/components";
import { userFriendlyRecipientsMap } from "../Email";

export const ConfirmEmailSend = (props: any) => {
  return (
    <Div className="p-4">
      <Text.P1 style={{ fontSize: 14 }} className="pb-2">
        <b>Confirm Sending reminder emails to participants</b>
      </Text.P1>
      <Text.P1 style={{ fontSize: 14 }} className="pb-4">
        You are about to send reminder emails to participants of the survey.
        Confirm and proceed to send the emails. This action can’t be undone.
      </Text.P1>
      <Div className="mt-20" />
      {!props.targetGroupArr?.find((tg: any) => tg.value === "CUSTOM") ? (
        <Div>
          {props.targetGroupArr.map((tg: any) => (
            <Text.P1
              style={{ fontSize: 14 }}
              key={tg.label}
              className="fw-500 target-details"
            >
              All{" "}
              <span
                style={{ display: "contents", color: "#518CFF" }}
                className="fw-600"
              >
                {(userFriendlyRecipientsMap as any)[tg.value]}
              </span>{" "}
              Paricipants{" "}
              <span>
                {
                  props.recipientsCountArr?.find?.(
                    (rc: any) => rc.recipient === tg.value
                  )?.count
                }{" "}
                in total
              </span>
            </Text.P1>
          ))}
        </Div>
      ) : (
        <Div className="detailWrap">
          <Div className="customParticipantsWrap d-flex align-items-center justify-content-center">
            <Text.P1 style={{ fontSize: 14 }} className="customParticipant">
              <span style={{ fontWeight: 600 }}>
                {props.selectedEmails?.length}
              </span>{" "}
              Custom Participants
            </Text.P1>
          </Div>
          <Div>
            {props.selectedEmails?.length === 0 && (
              <Text.P1 style={{ fontSize: 14 }} className="pl-2">
                <b>No custom participants selected</b>
              </Text.P1>
            )}
            {props.selectedEmails?.map((participant: any) => {
              return (
                <React.Fragment key={participant?.label}>
                  <Div className="d-flex participantLi">
                    <Text.P1 style={{ width: 130, fontSize: 14 }}>
                      <b>
                        {participant?.first_name} {participant?.last_name}
                      </b>
                    </Text.P1>
                    <Text.P1 style={{ fontSize: 14 }} className="ml-3">
                      {participant?.label}
                    </Text.P1>
                  </Div>
                  <hr
                    className="d-flex align-items-center"
                    style={{ margin: "0px 10px", background: "#DADADA" }}
                  />
                </React.Fragment>
              );
            })}
          </Div>
        </Div>
      )}
      <Div style={{ marginTop: 20, textAlign: "right" }}>
        <Button
          onClick={() => {
            props.setShowModal(false);
            props.onMockSend();
          }}
          variant="outline-primary"
          className="mr-3"
        >
          Mock Send to myself
        </Button>
        <Button
          onClick={() => {
            props.setShowModal(false);
            props.onSend();
          }}
        >
          Send Email
        </Button>
      </Div>
    </Div>
  );
};
