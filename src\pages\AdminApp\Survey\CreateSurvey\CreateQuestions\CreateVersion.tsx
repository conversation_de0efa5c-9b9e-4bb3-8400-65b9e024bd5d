import React from "react";
import { useFormik } from "unmatched/hooks";
// import surveyCore from "unmatched/survey/creation/components";
import util from "unmatched/utils";
import {
  getWavierQuestionFact,
  patchSurveyVersionFact,
} from "../../survey-api";
import CreateVersionComponent from './CreateVersionLocal';

// const CreateVersionComponent = surveyCore.CreateVersion;

const { yup } = util.formik;

// import { useCreateSurveyContext } from "../Provider";
// import PropTypes from 'prop-types'

const CreateVersion = (props: any) => {
  const { version, onUpdate, updateValidators, setLoading, isUpwardReview, survey } =
    props;
  const [wavier, setWavier]: any = React.useState({});
  const initialValues = {
    label: version.label || "",
    raterGroup: "",
    targetGroup: "",
    minResponses: version.minResponses || 0,
  };
  const upwardValidations: any = isUpwardReview
    ? {
        raterGroup: yup.string(),
        targetGroup: yup.string(),
        minResponses: yup.string(),
      }
    : {};

  const formikOptions = {
    initialValues,
    validationSchema: yup.object().shape({
      label: yup.string().required(),
      ...upwardValidations,
    }),
    onSubmit: () => {
      // on submit
    },
  };

  const formik = useFormik(formikOptions);

  const getWavierQuestion = () => {
    if (!version.eligibilityId) return;
    getWavierQuestionFact(version.eligibilityId).then((response: any) => {
      setWavier(response);
    });
  };

  React.useEffect(() => {
    const key = `version-${version.id}`;
    updateValidators({
      key,
      validate: () => util.formik.formikSubmitForm(formik),
    });
    return () => {
      updateValidators({
        key,
        validate: null,
      });
    };
  }, []);

  React.useEffect(() => {
    getWavierQuestion();
  }, [version.eligibilityId]);
  
  return (
    <>
      <CreateVersionComponent
        patchSurveyVersionFact={patchSurveyVersionFact}
        formik={formik}
        onUpdate={onUpdate}
        setLoading={setLoading}
        wavier={wavier}
        version={version}
        permissions={{
          hasVisibility: isUpwardReview,
        }}
        setWavier={setWavier}
        getWavierQuestion={getWavierQuestion}
        survey={survey}
      />
    </>
  );
};

// CreateVersion.propTypes = {

// };

export default CreateVersion;
