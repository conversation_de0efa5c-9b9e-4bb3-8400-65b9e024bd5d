import React from "react";
import { CustomModal as Modal } from "unmatched/components";
// import AddEmployeeScreen1 from "./RequiredFields";
import AddEmployeeScreen2 from "./UploadFile";
import AddEmployeeScreen3 from "./PublishFile";

export default function AddEmployeeModal(props: any) {
  // let screen: number = props.screen;
  const [screen, setScreen] = React.useState<number>(1);
  const ScreenView = (inPosition: number, props: any) => {
    switch (screen) {
      // case 1:
      //   return <AddEmployeeScreen1 {...props} setScreen={setScreen}/>;
      case 1:
        return <AddEmployeeScreen2 {...props} setScreen={setScreen} />;
      case 2:
        return <AddEmployeeScreen3 {...props} setScreen={setScreen} />;
      default:
        return "";
    }
  };
  return (
    <Modal
      {...props}
      children={
        <>
          <Modal.Header closeButton>
            <Modal.Title>Add more employees</Modal.Title>
          </Modal.Header>
          {ScreenView(screen, props)}
        </>
      }
    />
  );
}
