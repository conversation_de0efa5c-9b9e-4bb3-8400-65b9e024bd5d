// import { questionsSelector } from './questions-selector';
// import { AxiosResponse } from "axios";
import _ from "lodash";
import util from "unmatched/utils";
import { getSurveyQestionsMetaFact } from "../create-survey-api";
import { questionsSelector, sectionsSelector } from "./questions-selector";
// import { QUESTION } from "../../survey-enums";

const { mutateList } = util.store;

export const TYPES = {
  META_GET: "app/admin/survey/create/meta",
  META_LOAD: "app/admin/survey/create/meta/load",
  META_GET_SUCCESS: "app/admin/survey/create/meta/success",
  META_GET_FAILURE: "app/admin/survey/create/meta/fail",
  SET_SECTION: "app/admin/survey/create/set-section",
  SET_QUESTION: "app/admin/survey/create/set-question",
  SET_RANKING: "app/admin/survey/create/question/ramking",
  SET_DROPDOWN: "app/admin/survey/create/question/dropdown",
  SET_CHECKBOX: "app/admin/survey/create/question/checkbox",
  SET_RADIO: "app/admin/survey/create/question/radio",
  SET_INPUT: "app/admin/survey/create/question/input",
  RESET: "app/admin/survey/create/questions/reset",
};

export const getCreateQuestionsMetaAction = (surveyId: number) => {
  return () => {
    // dispatch(toggleLoaderAction(true));
    getSurveyQestionsMetaFact({ surveyId }).then(
      () => {
        // dispatch(setQuestionsMetaAction(response.data.payload));
      },
      () => {
        // dispatch({ type: TYPES.META_GET_FAILURE, payload: error });
      }
    );
  };
};

export const toggleLoaderAction = (loader: boolean) => {
  return { type: TYPES.META_LOAD, payload: loader };
};

export const resetQuestionsAction = () => {
  return { type: TYPES.RESET };
};

// Sections CRUD

export const saveSectionAction = (section: any) => {
  return (dispatch: any, getState: Function) => {
    const sectionKey = `section${section.id}`;
    const sections = sectionsSelector(getState());

    sections.content[sectionKey] = section;
    sections.list = mutateList.add(sections.list, section.id);

    dispatch({
      type: TYPES.SET_SECTION,
      payload: sections,
    });
  };
};

export const updateSectionAction = (section: any) => {
  const sectionKey = `section${section.id}`;
  return {
    type: TYPES.SET_SECTION,
    payload: {
      content: {
        [sectionKey]: section,
      },
    },
  };
};

export const deleteSectionAction = (section: any) => {
  return (dispatch: any, getState: Function) => {
    const sectionKey = `section${section.id}`;

    const sections = sectionsSelector(getState());
    const sectionIndex = _.findIndex(
      sections.list,
      (item: number) => item === section.id
    );

    sections.list = mutateList.remove(sections.list, sectionIndex);
    sections.content = _.omit(sections.content, sectionKey);

    dispatch({
      type: TYPES.SET_SECTION,
      payload: sections,
    });
  };
};

// Questions CRUD

export const saveQuestionAction = (question: any) => {
  return (dispatch: any, getState: Function) => {
    const sectionKey = `section${question.sectionId}`;
    const questionKey = `question${question.id}`;
    const questions = questionsSelector(getState());
    const sections = sectionsSelector(getState());
    const section = sections.content[sectionKey];

    section.questions = mutateList.add(section.questions, question.id);

    questions.list = mutateList.add(questions.list, question.id);
    questions.content[questionKey] = question;

    dispatch({
      type: TYPES.SET_QUESTION,
      payload: questions,
    });
    dispatch(updateSectionAction(section));
  };
};

export const updateQuestionAction = (question: any) => {
  const key = `question${question.id}`;
  return {
    type: TYPES.SET_QUESTION,
    payload: {
      content: {
        [key]: question,
      },
    },
  };
};

export const deleteQuestionAction = (question: any) => {
  return (dispatch: any, getState: Function) => {
    const sectionKey = `section${question.sectionId}`;
    const questionKey = `question${question.id}`;
    const questions = questionsSelector(getState());
    const sections = sectionsSelector(getState());
    const section = sections.content[sectionKey];

    const questionIndex = _.findIndex(
      questions.list,
      (item: number) => item === question.id
    );
    const sectionQuestionIndex = _.findIndex(
      section.questions,
      (item: number) => item === question.id
    );

    questions.content = _.omit(questions.content, questionKey);
    questions.list = mutateList.remove(questions.list, questionIndex);
    section.questions = mutateList.remove(
      section.questions,
      sectionQuestionIndex
    );
    dispatch({
      type: TYPES.SET_QUESTION,
      payload: questions,
    });
    dispatch(updateSectionAction(section));
  };
};

// Options CRUD

// export const saveOptionAction = (type: string, data: any) => {
//   return (dispatch: any, getState: Function) => {
//     switch (type) {
//       case QUESTION.DROPDOWN:
//         dispatch({
//           type: TYPES.SET_DROPDOWN,
//           payload: {
//             options: {
//               content: {
//                 [`option${data.id}`]: data,
//               },
//             },
//           },
//         });
//         return;
//       default:
//         break;
//     }
//   };
// };

// export const updateOptionAction = (type: string, data: any) => {
//   return (dispatch: any, getState: Function) => {
//     switch (type) {
//       case QUESTION.DROPDOWN:
//         dispatch({
//           type: TYPES.SET_DROPDOWN,
//           payload: {
//             options: {
//               content: {
//                 [`option${data.id}`]: data,
//               },
//             },
//           },
//         });
//         return;
//       default:
//         break;
//     }
//   };
// };

export const deleteOptionAction = () => {
  return {
    type: "",
    payload: "",
  };
};

// export const onRankChangeAction = (questionId: number, value: any) => {
//   return {
//     type: '',
//     payload: '',
//   };
// };
