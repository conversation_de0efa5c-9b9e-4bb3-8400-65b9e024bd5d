import React from "react";
import { Div, Form, Text, FormControl, FormGroup } from "unmatched/components";

export const SurveyOptions = (props: any) => {
  // const [options, setOptions] = React.useState([
  //   { id: 1,  }
  // ]);
  return (
    <Div className="p-3 bg-light h-100">
      <Text.H2 className="pb-3">Show</Text.H2>
      <Form>
        <FormControl.Radio>
          <FormControl.Radio.Input
            checked={props.options.type === "upward"}
            onChange={() => {
              props.options.onChange && props.options.onChange("upward");
            }}
          />
          <FormControl.Radio.Label>Upward</FormControl.Radio.Label>
        </FormControl.Radio>
        <FormControl.Radio>
          <FormControl.Radio.Input
            checked={props.options.type === "360"}
            onChange={() => {
              props.options.onChange && props.options.onChange("360");
            }}
          />
          <FormControl.Radio.Label>360</FormControl.Radio.Label>
        </FormControl.Radio>
        <FormGroup className="ml-4">
          {props.options.type === "360" &&
            props.options.options.map((item: any) => {
              return (
                <FormControl.Checkbox className="my-1" key={item}>
                  <FormControl.Checkbox.Input
                    checked={props.options.selected.includes(item)}
                    onChange={() => {
                      let payload = [];
                      const { selected } = props.options;
                      // props.options.selected.forEach((o: any) => {
                      //   if (props.options.selected)
                      // })
                      if (selected.includes(item)) {
                        payload = selected.filter((o: any) => o !== item);
                      } else {
                        payload = [...selected, item];
                      }
                      props.options.onGroupChange &&
                        props.options.onGroupChange(payload);
                    }}
                  />
                  <FormControl.Checkbox.Label>
                    {item}
                  </FormControl.Checkbox.Label>
                </FormControl.Checkbox>
              );
            })}
        </FormGroup>
      </Form>
    </Div>
  );
};
