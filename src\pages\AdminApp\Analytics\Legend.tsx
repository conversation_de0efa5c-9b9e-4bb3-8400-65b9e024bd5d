import React from "react";
import { Layout, Text } from "unmatched/components";
// import PropTypes from 'prop-types'

const Legend = (props: any) => {
  // const items = [
  //   { id: 1, title: "Never" },
  //   { id: 2, title: "Rarely" },
  //   { id: 3, title: "Occasionally" },
  //   { id: 4, title: "Most Times" },
  //   { id: 5, title: "Always" },
  // ];

  return (
    <Layout.Flex className="py-2 justify-content-center">
      {/* <Text.H2 className="pb-3">Legend</Text.H2> */}

      {/* <Text.P1 className="text-muted pr-2">Scale</Text.P1> */}
      {(props.items || []).map((item: any) => {
        return (
          <Layout.FlexItem className="pr-5">
            <Text.P1 className="text-muted" key={item.id}>
              {item.id} - {item.title}
            </Text.P1>
          </Layout.FlexItem>
        );
      })}
    </Layout.Flex>
  );
};

// Legend.propTypes = {

// }

export default Legend;
