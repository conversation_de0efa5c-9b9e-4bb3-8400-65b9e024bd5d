import React from "react";
import util from "unmatched/utils";
import { useCreateSurveyContext } from "../Provider";
import AddUsers from "./AddUsers/AddUsers";
import ManualAddParticipants from "./ManualAddParticipants/ManualAddParticipants";
import PairingsList from "./PairingsManagement/PairingsList";
import ParticipantList from "./UserManagement/ParticipantManagement";

const AddParticipants = (props: any) => {
  const { survey = props.survey } = useCreateSurveyContext();
  const { enums } = util;
  if (!survey.data) return null;
  const getComp = () => {
    if (
      survey.data.type === enums.Survey.Upward ||
      survey.data.type === enums.Survey._360Degree
    ) {
      return (
        <PairingsList
          breadcrumbs={props.breadcrumbs}
          survey={survey}
          viewOnly={props.viewOnly}
          canDelete={props.canDelete ?? true}
        />
      );
    } else if (survey.data.type === enums.Survey.Exit) {
      return <ManualAddParticipants />;
    } else if (
      survey.data.type === enums.Survey.Engagement ||
      survey.data.type === enums.Survey.Self
    ) {
      return (
        <ParticipantList
          breadcrumbs={props.breadcrumbs}
          survey={survey}
          viewOnly={props.viewOnly}
          canDelete={props.canDelete ?? true}
        />
      );
    }
    return (
      <>
        <AddUsers
          breadcrumbs={props.breadcrumbs}
          viewOnly={props.viewOnly}
          auxiliary={survey.data.upwardTitle}
          // doLoad=
        />
      </>
    );
  };
  return <div id="add-participants-root">{getComp()}</div>;
};

export default AddParticipants;
