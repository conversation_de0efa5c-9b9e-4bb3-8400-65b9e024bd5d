import React, { useState } from "react";
import {
  Text,
  Layout,
  Tab,
  Nav,
  Div,
  Button,
  Table,
  Dropdown,
} from "unmatched/components";
import { useParams, useTable } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import Header from "../../Header";
// import { useCreateSurveyContext } from "../../Provider";
// import AddParticipants from "../AddParticipants";
// import { Empty } from "../AddUsers/AddUsers";
import AddParticipants from "./AddParticipants/AddParticipants";
import AllParticipantsList from "./AllParticipantList/AllParticipantsList";
// import AddPairings from "../AddPairings/AddPairings";
// import AllPairingsList from "./AllPairingsList/AllPairingsList";
// import FilesList from "./FilesList/FilesList";
import { keys, map, get } from "lodash";
import {
  deleteParticipantFileFact,
  getAllParticipantFileFact,
} from "./participants-api";
import { util } from "@unmatchedoffl/ui-core";
import { StyledActionsDropdown } from "pages/AdminApp/Survey/SurveyList/SurveysView";
import { OptionButton } from "../PairingsManagement/FilesList/FilesList";
// import { StyledActionsDropdown } from "../EmployeeDataList/EmployeeDataList";

const ParticipantTab = [
  { key: "files", label: "Files" },
  { key: "list", label: "All Participants" },
];

export default function ParticipantList(props: any) {
  // const { survey = props.survey } = useCreateSurveyContext();
  const { id } = useParams<any>();
  const toastr = useToastr();
  const tableMeta = useTable({});
  // const history = useHistory();

  const [activeTab, setActiveTab] = useState(ParticipantTab[0].key);
  const [show, setShow] = useState(false);

  // const [filters, setFilters] = React.useState({
  //   search: "",
  //   page: 1,
  //   totalPages: 4,
  // });
  const [isLoading, setLoading] = React.useState(true);
  const [counter, setCounter] = React.useState(0);
  const [participantData, setParticipantData] = useState([]);

  const getColumnsData = () => {
    return [
      // {
      //   key: 1,
      //   renderItem: getCheckAllTemplate,
      //   hasSort: false,
      // },
      { key: 2, label: "No.", hasSort: false },
      {
        key: 3,
        label: "Title",
        hasSort: true,
        sortKey: "title",
        sortValue: "asc",
      },
      {
        key: 4,
        label: "Total Records",
        hasSort: true,
        sortKey: "records",
        sortValue: "asc",
      },
      { key: 5, label: "Tags", hasSort: false },
      {
        key: 6,
        label: "Uploaded On",
        hasSort: true,
        sortKey: "uploaded_on",
        sortValue: "asc",
      },
      {
        key: 7,
        label: "Last Updated On",
        hasSort: true,
        sortKey: "last_uloaded_on",
        sortValue: "asc",
      },
      { key: 8, label: "Action", hasSort: false },
    ];
  };

  React.useEffect(() => {
    if (!show) {
      getParticipantFiles();
    }
  }, [show]);

  const getParticipantFiles = async (params?: any) => {
    setLoading(true);
    try {
      const response = await getAllParticipantFileFact(
        props.survey.data.type === "SurveyIndexSelf" ? "self" : "engagement",
        {
          page_size: 10,
          // ordering,
          ...params,
          index_id: id,
        }
      );
      tableMeta.updatePagination({ totalPages: response.totalPages });
      setParticipantData(response.results);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.log(err);
    }
  };
  const [columnsData, setColumnsData] = React.useState<any>(getColumnsData());

  const onPageSelect = async (page: number) => {
    try {
      const response = await getAllParticipantFileFact(
        props.survey.type === "SurveyIndexSelf" ? "self" : "engagement",
        {
          page,
          // search: "",
          page_size: 10,
          ordering: props.ordering,
          index_id: id,
        }
      );
      setParticipantData(response.results);
      tableMeta.onPageSelect(page);
    } catch (e: any) {}
  };

  const getColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const onParticipantFileDelete = async (file_id: string) => {
    try {
      await deleteParticipantFileFact(file_id);
      toastr.onSucces({
        title: "Success",
        content: "Pair File Deleted Successfully",
      });
      getParticipantFiles();
    } catch (error: any) {
      toastr.onError(error);
      // console.log(error);
    }
  };

  const getRowsTemplate = () => {
    return participantData.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          // onClick={() =>
          //   history.push(appUrls.admin.dataLoad.getManagePairingsUrl(item.id))
          // }
          even={!isEven}
          key={index}
        >
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data width="40%">
            <Text.P1>
              {item.title ?? <i className="text-muted">No Name</i>}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.records}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.tags.length > 0 ? (
                item.tags.join(", ")
              ) : (
                <span className="text-muted">-</span>
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.uploaded_on,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.updated_at,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1 className="text-center">
              <StyledActionsDropdown
                onClick={(e: any) => e.stopPropagation()}
                style={{ position: "absolute" }}
              >
                <OptionButton
                  className="d-flex align-items-center justify-content-center py-0 px-3 no-shadow"
                  id={"dropdown-button-drop-start"}
                  style={{ height: "100%", boxShadow: "none", border: 0 }}
                >
                  ...
                </OptionButton>

                <Dropdown.Menu
                  align="right"
                  className="text-right shadow-lg"
                  style={{ zIndex: 1 }}
                >
                  {item.error_log_file && (
                    <Dropdown.Item
                      href="#"
                      // onClick={() =>
                      // downloadPairFileInfoFact(item.id, "ERROR", () =>
                      //   toastr.errorToast("No Rejected Records Found")
                      // )
                      // void;
                      // }
                      onClick={() => window.open(item.error_log_file)}
                    >
                      Rejected Records
                    </Dropdown.Item>
                  )}
                  <Dropdown.Item
                    href="#"
                    // onClick={() => downloadParticipantFileInfoFact(item.file)}
                    onClick={() => window.open(item.file)}
                  >
                    Download
                  </Dropdown.Item>
                  <Dropdown.Item
                    className="text-danger"
                    href="#"
                    // onClick={() => onPairFileDelete(item.id)}
                    onClick={() => onParticipantFileDelete(item.id)}
                  >
                    Delete
                  </Dropdown.Item>
                </Dropdown.Menu>
              </StyledActionsDropdown>
            </Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const getTabsTemplate = () => {
    return (
      <Div className="custom-tabs-2">
        <Tab.Container activeKey={activeTab}>
          <Nav className="nav-tabs">
            {ParticipantTab.map((tab: any) => (
              <Nav.Item key={tab.key}>
                <Nav.Link
                  as={Div}
                  eventKey={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className="cursor-pointer"
                >
                  {tab.label}
                </Nav.Link>
              </Nav.Item>
            ))}
          </Nav>
          <Tab.Content>
            <Tab.Pane eventKey={"files"}>
              <Text.P2 className="mt-3 user-select-none">
                Click on 'Add Participants' button to add participants to the
                survey.
              </Text.P2>

              <Div
                style={{
                  minHeight: "calc(100vh - 250px)",
                }}
                className="my-3"
              >
                {/* <AddUsers
                  breadcrumbs={props.breadcrumbs}
                  viewOnly={props.viewOnly}
                  auxiliary={survey.data.upwardTitle}
                  title="Participants"
                  showButton={true}
                  button={<Button onClick={() => setShow(true)}>Add Participants</Button>}
                /> */}
                <Table
                  columns={getColumns()}
                  rows={participantData}
                  customRows
                  render={() => getRowsTemplate()}
                  hasPagination
                  activePage={tableMeta.page}
                  pages={tableMeta.totalPages}
                  onPageSelect={onPageSelect}
                  isLoading={isLoading}
                  onSort={(item: any) => {
                    const label = util.label.getSortingLabel(
                      item.sortKey,
                      item.sortValue
                    );
                    setColumnsData((_columns: any) => {
                      return Object.values(
                        tableMeta.resetColumns(_columns, item)
                      );
                    });
                    props.setOrdering(label);
                    props.dataCall({ pairOrdering: label });
                  }}
                  // {...(filters.search && {
                  //   notFoundMsg: util.noSearchRecordsFoundMsg,
                  // })}
                />
                {/* {!isLoading && participantData.length === 0 && <Empty />} */}
              </Div>
              {/* <AddParticipants /> */}
            </Tab.Pane>
            <Tab.Pane eventKey={"list"}>
              {/* <AllPairingsList show={show} survey={props.survey?.data} /> */}
              <AllParticipantsList
                show={false}
                survey={props.survey?.data}
                counter={counter}
              />
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Div>
    );
  };

  return (
    <>
      <Header
        metaItem={
          <Layout.Flex>
            <Layout.FlexItem className="pl-3">
              {/* {props.survey?.data?.isLocked ? null : ( */}
              <Button onClick={() => setShow(true)}>Add Participants</Button>
              {/* )} */}
            </Layout.FlexItem>
          </Layout.Flex>
        }
        title={<Text.H1 className="pb-2">Participants</Text.H1>}
        breadcrumbs={props.breadcrumbs}
      />
      <Layout.Container fluid className="pt-3">
        <Div>{getTabsTemplate()}</Div>
      </Layout.Container>
      <AddParticipants
        size="lg"
        onHide={() => {
          getParticipantFiles();
          setShow(false);
          setCounter(counter + 1);
        }}
        aria-labelledby="contained-modal-title-vcenter"
        centered
        dialogClassName="modal-90w"
        backdrop="static"
        show={show}
        survey={props.survey}
      />
    </>
  );
}
