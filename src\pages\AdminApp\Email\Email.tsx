import React, { useCallback, useEffect, useState } from "react";
import {
  Div,
  Layout,
  Text,
  Card,
  Button,
  FormGroup,
  FormControl,
  Form,
  PageContainer,
  MultiSelect,
  RichEditor,
} from "unmatched/components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import { CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS } from "./meta";
import {
  getComputedValue,
  getComputedValueColorSub,
  getComputedValueText,
} from "../Survey/CreateSurvey/SendSurvey/SendSurvey";
import { debounce } from "lodash";
import Confirmation from "unmatched/components/Confirmation";
import QuickSend from "./components/QuickSend";
import "./components/styles.css";
import SendCustomEmail from "./components/SendCustomEmail";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";
import LoadingImg from "assets/images/Loading_2.gif";
import { components } from "react-select";
import { sendGlobalEmailReminderFact } from "./email-apis";
import useToastr from "unmatched/modules/toastr/hook";
import { getTemplatesFact, patchEmailTemplateFact, sendCustomEmailReminderFact } from "../Survey/survey-api";
import useSession from "unmatched/modules/session/hook";
import { UMTab } from "unmatched/components/Tabs";

export const userFriendlyTemplatesMap = {
  INACTIVE_ALL: "Activation Reminder",
  ACTIVE_INCOMPLETE: "General Reminder",
  SURVEY_INVITATION: "Survey Invitation",
  SPECIAL: "Special Reminder",

  REMINDER_INACTIVE: "Activation Reminder",
  REMINDER_ACTIVE: "General Reminder",
  CUSTOM: "Custom",
};

export const templatesDescMap = {
  INACTIVE_ALL: "Reminder for all reviewers who are inactive",
  ACTIVE_INCOMPLETE: "Reminder for reviewers who are active",
  SURVEY_INVITATION: "Invitation to participate",
  SPECIAL: "Special Reminder for reviewers",

  REMINDER_INACTIVE: "Reminder for all reviewers who are inactive",
  REMINDER_ACTIVE: "Reminder for reviewers who are active",
  CUSTOM: "Custom reminder",
};

export const userFriendlyRecipientsMap = {
  INACTIVE_ALL: "Inactive Users",
  ACTIVE_INCOMPLETE: "Idle Users",
  ACTIVE_COMPLETE: "Completed Users ",
  ACTIVE_PARTIAL: "Active Users",
  CUSTOM: "Custom",
};

export const recipientsDescMap = {
  INACTIVE_ALL:
    "Inactive Users - Users who haven’t even activated their accounts.",
  ACTIVE_INCOMPLETE:
    "Idle Users - Users who have activated their accounts but haven’t submitted any surveys.",
  ACTIVE_COMPLETE:
    "Completed Users - Users who have completed the surveys assigned to them.",
  ACTIVE_PARTIAL:
    "Active Users - Users who have activated and are submitting surveys.",
  CUSTOM: "Custom selected users.",
};

const Email = (props: any) => {
  const [targetGroup, setTargetGroup] = useState("INACTIVE_ALL");
  const [showConfirm, setShowConfirm] = useState(false);
  // const [showSendCustomModal, setShowSendCustomModal] = useState(false);
  const [quickSendType, setQuickSendType] = useState("selected");
  const [isSaving, setIsSaving] = useState(false);
  const [userOptions, setUserOptions] = useState<any>([]);
  const [selectedEmails, setSelectedEmails] = useState<any>([]);
  const [selectedSurveys, setSelectedSurveys] = React.useState<any>([]);
  const [emailData, setEmailData] = React.useState<any>(null);
  const [emailTemplates, setEmailTemplates] = React.useState([]);
  const [loggedInUser, setLoggedInUser] = React.useState<any>({});
  
  const toastr = useToastr();
  const { user } = useSession();

  const [activeTab, setActiveTab] = useState(0);

  const templates = [
    "INACTIVE_ALL", // REMINDER_INACTIVE
    "ACTIVE_INCOMPLETE", // REMINDER_ACTIVE
    // "ACTIVE_PARTIAL", // REMINDER_ACTIVE
    // "ACTIVE_COMPLETE", // REMINDER_ACTIVE
    "SURVEY_INVITATION", // SURVEY_INVITATION
    "SPECIAL", // SPECIAL
  ];

  const targetGroupsMap = {
    INACTIVE_ALL: "REMINDER_INACTIVE",
    ACTIVE_INCOMPLETE: "REMINDER_ACTIVE",
    ACTIVE_PARTIAL: "REMINDER_ACTIVE",
    ACTIVE_COMPLETE: "REMINDER_ACTIVE",
    SURVEY_INVITATION: "SURVEY_INVITATION",
    SPECIAL: "SPECIAL",
  };
  // meta
  let richTextRef: any = null;
  let subRichTextRef: any = null;

  const setRef = (ref: any) => {
    richTextRef = ref;
  };

  const setSubRef = (ref: any) => {
    subRichTextRef = ref;
  };

  const sendEmailReqBody = {
    survey_index: props.indexId,
    is_sandbox: false,
  };

  const sendEmail = (template: string, tGroup: string, showToast = false) => {
    const reqData = { is_sandbox: false };
    (reqData as any).target_group = tGroup;
    (reqData as any).template_name = template;
    return sendEmailReminder(reqData, showToast);
  };

  useEffect(() => {
    if (user?.email) {
      searchUsersFact("", { search: user.email })
        .then((res) => {
          setLoggedInUser(res.data?.results[0])
        })
        .catch((err) => console.log(err));
    }
  }, []);

  const sendCustomEmail = (
    template: string,
    tGroup: string,
    raterUsers: any
  ) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).template_name = template;
    if (selectedSurveys.length) {
      (reqData as any).survey_indexes = selectedSurveys.map((s: any) => s.id);
    }
    if (tGroup !== "CUSTOM") {
      (reqData as any).target_group = tGroup;
    } else {
      (reqData as any).rater_users = raterUsers;
    }
    sendCustomEmailReminder(reqData, true);
  };

  const sendMockCustomEmail = (template: string) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).template_name = template;
    (reqData as any).rater_users = [loggedInUser.id];
    sendCustomEmailReminder(reqData, true);
  };

  const sendCustomEmailReminder = (data: any, showToast: boolean) => {
    return sendCustomEmailReminderFact(data).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  const sendCustomEmailTemplate = (
    tGroup: string,
    raterUsers: any,
    showToast?: any
  ) => {
    const reqData = { ...sendEmailReqBody };
    (reqData as any).template_name = "CUSTOM";
    (reqData as any).rater_users = tGroup === "CUSTOM" ? raterUsers : [];
    if (tGroup !== "CUSTOM") {
      (reqData as any).target_group = tGroup;
    }
    return sendCustomEmailReminder(reqData, showToast);
  };

  const sendQuickMockEmailReminder = (data: any, showToast: boolean) => {
    const reqData = { is_sandbox: false };

    if (selectedSurveys.length) {
      (reqData as any).survey_indexes = selectedSurveys.map((s: any) => s.id);
    }
    (reqData as any).template_name = data.template;
    (reqData as any).rater_users = [loggedInUser?.id];
    return sendCustomEmailReminderFact(reqData).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  const sendEmailReminder = (data: any, showToast: boolean) => {
    const newData = { ...data };
    if (selectedSurveys.length) {
      newData.survey_indexes = selectedSurveys.map((s: any) => s.id);
    }
    return sendGlobalEmailReminderFact(newData).then((res: any) => {
      {
        showToast &&
          toastr.onSucces({
            title: "Success",
            content: "Email sent Successfully",
          });
      }
      return res;
    });
  };

  const getRecipientsCount = async (tGroup: any) => {
    const reqData = { ...sendEmailReqBody };
    reqData.is_sandbox = true;
    if (tGroup) {
      (reqData as any).target_group = tGroup;
    }
    const result = await sendEmailReminder(reqData, false);
    return { recipient: tGroup, count: result?.data?.count };
  };

  const getRecipientsCountsArr = async (recipients: any) => {
    return await Promise.all(
      recipients.map((r: any) => {
        return getRecipientsCount(r.value);
      })
    );
  };

  const getComputedUsers = (users: any) => {
    const usersData = users.map((u: any) => ({
      ...u,
      label: u.email,
      value: u.id,
    }));
    return usersData;
  };

  const getUserOptions = debounce((search: string) => {
    if (!search) return;
    searchUsersFact("", { search })
      .then((res) => {
        const data = getComputedUsers(res.data?.results || []);
        setUserOptions(data);
      })
      .catch((err) => console.log(err));
  }, 200);

  // debounce on subject change start

  const onSubjectChange = () => {
    // updateEmailData(
    //   {
    //     ...props.emailData,
    //   },
    //   false,
    //   setIsSaving
    // );
  };

  const delayedSubChange = useCallback(debounce(onSubjectChange, 1000), [
    props.emailData?.subject,
  ]);

  useEffect(() => {
    delayedSubChange();
    return delayedSubChange.cancel;
  }, [props.emailData?.subject, delayedSubChange]);

  useEffect(() => {
    getTemplates();
  }, []);

  

  // debounce on subject change end

  const getTemplates = (initial?: any) => {
    if (initial ===  undefined) initial = true;
    return getTemplatesFact(null, true).then((response: any) => {
      setEmailTemplates(response.results);

      initial &&
        setEmailData(
          response.results.find((r: any) => r.label === "REMINDER_INACTIVE")
        );
        
    });
  };

  useEffect(() => {
    console.log(emailData)
  }, [emailData]);

  const getTemplate = (name: any) => {
    const template = emailTemplates.find((r: any) => r.label === name);
    return template;
  }

  const TemplateOption = (props: any) => {
    return (
      <div className="px-2 py-1">
        <components.Option {...props} className="rounded pt-1 pb-2">
          <FormGroup className="m-0 d-flex align-items-center">
            <Text.P1
              style={{ color: "#000", fontSize: 14 }}
              className="pt-1 cursor-pointer"
            >
              {props.label}
            </Text.P1>
          </FormGroup>
        </components.Option>
      </div>
    );
  };

  const updateEmailData = (
    data: any,
    setAgain?: boolean,
    setIsSaving?: any,
    showToast?: boolean
  ) => {
    // let body = data.body.replaceAll(`<span style="color: black;"> </span>`, " ");
    setIsSaving?.(true);
    const body = data.body.replaceAll(`rgb(81, 140, 255)`, "black");
    return patchEmailTemplateFact(
      {
        ...data,
        // index: params.id,
        body,
      },
      data.id
    ).then((response: any) => {
      setAgain && setEmailData(response);
      setIsSaving?.(false);
      getTemplates(false);
      showToast &&
        toastr.onSucces({
          title: "Success",
          content: "Template saved Successfully",
        });
      return response;
    });
  };

  const getUpdateEmailTemplate = () => {
    return (
      <Card className="mb-4 mt-5" noShadow>
        <Card.Header className="pl-3 py-2 d-flex justify-content-between">
          <Text.H3>Email Templates</Text.H3>
          {/* <span className="mr-4 fs-14">{isSaving ? "Saving..." : "Saved"}</span> */}
        </Card.Header>
        <Div className="pl-3 py-2">
          <Div className="pr-4 pt-2">
            <Form>
              <Div className="d-flex justify-content-between align-items-center">
                <Div style={{ width: 230 }}>
                  <FormGroup>
                    <FormGroup.Label className="pb-2">
                      Choose Template:
                    </FormGroup.Label>
                    <MultiSelect
                      CustomOption={TemplateOption}
                      options={templates.map((tg: any) => ({
                        value: tg,
                        label: (userFriendlyTemplatesMap as any)[tg],
                      }))}
                      isMulti={false}
                      closeMenuOnSelect
                      value={{
                        value: targetGroup,
                        label: (userFriendlyTemplatesMap as any)[targetGroup],
                      }}
                      onSelect={(selected: any) => {
                        setTargetGroup(selected.value);
                        const template = emailTemplates.find(
                          (r: any) =>
                            r.label === (targetGroupsMap as any)[selected.value]
                        );
                        if (template) {
                          setEmailData(template);
                          const quillRef = richTextRef.getEditor();
                          const subQuillRef = subRichTextRef.getEditor();
                          // https://stackoverflow.com/questions/46626633/how-do-you-insert-html-into-a-quilljs
                          setTimeout(() => {
                            quillRef.clipboard.dangerouslyPasteHTML(
                              getComputedValue((template as any).body, true)
                            );

                            subQuillRef.clipboard.dangerouslyPasteHTML(
                              getComputedValueColorSub(
                                getComputedValue((template as any).subject, true)
                              )
                            );
                          }, 100);
                        }
                      }}
                    />
                  </FormGroup>
                </Div>

                <Div>
                  <Button
                    onClick={() => setShowConfirm(true)}
                    variant="outline-primary"
                    loading={isSaving}
                  >
                    {isSaving && <img width={25} src={LoadingImg} />} Save
                    Template
                  </Button>
                </Div>
              </Div>

              <FormGroup>
                <FormGroup.Label>Email Subject</FormGroup.Label>
                <Div className="survey-quill-subject">
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      setEmailData((eD: any) => ({
                        ...eD,
                        subject: getComputedValueText(text, false),
                      }));
                      // delayedSubChange();
                    }}
                    value={getComputedValueColorSub(
                      getComputedValue(emailData?.subject, true)
                    )}
                    insertOptions={CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS}
                    setRef={setSubRef}
                  />
                </Div>
              </FormGroup>
              <FormGroup>
                <FormGroup.Label>Email Body</FormGroup.Label>
                <Div className="survey-quill">
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      setEmailData((eD: any) => ({
                        ...eD,
                        body: getComputedValue(html, false),
                        text: getComputedValueText(text, false),
                      }));
                    }}
                    value={getComputedValue(emailData?.body, true)}
                    insertOptions={CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS}
                    setRef={setRef}
                  />
                  <Div>
                    <FormControl.Textarea
                      className="email-body-foot"
                      disabled
                      value={`This is an automated email. Please do not respond to this email.
If you have any technical issues, please contact our support <NAME_EMAIL>

Unmatched by Survey Research Associates
Professional Development | People Management | Employee Engagement software`}
                      rows={5}
                      style={{ color: "#6D6D6D", fontSize: 14 }}
                    />
                  </Div>
                </Div>
              </FormGroup>
            </Form>
          </Div>
        </Div>
        {false && (
          <Card.Footer className="mr-4">
            {/* <Button
              onClick={sendMockEmail}
              variant="outline-primary"
              className="mr-3"
            >
              Mock Send
            </Button> */}
            <Button
              onClick={() => setShowConfirm(true)}
              variant="outline-primary"
            >
              Send Email
            </Button>
          </Card.Footer>
        )}
      </Card>
    );
  };

  const getNavItem = (title: string, key: any) => {
    return <UMTab
      eventKey={key}
      activeKey={activeTab}
      onClick={() => setActiveTab(key)}
    >
      {title}
    </UMTab>;
  };

  const getTabsUI = () => {
    return (
      <Div
        style={{
          position: "absolute",
          fontSize: 14,
          width: 180,
          justifyContent: "space-between",
          bottom: 0,
        }}
        className="d-flex"
      >
        {getNavItem("Quick Send", 0)}
        {getNavItem("Templates", 1)}
      </Div>
    );
  };

  return (
    <Div>
      <CustomHeader
        style={{ marginLeft: 60, padding: "25px 30px 25px" }}
        title={
          <>
            <Layout.Flex>
              <Layout.FlexItem>
                <Text.H1 className="pb-2">Emails</Text.H1>
              </Layout.FlexItem>
              <Layout.FlexItem className="pl-2">
                {/* <Badge variant={meta.variant}>{meta.title}</Badge> */}
              </Layout.FlexItem>
            </Layout.Flex>
            {getTabsUI()}
          </>
        }
        breadcrumbs={[
        
        ]}
      />
      {/* //tabs */}

      <PageContainer className="pt-2">
        <Layout.Container fluid className="pt-4">
          {activeTab === 0 && (
            <>
              <FormGroup className="d-flex mt-4">
                <FormControl.Radio className="mr-3">
                  <FormControl.Radio.Label>
                    Send from templates
                  </FormControl.Radio.Label>
                  <FormControl.Radio.Input
                    onChange={() => setQuickSendType("selected")}
                    checked={quickSendType === "selected"}
                  />
                </FormControl.Radio>
                <FormControl.Radio>
                  <FormControl.Radio.Label>
                    Send custom email
                  </FormControl.Radio.Label>
                  <FormControl.Radio.Input
                    onChange={() => setQuickSendType("custom")}
                    checked={quickSendType === "custom"}
                  />
                </FormControl.Radio>
              </FormGroup>
              {quickSendType === "selected" ? (
                <QuickSend
                  loggedInUser={loggedInUser}
                  sendEmail={sendEmail}
                  getRecipientsCount={getRecipientsCount}
                  sendQuickMockEmailReminder={sendQuickMockEmailReminder}
                  // setShowSendCustomModal={setShowSendCustomModal}
                  userOptions={userOptions}
                  onInputChange={getUserOptions}
                  setSelectedEmails={setSelectedEmails}
                  selectedEmails={selectedEmails}
                  sendCustomEmail={sendCustomEmail}
                  sendMockCustomEmail={sendMockCustomEmail}
                  getRecipientsCountsArr={getRecipientsCountsArr}
                  // onBlur={() => setUserOptions([])}
                  survey={props.survey}
                  setSelectedSurveys={setSelectedSurveys}
                  selectedSurveys={selectedSurveys}
                />
              ) : (
                <SendCustomEmail
                  loggedInUser={loggedInUser}
                  userOptions={userOptions}
                  onInputChange={getUserOptions}
                  setSelectedEmails={setSelectedEmails}
                  selectedEmails={selectedEmails}
                  getTemplate={getTemplate}
                  getRecipientsCount={getRecipientsCount}
                  sendCustomEmailTemplate={sendCustomEmailTemplate}
                  sendEmail={sendEmail}
                  sendMockCustomEmail={sendMockCustomEmail}
                  updateEmailData={updateEmailData}
                  getRecipientsCountsArr={getRecipientsCountsArr}
                  // onBlur={() => setUserOptions([])}
                  survey={props.survey}
                />
              )}
            </>
          )}
          {activeTab === 1 && getUpdateEmailTemplate()}
        </Layout.Container>
      </PageContainer>
      <Confirmation
        confirmText="Are you sure you want to save template?"
        show={showConfirm}
        onOk={async () => {
          await updateEmailData(
            {
              ...emailData,
            },
            false,
            setIsSaving,
            true
          );
        }}
        setShow={setShowConfirm}
      />
      {/* <Modal show={showSendCustomModal} size="lg" centered>
        <ModalHeader
          title="Send a Custom Email"
          onHide={() => setShowSendCustomModal(false)}
        />
        <Modal.Body>
          <SendCustomEmail
            userOptions={userOptions}
            onInputChange={getUserOptions}
            setSelectedEmails={setSelectedEmails}
            selectedEmails={selectedEmails}
            getTemplate={getTemplate}
            getRecipientsCount={getRecipientsCount}
            sendCustomEmailTemplate={sendCustomEmailTemplate}
            sendEmail={sendEmail}
            sendMockCustomEmail={sendMockCustomEmail}
            updateEmailData={updateEmailData}
            getRecipientsCountsArr={getRecipientsCountsArr}
            // onBlur={() => setUserOptions([])}
            survey={props.survey}
          />
        </Modal.Body>
      </Modal> */}
    </Div>
  );
};

export default Email;
