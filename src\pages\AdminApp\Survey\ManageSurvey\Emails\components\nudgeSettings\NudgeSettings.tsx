import { deleteNudgeSettingFact, patchNudgeSettingFact, postNudgeSettingFact } from "pages/AdminApp/Survey/survey-api";
import {
  Div,
  Layout,
  Text,
  Card,
  FormGroup,
  FormControl,
  Button,
} from "unmatched/components";
import useToastr from "unmatched/modules/toastr/hook";

export const days = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

const typeTitle = {
  activation: "Activation Reminder",
  general: "General Reminder",
  special: "Special Reminder",
};

const NudgeSettings = (props: any) => {
  const { settings, handleChange } = props;
  const { showToast } = useToastr();

  const post = (settings: any) => {
    postNudgeSettingFact(settings).then(() => {
        props.getNudgeSettings();
        showToast({
          variant: "success",
          title: `${(typeTitle as any)[props.type]}`,
          content: "Nudge settings were added successfully",
        });
      });
  }

  const patch = (settings: any) => {
    patchNudgeSettingFact(settings, settings.id).then(() => {
        props.getNudgeSettings();
        showToast({
          variant: "success",
          title: `${(typeTitle as any)[props.type]}`,
          content: "Nudge settings were updated successfully",
        });
      });
  }

  const deleteNudge = (settings: any) => {
    deleteNudgeSettingFact(settings.id).then(() => {
        props.getNudgeSettings();
        showToast({
          variant: "success",
          title: `${(typeTitle as any)[props.type]}`,
          content: "Nudge settings unset successfully",
        });
      });
  }

  const getNudgeSettingsTemplate = () => {
    return (
      <Card className="mb-4" noShadow>
        <Card.Header className="pl-3 py-2">
          <Text.H3>Nudge Settings - {(typeTitle as any)[props.type]}</Text.H3>
        </Card.Header>
        <Div className="pl-3 py-2">
          <Text.P1 style={{ fontSize: 12 }}>
            {(details as any)[props.type].desc}
          </Text.P1>
          <Layout.Row className="mt-3">
            <Layout.Col className="col-12 mb-3">
              <Layout.Flex>
                <Layout.FlexItem>
                  <FormControl.Radio>
                    <FormControl.Radio.Input
                      onChange={() => handleChange("frequency", "DAILY")}
                      checked={settings.frequency === "DAILY"}
                    />
                  </FormControl.Radio>
                </Layout.FlexItem>
                <Layout.FlexItem>
                  {/* <Text.H3>{item.title}</Text.H3> */}
                  <Text.P1>Send reminders daily</Text.P1>
                </Layout.FlexItem>
              </Layout.Flex>
            </Layout.Col>
            <Layout.Col className="col-12 mb-3">
              <Layout.Flex>
                <Layout.FlexItem>
                  <FormControl.Radio>
                    <FormControl.Radio.Input
                      onChange={() => handleChange("frequency", "WEEKLY")}
                      checked={settings.frequency === "WEEKLY"}
                    />
                  </FormControl.Radio>
                </Layout.FlexItem>
                <Layout.FlexItem>
                  <Text.P1>Send weekly reminders on selected days</Text.P1>
                </Layout.FlexItem>
              </Layout.Flex>
            </Layout.Col>
            <Layout.Col className="col-12 mb-3">
              <Layout.Flex>
                <Layout.FlexItem>
                  <FormControl.Radio>
                    <FormControl.Radio.Input
                      onChange={() => handleChange("frequency", "BIWEEKLY")}
                      checked={settings.frequency === "BIWEEKLY"}
                    />
                  </FormControl.Radio>
                </Layout.FlexItem>
                <Layout.FlexItem>
                  <Text.P1>Send bi weekly reminders on selected days</Text.P1>
                </Layout.FlexItem>
              </Layout.Flex>
            </Layout.Col>
            <Text.P1 style={{ fontSize: 14 }} className="text-muted pt-2 ml-3">
              <Div className="row px-3">
                {days.map((day) => (
                  <FormGroup>
                    <FormControl.Checkbox className="col">
                      <FormControl.Checkbox.Label>
                        {day}
                      </FormControl.Checkbox.Label>
                      <FormControl.Checkbox.Input
                        onChange={() =>
                          handleChange(
                            "days",
                            settings.days.includes(day)
                              ? settings.days.filter((el: string) => el !== day)
                              : [...settings.days, day]
                          )
                        }
                        checked={settings.days.includes(day)}
                        disabled={settings.frequency === "DAILY"}
                      />
                    </FormControl.Checkbox>
                  </FormGroup>
                ))}
              </Div>
            </Text.P1>
          </Layout.Row>
          <Div className="row">
            <Div className="col pr-0 pt-1" style={{ maxWidth: 80 }}>
              <FormGroup>
                <FormGroup.Label>Set Time:</FormGroup.Label>
              </FormGroup>
            </Div>
            <Div className="col-3">
              <FormGroup>
                <input
                  onChange={(e: any) => {
                    handleChange("time", e.target.value);
                  }}
                  type="time"
                  value={settings.time}
                  className="form-control"
                />
              </FormGroup>
              
            </Div>
            <Div className="col-4"><Text.P1 className="mt-2">{props.survey.timezone}</Text.P1></Div>
            <Div
              className="col-4"
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
                paddingRight: 30
              }}
            >
              {props.notIsNew && <Button
                onClick={() => {
                  deleteNudge(settings)
                }}
                variant="outline-primary"
                className="mr-1"
              >
                Unset
              </Button>}
              <Button
                onClick={() => {
                  props.notIsNew ? patch(settings) : post(settings);
                }}
                variant="primary"
              >
                Save settings
              </Button>
            </Div>
          </Div>
        </Div>
      </Card>
    );
  };

  return <Div>{getNudgeSettingsTemplate()}</Div>;
};

const details = {
  activation: {
    desc: "Send activation reminder emails to reviewers who have not visited the survey. Set the frequency below.",
  },
  general: {
    desc: "Send a general reminder to reviewers who have visited the survey but have outstanding surveys to complete. Set the frequency below.",
  },
  special: {
    desc: "Send a special reminder to reviewers who could help a reviewee get a report if they respond. (Email will contain the list of reviewees who are shy of one response to get a report). Set the frequency below.",
  },
}

export default NudgeSettings;
