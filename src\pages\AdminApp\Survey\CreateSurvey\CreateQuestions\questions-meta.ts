import { getXHRState } from "unmatched/utils/store";
import { QUESTION } from "../../survey-enums";

const data = {
  ...getXHRState(),
  sections: {
    list: [1, 2],
    content: {
      section1: {
        id: 1,
        title: "Section 1",
        questions: [1, 2],
      },
      section2: {
        id: 1,
        title: "Section 2",
        questions: [3],
      },
    },
  },
  questions: {
    list: [1, 2],
    content: {
      question1: {
        id: 1,
        sectionId: 1,
        isValid: false,
        question: "Question 1",
        markAsMandatory: true,
        collectTextFeedback: true,
        type: QUESTION.CHECKBOX,
        rankingId: 1,
        radioId: 1,
        checkboxId: 1,
        inputId: 1,
        dropdownId: 1,
      },
      // question2: {
      //   id: 2,
      //   sectionId: 2,
      //   isValid: false,
      //   question: "Question 2",
      //   markAsMandatory: true,
      //   collectTextFeedback: true,
      //   type: QUESTION.DROPDOWN,
      //   rankingId: 1,
      //   radioId: 1,
      //   checkboxId: 1,
      //   inputId: 1,
      //   dropdownId: 1,
      // },
      question3: {
        id: 3,
        sectionId: 2,
        isValid: false,
        question: "Question 3",
        markAsMandatory: true,
        collectTextFeedback: true,
        type: QUESTION.RADIO,
        rankingId: 1,
        radioId: 1,
        checkboxId: 1,
        inputId: 1,
        dropdownId: 1,
      },
    },
  },
  rankings: {
    list: [1, 2, 3, 4, 5],
    content: {
      ranking1: { id: 1, scale: 3, questionId: 1, options: [1, 2, 3] },
      ranking2: { id: 2, scale: 3, questionId: 2, options: [1, 2, 3] },
      ranking3: { id: 3, scale: 3, questionId: 3, options: [1, 2, 3] },
      ranking4: { id: 4, scale: 3, questionId: 4, options: [1, 2, 3] },
      ranking5: { id: 5, scale: 3, questionId: 5, options: [1, 2, 3] },
    },
    options: {
      list: [1, 2, 3, 4, 5],
      content: {
        option1: { id: 1, label: "", isValid: false, order: 1, rankingId: 1 },
        option2: { id: 2, label: "", isValid: false, order: 2, rankingId: 1 },
        option3: { id: 3, label: "", isValid: false, order: 3, rankingId: 1 },
      },
    },
  },
  checkboxes: {
    list: [1, 2, 3, 4, 5],
    content: {
      checkbox1: { id: 1, questionId: 1, options: [1, 2, 3] },
      checkbox2: { id: 2, questionId: 2, options: [1, 2, 3] },
      checkbox3: { id: 3, questionId: 3, options: [1, 2, 3] },
      checkbox4: { id: 4, questionId: 4, options: [1, 2, 3] },
      checkbox5: { id: 5, questionId: 5, options: [1, 2, 3] },
    },
    options: {
      list: [1, 2, 3, 4, 5],
      content: {
        option1: { id: 1, label: "", isValid: false, order: 1, checkboxId: 1 },
        option2: { id: 2, label: "", isValid: false, order: 1, checkboxId: 1 },
        option3: { id: 3, label: "", isValid: false, order: 1, checkboxId: 1 },
      },
    },
  },
  radios: {
    list: [1, 2, 3, 4, 5],
    content: {
      radio1: { id: 1, questionId: 1, options: [1, 2, 3] },
      radio2: { id: 2, questionId: 2, options: [1, 2, 3] },
      radio3: { id: 3, questionId: 3, options: [1, 2, 3] },
      radio4: { id: 4, questionId: 4, options: [1, 2, 3] },
      radio5: { id: 5, questionId: 5, options: [1, 2, 3] },
    },
    options: {
      list: [1, 2, 3, 4, 5],
      content: {
        option1: { id: 1, label: "", isValid: false, order: 1, radioId: 1 },
        option2: { id: 2, label: "", isValid: false, order: 1, radioId: 1 },
        option3: { id: 3, label: "", isValid: false, order: 1, radioId: 1 },
      },
    },
  },
  dropdowns: {
    list: [1, 2, 3, 4, 5],
    content: {
      dropdown1: { id: 1, questionId: 1, options: [1, 2, 3] },
      dropdown2: { id: 2, questionId: 2, options: [1, 2, 3] },
      dropdown3: { id: 3, questionId: 3, options: [1, 2, 3] },
      dropdown4: { id: 4, questionId: 4, options: [1, 2, 3] },
      dropdown5: { id: 5, questionId: 5, options: [1, 2, 3] },
    },
    options: {
      list: [1, 2, 3, 4, 5],
      content: {
        option1: {
          id: 1,
          label: "",
          isValid: false,
          order: 1,
          dropdownId: 1,
        },
        option2: {
          id: 2,
          label: "",
          isValid: false,
          order: 1,
          dropdownId: 1,
        },
        option3: {
          id: 3,
          label: "",
          isValid: false,
          order: 1,
          dropdownId: 1,
        },
      },
    },
  },
  inputs: {
    list: [1, 2, 3, 4, 5],
    content: {
      input1: { id: 1, questionId: 1 },
      input2: { id: 2, questionId: 2 },
      input3: { id: 3, questionId: 3 },
      input4: { id: 4, questionId: 4 },
      input5: { id: 5, questionId: 5 },
    },
  },
};

export default data;
