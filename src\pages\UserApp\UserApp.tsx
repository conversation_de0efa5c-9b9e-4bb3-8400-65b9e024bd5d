// Node modules
import React, { useEffect, useState } from "react";
import { Redirect, Route, useHistory } from "react-router-dom";
import DASHBOARD_ROUTES from "./user-app-routes";
import util from "unmatched/utils";
import { Button, Div, Text, InfoPopover, Icon } from "unmatched/components";
import AppRoutes from "../AppRoutes";
import Header from "./Header/Header";
import useSession from "unmatched/modules/session/hook";
// import  from "unmatched/components/InfoPopover";
import { PasswordModalContext } from "pages/App";
import styled from "styled-components";
// import _localStorage from "unmatched/utils/local-storage";
const _localStorage = util.storage;

const StyledButton = styled(Button)`
  border-color: #fff;
  color: #fff;
  margin: 25px 0px;
`;

const UserDashboard = () => {
  const history = useHistory();
  const { user } = useSession();
  const [showInfoPopover, setShowInfoPopover] = useState(false);

  useEffect(() => {
    setShowInfoPopover(!user.isPasswordSet);
  }, []);

  const onLogout = async () => {
    history.push(util.appUrls.logout);
  };

  const setPassSeen = _localStorage.getItem("setPassSeen");

  return (
    <Div>
      <PasswordModalContext.Consumer>
        {({ toggle }) => (
          <>
            <Header
              onLogout={onLogout}
              name={user && user.firstName ? user.firstName[0] : "U"}
              togglePasswordModal={toggle}
            />
            <Div className={util.getUtilClassName({ mt: 5, pt: 4 })}>
              <AppRoutes routes={DASHBOARD_ROUTES}>
                <Route exact path={util.appUrls.user.default}>
                  <Redirect to={util.appUrls.user.dashboard.default} />
                </Route>
              </AppRoutes>
            </Div>
            {showInfoPopover && !setPassSeen && (
              <InfoPopover>
                <Button
                  className="pull-right text-muted closepass"
                  onClick={() => {
                    _localStorage.setItem("setPassSeen", true);
                    setShowInfoPopover(false);
                  }}
                  size="sm"
                  variant="light"
                >
                  <Icon icon="far fa-times" />
                </Button>
                <Text.H2>Setup a Password</Text.H2>
                <Text.P1 className="mt-3">
                  Setup a password for your account to take up the survey
                  anytime. You can also use the magic link received on your
                  email to login back again.
                </Text.P1>
                <StyledButton
                  onClick={() => {
                    toggle(true);
                    setShowInfoPopover(false);
                    _localStorage.setItem("setPassSeen", true);
                  }}
                  variant="outline-primary"
                >
                  Setup Password
                </StyledButton>
                <Text.P1
                  onClick={() => {
                    _localStorage.setItem("setPassSeen", true);
                    setShowInfoPopover(false);
                  }}
                  className="mt-3 cursor-pointer"
                >
                  Dismiss
                </Text.P1>
              </InfoPopover>
            )}
          </>
        )}
      </PasswordModalContext.Consumer>
    </Div>
  );
};

export default UserDashboard;
