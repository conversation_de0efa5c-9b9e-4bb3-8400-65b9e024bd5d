import axios from "axios";
import util from "unmatched/utils";
// import api from "unmatched/utils/api";
// import API_URLS from "unmatched/utils/api";

// interface LoginPayload {
//       email: string;
//       password: string;
// }

// interface Upload {
//   email: string;
// }

// interface SetPayload {
//   email: string;
//   token: string;
//   password: string;
// }

export const employeeFileUploadFact = (req: any, params?: any, meta?: any) => {
  const {data, format, title, tags} = req
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  const formData = new FormData();
  formData.append("data", data);
  formData.append("format", format);
  formData.append("title", title)
  formData.append("tags[]", tags)
  return axios.post(`${util.apiUrls.EMPLOYEE_UPLOAD}`, formData, config);
};

export const getSampleEmployeeListDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.SAMPLE_EMPLOYEE_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `example.${params.f}`);
      document.body.appendChild(link);
      link.click();
    });
};
export const getSamplePairingDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.SAMPLE_PAIRING_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `example.${params.f}`);
      document.body.appendChild(link);
      link.click();
    });
};

export const pairingFileUploadFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  const formData = new FormData();
  formData.append("data", data.File);
  formData.append("format", data.Format);
  return axios.post(`${util.apiUrls.PAIRING_UPLOAD}`, formData, config);
};
export const pairingPatchFileFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PAIRING_UPLOAD}`, config);
};

export const getAllUserFilesFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_USER_FILES}`, config);
};

export const getAllPairFileFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_PAIR_FILES}`, config);
};

export const getAllAssociateFileFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_ASSOCIATE_FILES}`, config);
};

export const patchUserUploadFact = (
  id: string,
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PATCH_USER_UPLOAD(id)}`, data, config);
};

export const patchPairUploadFact = (
  id: string,
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PATCH_PAIRING_UPLOAD(id)}`, data, config);
};

export const fetchPairFileInfoFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.FETCH_PAIRING_FILE_INFO(id)}`, config);
};

export const fetchOngoingPairFileInfoFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.FETCH_ONGOING_PAIRING_FILE_INFO(id)}`, config);
};

export const fetchAdminAbstractPairingsFact = (
  id?: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_PAIRINGS_FROM_FILE}`, config);
};

export const fetchAdminOngoingPairingsFact = (
  id?: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ADMIN_PAIRINGS}`, config);
};

export const searchUsersFact = (id?: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_USERS}`, config);
};

export const fetchUserFileInfoFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.FETCH_USER_FILE_INFO(id)}`, config);
};

export const getAllPairFromFileFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_PAIRINGS_FROM_FILE}`, config);
};

export const getAllUserFromFileFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.GET_ALL_USERS_FROM_FILE}`, config)
    .then((response) => {
      const { count_pages, results } = response.data;

      return {
        data: {
          totalPages: count_pages,
          results: results.map((item: any) => {
            return {
              email: item.email,
              empId: item.emp_id,
              firstName: item.first_name,
              id: item.id,
              lastName: item.last_name,
              metadata: item.metadata,
            };
          }),
        },
      };
    });
};

export const deleteUserFileFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.PATCH_USER_UPLOAD(id)}`, config);
};

export const deletePairFileFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.PATCH_PAIRING_UPLOAD(id)}`, config);
};

export const downloadUserFileInfoFact = async (
  id: string,
  type: string,
  fallback: Function
  // params?: any,
  // meta?: any
) => {
  const getInfo = await fetchUserFileInfoFact(id);
  switch (type) {
    case "DOWNLOAD":
      // return await call(getInfo.data.file)
      return window.open(getInfo.data.file);
    case "ERROR":
      if (getInfo.data.error_log_file) {
        return window.open(getInfo.data.error_log_file);
      } else {
        return fallback();
      }

    default:
      return Error("Invalid");
  }
};
export const downloadPairFileInfoFact = async (
  id: string,
  type: string,
  fallback: Function
  // params?: any,
  // meta?: any
) => {
  const getInfo = await fetchPairFileInfoFact(id);
  switch (type) {
    case "DOWNLOAD":
      // return await call(getInfo.data.file)
      return window.open(getInfo.data.file);
    case "ERROR":
      if (getInfo.data.error_log_file) {
        return window.open(getInfo.data.error_log_file);
      } else {
        return fallback();
      }

    default:
      return Error("Invalid");
  }
};
export const getRequiredUserFieldFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_REQUIRED_USER_FIELDS}`, config);
};

export const getRequiredPairFieldFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_REQUIRED_PAIR_FIELDS}`, config);
};

export const addPairingsBulkFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.ADD_BULK_PAIRING}`, data, config);
};

export const validatePairingFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.VALIDATE_PAIRING}`, data, config);
};

export const pairingsFileDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.PAIRINGS_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `pairings_${params.index_id}.${params.f}`);
      document.body.appendChild(link);
      link.click();
    })
    .catch((err: any) => console.log(err));
};
