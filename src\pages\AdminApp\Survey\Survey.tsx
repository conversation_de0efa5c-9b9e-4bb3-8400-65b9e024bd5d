import React from "react";
import SURVEY_ROUTES from "./survey-routes";
import AppRoutes from "../../AppRoutes";
import { Redirect, Route } from "react-router-dom";
import appUrls from "unmatched/utils/urls/app-urls";

export default function Survey() {
  return (
    <AppRoutes routes={SURVEY_ROUTES}>
      <Route exact path={appUrls.admin.survey.default}>
        <Redirect to={appUrls.admin.survey.getSurveysUrl()} />
      </Route>
    </AppRoutes>
  );
}
