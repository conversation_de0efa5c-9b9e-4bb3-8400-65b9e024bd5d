import React from "react";
import { Tooltip, OverlayTrigger } from "react-bootstrap";
import styled from "styled-components";
import { Div, Icon } from "unmatched/components";

const StyledTooltip = styled(Tooltip)`
  .tooltip-inner {
    max-width: 500px;
    text-align: left;
  }
`;

function Tip(props: any) {
  return (
    <Div className="ml-1">
      <OverlayTrigger
        placement="bottom"
        overlay={<StyledTooltip className="lg-info" id="button-StyledTooltip-2">{props.children}</StyledTooltip>}
      >
        {({ ...triggerHandler }) => (
          <span {...triggerHandler}>
            <Icon icon="fal fa-info-circle fs-12" />
          </span>
        )}
      </OverlayTrigger>
    </Div>
  );
}

export default Tip;
