import React, { useState, useEffect } from "react";
import Manualpairing from "./AddManuallyForm";
import {
  addPairingsBulkFact,
  searchUsersFact,
  validatePairingFact,
} from "../../pairings-api";
import { cloneDeep, debounce } from "lodash";
import useToastr from "unmatched/modules/toastr/hook";

export function AddPairingManually(props: any) {
  const initPairData = [
    {
      id: Math.random().toString().split(".")[1],
      rater: "",
      target: "",
    },
  ];
  const [pairData, setPairData] = useState(initPairData);
  const [errors, setErrors] = useState<any>([]);
  const [userOptions, setUserOptions] = useState<any>([]);
  const { showToast } = useToastr();
  const [pairErrors, setPairErrors] = useState<any>([]);

  const { onHide, surveyIndex } = props;
  
  useEffect(() => {
    updateErrors();
  }, [pairData]);

  const getComputedUsers = (users: any) => {
    const usersData = users.map((u: any) => ({
      ...u,
      label: u.email,
      value: u.emp_id,
    }));
    return usersData;
  };
  const getUserOptions = debounce((search: string) => {
    searchUsersFact("", { search })
      .then((res) => {
        const data = getComputedUsers(res.data?.results || []);
        setUserOptions(data);
      })
      .catch((err) => console.log(err));
  }, 200);

  const editPairSelect = (id: number, name: string) => {
    setPairData((state: any) => {
      return state.map((el: any) => (el.id === id ? { ...el, name } : el));
    });
  };

  const deletePairSelect = (index: number) => {
    const newPairData = pairData.filter((_: any, i: number) => i !== index);
    setPairData(newPairData);
    setPairErrors(pairErrors.filter((_: any, i: number) => i !== index));
  };

  const updateErrors = () => {
    setErrors(
      pairData.map((pD: any) => ({
        target: !pD.target ? "Target is required" : "",
        rater: !pD.rater ? "Rater is required" : "",
      }))
    );
  };

  const checkValidPair = (pair: any, i: number) => {
    validatePairingFact({
      rater: pair.rater,
      target: pair.target,
      index: surveyIndex,
    })
      .then(({ data }) => {
        if (data?.isValid) {
          return;
        }
        const err = data?.errors?.non_field_errors?.[0];
        const errs = cloneDeep(pairErrors);
        errs[i] = err || "";
        if (
          pairData.some(
            (pD: any) => pD.rater === pair.rater && pD.target === pair.target
          )
        ) {
          errs[i] = "Duplicate pair";
        }
        setPairErrors(errs);
      })
      .catch((err: any) => console.log(err));
  };

  const hasError = (errors: any) => {
    return (
      errors.some((err: any) => err.rater || err.target) ||
      pairErrors.some((pE: string) => pE)
    );
  };

  const onSelect = (i: number, name: string) => (user: any) => {
    const newPairData = cloneDeep<any>(pairData);
    newPairData[i][name] = user.id;
    setPairData(newPairData);
    if (newPairData[i].rater && newPairData[i].target)
      checkValidPair(newPairData[i], i);
  };

  const addPairings = async (data: any) => {
    try {
      data.index = surveyIndex;
      data.survey = props.survey?.data?.versions?.[0]?.id
      await addPairingsBulkFact(data);
      showToast({
        variant: "success",
        title: "Success",
        content: "Pairings added successfully.",
      });
      onHide();
      setPairData(initPairData);
      // getPairings();
    } catch (err: any) {
      showToast({
        variant: "danger",
        title: "Error",
        content: err.msg,
      });
      console.log(err);
    }
  };

  const renderView = (props: any) => {
    return (
      <Manualpairing
        {...props}
        pairData={pairData}
        setPairData={setPairData}
        editPairSelect={editPairSelect}
        deletePairSelect={deletePairSelect}
        errors={errors}
        onInputChange={getUserOptions}
        userOptions={userOptions}
        hasError={hasError}
        updateErrors={updateErrors}
        onSelect={onSelect}
        addPairings={addPairings}
        initPairData={initPairData}
        pairErrors={pairErrors}
        setPairErrors={setPairErrors}
        setScreen={props.setScreen}
      />
    );
  };

  return renderView(props);
}
