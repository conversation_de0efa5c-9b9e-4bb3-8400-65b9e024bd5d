import React from "react";
import _ from "lodash";
import { Div, Layout, Text, FormControl } from "@unmatchedoffl/ui-core";
// import QuestionTypeWrapper from "./QuestionOptionWrapper";
// import { QUESTION } from "../../../survey-enums";
// import { getOptionItem } from "./QuestionTypes";

// patchRatingOptionsFact(payload).then(
//   () => {
//     onUpdate(payload);
//   },
//   () => {
//     setSwap(!swap);
//   }
// );

interface RankingType {
  surveyId?: number;
  sectinoId?: number;
  id?: number;
  question?: any;
  setValidations?: Function;
  onUpdate: Function;
  onSwap: Function;
  viewOnly?: boolean;
  renderOption: any;
  questionTypes: any;
  api: any;
}

const Rating = (props: RankingType) => {
  const { question, onUpdate, viewOnly, renderOption, questionTypes, api } =
    props;
  const ratingData = question.rating;

  const [hasSwap, setSwap] = React.useState(question.hasSwap);
  let tags = _.toPairs(ratingData.tags);

  if (hasSwap) {
    tags = _.reverse(tags);
  }

  const onSubmit = ({ option }: any, { id }: any) => {
    const payload = {
      ...question,
      rating: {
        ...question.rating,
        tags: {
          ...question.rating.tags,
          [id]: option,
        },
      },
    };
    api(payload).then(() => {
      onUpdate(payload);
    });
  };

  const getTags = (value: number) => {
    const tags: any = {};
    _.range(1, value + 1).forEach((item: number) => {
      tags[item.toString()] = `Scale - ${Number(item)}`;
    });
    return tags;
  };

  const onScaleChange = (value: number) => {
    const payload = {
      ...question,
      rating: {
        ...question.rating,
        range_end: value,
        end: value,
        tags: getTags(value),
      },
    };
    api(payload).then(() => {
      onUpdate(payload);
    });
  };

  const onSwap = () => {
    const swap = !hasSwap;
    const payload = {
      ...question,
      hasSwap: swap,
    };
    setSwap(swap);
    api(payload).then(
      () => {
        onUpdate(payload);
      },
      () => {
        setSwap(!swap);
      }
    );
  };

  React.useEffect(() => {
    console.log("Ratings R mounted");
}, []);

  return (
    <Div>
      <Layout.Flex>
        <Layout.FlexItem className="align-self-center pr-3">
          <Text.P1>Scale Range</Text.P1>
        </Layout.FlexItem>
        <Layout.FlexItem className="align-self-center pr-3">
          <FormControl.Select value={ratingData.end}>
            {!viewOnly &&
              _.range(1, 11).map((item: number) => {
                return (
                  <FormControl.SelectItem
                    key={item}
                    onSelect={() => onScaleChange(item)}
                  >
                    {item}
                  </FormControl.SelectItem>
                );
              })}
          </FormControl.Select>
        </Layout.FlexItem>
        <Layout.FlexItem className="align-self-center">
          <Text.P1 className="pt-2">
            <FormControl.Switch
              name={"enablePairings"}
              checked={hasSwap}
              onChange={onSwap}
            />{" "}
            Reverse Scale
          </Text.P1>
        </Layout.FlexItem>
      </Layout.Flex>
      <Div>
        {/* {JSON.stringify(ratingData)} */}
        {tags.map(([key, value]: any) => {
          const content = <Text.P1>{Number(key)}.</Text.P1>;
          return (
            <React.Fragment key="key">
              {renderOption(
                {
                  onSubmit,
                  id: key,
                  type: questionTypes.RATING,
                  value: value,
                },
                content
              )}
            </React.Fragment>
          );
        })}
      </Div>
    </Div>
  );
};

export default Rating;
