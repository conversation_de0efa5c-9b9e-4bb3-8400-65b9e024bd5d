import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  useHistory,
} from "unmatched/hooks";
// Components
import {
  Layout,
  Text,
  Button,
  Icon,
  Div,
  FormGroup,
  FormControl,
  Span,
} from "unmatched/components";
// Span
import CreateVersion from "./CreateVersion";
import ManageQuestion from "./QuestionForm";
import SectionForm from "./SectionForm";
import Header from "../Header";
// import Footer from "../Footer";
import { useCreateSurveyContext } from "../Provider";
import util from "unmatched/utils";
import {
  createSurveySectionFact,
  createSurveyVersionFact,
  getSurveyQuestionsFact,
  getSurveySectionsFact,
  // createSurveyQuestionFact,
  cloneSurveyQuestionFact,
  patchSurveyFact,
  createSurveyInstructionFact,
  createSpecificSurveyQuestionFact,
} from "../../survey-api";
import Loader from "assets/images/Loader";
import { DateTime } from "luxon";
import icons from "assets/icons/icons";
import { SectionOptions } from "./SectionOptions";
// import { useOutsideClick } from "unmatched/hooks/useOutsideClick";

const { PlusIcon } = icons;

// const DashedButton = styled(Button)`
//   border: "1px dashed";
// `;

export const Empty = (props: any) => {
  const { PAIRICON } = util.images;
  return (
    <Div
      className="d-flex flex-column align-items-center"
      style={{ height: "calc(100vh - 120px)", padding: "150px 0" }}
      onClick={props.onClick}
    >
      <img src={PAIRICON} alt="" />
      <Text.P1 className="pb-4 text-center" style={{ margin: 30 }}>
        {props.message}
      </Text.P1>
    </Div>
  );
};

const CreateQuestions = (props: any) => {
  const {
    survey,
    version,
    versionsData,
    sectionsData,
    questionsData,
    updateValidators,
    // validateForms,
    setActiveVersionId,
    updateBuilderStatus,
    isDemoActive,
  } = useCreateSurveyContext();
  const params: any = useParams();
  const versionId = Number(params.versionId);
  const history = useHistory();
  // const optionsWrapRef = useRef(null);
  const metaCount = React.useMemo(
    () => ({
      versions: versionsData.data.length,
      sections: sectionsData.data.length,
      questions: questionsData.data.length,
    }),
    [
      versionsData.data.length,
      sectionsData.data.length,
      questionsData.data.length,
    ]
  );
  const [openSectionID, setOpenSectionID] = useState<number | null>(null);
  const [openAddSesion, setOpenAddSection] = useState<boolean>(false);
  const isUpwardReview =
    survey?.data && survey.data.type === util.enums.Survey.Upward;
  // const isUpwardReview = false;
  // useOutsideClick(optionsWrapRef, () => setOpenSectionID(null));
  const key = `versions-length-${survey.data.id}`;
  const getFormattedTime = () => {
    return util.date.getFormatedTime(new Date(), "MMMM dd, yyyy, HH:mm");
  };
  const [lastUpdated, setLastUpdated] = React.useState(survey.data.updatedAt);
  const lastModifiedLabel = `Last Updated ${DateTime.fromISO(
    new Date(lastUpdated).toISOString()
  ).toFormat(" LLL dd, yyyy, hh:mm a")}`;

  useEffect(() => {
    verifyVersion();
  }, [versionId]);

  useEffect(() => {
    updateValidators({
      key,
      validate: () => {
        const { versions, sections, questions } = metaCount;
        return new Promise((resolve: any, reject: any) => {
          if (!versions) {
            reject(new Error("Atleast one version is required"));
          } else if (!sections) {
            reject(new Error("Atleast one section is required"));
          } else if (!questions) {
            reject(new Error("Atleast one question is required"));
          } else {
            resolve();
          }
        });
      },
    });
  }, [metaCount]);

  useEffect(() => {
    if (!props.viewOnly) {
      updateBuilderStatus(util.enums.SurveyStatus.Questionnaire);
      return () => {
        setActiveVersionId(0);
        updateValidators({ key, validate: null });
      };
    }
  }, []);

  const verifyVersion = () => {
    let active = versionId || 0;
    if (versionId) {
      active = versionId;
    } else if (version.id) {
      active = version.id;
    } else if (versionsData.data.length) {
      active = versionsData.data[0].id;
    }
    setActiveVersionId(active);
    if (!active) return;
    history.push(
      util.appUrls.admin.survey.create.getQuestionsUrl(survey.data.id, active)
    );
    if (!versionId) return;
    getSections(versionId);
    getQuestions(versionId);
  };

  // Versions api calls

  const createSurveyVersion = () => {
    survey.setSaving(true);
    createSurveyVersionFact(params.id).then(
      (response: any) => {
        survey.setSaving(false);
        versionsData.setData([...versionsData.data, response]);
        history.push(
          util.appUrls.admin.survey.create.getQuestionsUrl(
            survey.data.id,
            response.id
          )
        );
      },
      (error: any) => {
        survey.setSaving(false);
        versionsData.onError(error);
      }
    );
  };

  // Sections api calls

  const getSections = (id: any) => {
    getSurveySectionsFact(id).then((response: any) => {
      sectionsData.onSuccess(response);
    });
  };

  const createSection = () => {
    survey.setSaving(true);
    createSurveySectionFact(versionId, sectionsData.getMetaConfig()).then(
      (response: any) => {
        survey.setSaving(false);
        const sections = [...sectionsData.data, response];
        sectionsData.onSuccess(sections);
      },
      (error: any) => {
        survey.setSaving(false);
        sectionsData.onError(error);
      }
    );
  };

  const onSectionClone = ({ section, questions }: any) => {
    sectionsData.setData(
      util.lib.orderBy([...sectionsData.data, section], "order,asc")
    );
    questionsData.setData(
      util.lib.orderBy([...questionsData.data, ...questions], "order,asc")
    );
  };

  // Questions api calls

  const getQuestions = (id: any) => {
    getSurveyQuestionsFact(id).then((response: any) => {
      questionsData.onSuccess(response);
    });
  };

  // const createQuestion = (sectionId: any) => {
  //   survey.setSaving(true);
  //   createSurveyQuestionFact(sectionId, questionsData.getMetaConfig()).then(
  //     (response: any) => {
  //       survey.setSaving(false);
  //       const questions = [...questionsData.data, response];
  //       questionsData.onSuccess(questions);
  //     }
  //   );
  // };

  const createSpecificQuestion = (sectionId: any, type: any) => {
    survey.setSaving(true);
    createSpecificSurveyQuestionFact(
      sectionId,
      questionsData.getMetaConfig(),
      type
    ).then((response: any) => {
      survey.setSaving(false);
      const questions = [...questionsData.data, response];
      questionsData.onSuccess(questions);
    });
  };

  const createInstruction = (sectionId: any) => {
    survey.setSaving(true);
    createSurveyInstructionFact(sectionId, questionsData.getMetaConfig()).then(
      (response: any) => {
        survey.setSaving(false);
        const questions = [...questionsData.data, response];
        questionsData.onSuccess(questions);
      }
    );
  };

  const onQuestionClone = (id: any) => {
    survey.setSaving(true);
    cloneSurveyQuestionFact(id).then((response: any) => {
      survey.setSaving(false);
      const questions = [...questionsData.data, response];
      questionsData.onSuccess(util.lib.orderBy(questions, "order,asc"));
    });
  };

  // Events and helper functions

  const onQuestionUpdate = (question: any) => {
    const updated = questionsData.data.map((item: any) =>
      question.id === item.id ? question : item
    );
    questionsData.setData(updated);
    setLastUpdated(getFormattedTime());
  };

  const onQuestionDelete = (id: any) => {
    const updated = questionsData.data.filter((item: any) => item.id !== id);
    questionsData.setData(updated);
  };

  // const validateForm = () => {
  //   _.forEach(validators, (item: any) => {
  //     if (item.validate) item.validate();
  //   });
  // };

  // Template Functions

  // const getAddSectionButton = () => {
  //   if (isDemoActive) return "";
  //   return (
  //     <Layout.Col xl={4}>
  //       <DashedButton
  //         variant="outline-warning"
  //         size="lg"
  //         className="py-2"
  //         onClick={createSection}
  //         block
  //       >
  //         <Icon icon="far fa-plus-circle" /> Click to Add Section
  //       </DashedButton>
  //     </Layout.Col>
  //   );
  // };

  // const getAddQuestionButton = (id: any) => {
  //   return (
  //     <DashedButton
  //       variant="outline-primary"
  //       key={`question-button-${id}`}
  //       size="lg"
  //       disabled={!id}
  //       className="py-2"
  //       onClick={() => createQuestion(id)}
  //       block
  //     >
  //       <Icon icon="far fa-plus-circle" /> Click to Add Question
  //     </DashedButton>
  //   );
  // };

  // const getAddInstructionButton = (id: any) => {
  //   return (
  //     <DashedButton
  //       variant="outline-success"
  //       key={`question-button-${id}`}
  //       size="lg"
  //       disabled={!id}
  //       className="py-2"
  //       onClick={() => createInstruction(id)}
  //       block
  //     >
  //       <Icon icon="far fa-plus-circle" /> Click to Add Instruction
  //     </DashedButton>
  //   );
  // };

  const getSectionsTemplate = () => {
    const sections = sectionsData.data;
    const questions = questionsData.data;
    if (!sections.length) {
      return (
        <Layout.Row className="pt-3 pb-5 ml-1">
          <PlusIcon
            className="cursor-pointer"
            onClick={() => {
              setOpenAddSection((s: boolean) => !s);
            }}
          />
          {openAddSesion && (
            <Div className="section-options-dropdown">
              <Div
                className="opt"
                onClick={() => {
                  createSection();
                  setOpenAddSection(false);
                }}
              >
                New Section
              </Div>
            </Div>
          )}
          {/* {getAddSectionButton()} */}
          {/* <Layout.Col>{getAddQuestionButton(0)}</Layout.Col> */}
        </Layout.Row>
      );
    }
    return (
      <>
        {sections.map((_section: any) => {
          const filteredQuestions = questions.filter(
            ({ sectionId }: any) => _section.id === sectionId
          );
          const questionNumbers = util.getQuestionNumbers(filteredQuestions);
          return (
            <Div key={`section-form-${_section.id}`}>
              <SectionForm
                sectionId={_section.id}
                updateValidators={updateValidators}
                setLoading={sectionsData.setSaving}
                showCloneDelete={!isDemoActive && !props.viewOnly}
                onSectionDataUpdate={() => {
                  setLastUpdated(getFormattedTime());
                }}
                onSectionClone={onSectionClone}
              >
                {filteredQuestions.map((item: any) => (
                  <ManageQuestion
                    key={item.id}
                    question={item}
                    index={questionNumbers[item.id]}
                    onPatch={onQuestionUpdate}
                    updateValidators={updateValidators}
                    onClone={onQuestionClone}
                    setLoading={questionsData.setSaving}
                    onDelete={onQuestionDelete}
                    hideCollectFeedback={isDemoActive}
                    hasDemoLabel={isDemoActive}
                    viewOnly={props.viewOnly}
                  />
                ))}
              </SectionForm>
              {!props.viewOnly && (
                <Layout.Row className="pt-3 pb-5 ml-1">
                  <PlusIcon
                    className="cursor-pointer"
                    onClick={() =>
                      setOpenSectionID((id: number) => {
                        if (id === _section.id) {
                          return null;
                        } else {
                          return _section.id;
                        }
                      })
                    }
                  />
                  {openSectionID === _section.id && (
                    <SectionOptions
                      section={_section}
                      createSection={createSection}
                      setOpenSectionID={setOpenSectionID}
                      createInstruction={createInstruction}
                      createSpecificQuestion={createSpecificQuestion}
                      onClose={() => {
                        setOpenSectionID(null);
                      }}
                    />
                  )}
                  {/* {getAddSectionButton()} */}
                  {/* <Layout.Col xl={4}>
                    {getAddQuestionButton(_section.id)}
                  </Layout.Col> */}
                  {/* <Layout.Col xl={4}>
                    {getAddInstructionButton(_section.id)}
                  </Layout.Col> */}
                </Layout.Row>
              )}
            </Div>
          );
        })}
      </>
    );
  };

  const getNavMetaTemplate = () => {
    return (
      <>
        {/* <Span>{lastModifiedLabel}</Span> */}
        <Text.P1 className="align-self-center">
          {questionsData.isSaving ||
          versionsData.isSaving ||
          sectionsData.isSaving ? (
            <>
              <Icon spin icon="fal fa-spinner-third" /> Saving
            </>
          ) : (
            lastModifiedLabel
          )}
        </Text.P1>
        {/* {isUpwardReview && (
          <Button className="ml-3" onClick={createSurveyVersion}>
            New Survey Version
          </Button>
        )} */}
      </>
    );
  };

  const getVersionsTemplate = () => {
    if (isDemoActive) return "";
    if (version.id) {
      const onChange = () => {
        const payload = {
          ...survey.data,
          hasDemographics: !survey.data.hasDemographics,
        };
        patchSurveyFact(survey.data.id, payload).then((response: any) => {
          survey.onSuccess(response);
        });
      };

      return (
        <Div className="border-bottom my-3">
          <Layout.Row>
            <Layout.Col xl={8}>
              <CreateVersion
                version={version}
                isUpwardReview={isUpwardReview}
                key={`version-${version.id}`}
                onUpdate={(payload: any) => {
                  versionsData.setData(
                    versionsData.data.map((item: any) =>
                      item.id === payload.id ? payload : item
                    )
                  );
                  setLastUpdated(getFormattedTime());
                }}
                setLoading={versionsData.setSaving}
                updateValidators={updateValidators}
                survey={survey}
              />
              {survey.data.type === "SurveyIndexEngagement" && (
                <FormGroup>
                  <Text.P1>
                    <FormControl.Switch
                      name={"enablePairings"}
                      checked={survey.data.hasDemographics}
                      onChange={onChange}
                    />{" "}
                    <Span className="ml-2">
                      Get demographic data from participants. (This data can
                      help in generating advanced analytics and reports.)
                    </Span>
                  </Text.P1>
                </FormGroup>
              )}
            </Layout.Col>
          </Layout.Row>
        </Div>
      );
    } else if (versionsData.isLoading) {
      return <Loader text={"Loading survey versions"} />;
    }
    return (
      <Empty
        message={
          'No versions are created yet, please click on "New Survey Version" Button'
        }
        onClick={createSurveyVersion}
      />
    );
  };

  const getHeaderTitleTemplate = () => {
    let _title = "Survey Questionnaire";
    if (isDemoActive) {
      _title = "Demographics";
    }
    return (
      <>
        <Div>
          <Text.H1 className="pb-2">{_title}</Text.H1>
          <Div className="sticky-tabs-container">
            {props.getVersions(createSurveyVersion)}
          </Div>
        </Div>
      </>
    );
  };

  return (
    <>
      <Header
        title={getHeaderTitleTemplate()}
        metaItem={getNavMetaTemplate()}
        breadcrumbs={props.breadcrumbs}
        styles={{ height: props.getVersions(createSurveyVersion) ? 120 : 100 }}
      />
      <Layout.Container className="pt-4" style={{ marginTop: 25 }} fluid>
        {getVersionsTemplate()}
        {getSectionsTemplate()}
      </Layout.Container>
      {/* <Footer
        onNext={() => {
          validateForms().then(() => {
            history.push(
              util.appUrls.admin.survey.create.getParticipantsUrl(survey.data.id)
            );
          });
        }}
        onBack={() => {
          validateForms().then(() => {
            history.push(
              util.appUrls.admin.survey.create.getUpwardReviewUrl(
                survey.data.id
              )
            );
          });
        }}
      /> */}
    </>
  );
};

export default CreateQuestions;
