import React from "react";
import {
  <PERSON>,
  Layout,
  But<PERSON>,
  Div,
  CustomModal as Mo<PERSON>,
  Icon,
} from "unmatched/components";
import { useEffect, useState } from "unmatched/hooks";
import { getSamplePairingDownload } from "../pairings-api";

interface RequiredFieldsProps {
  setScreen: (screen: number) => void;
  loading: boolean;
  data: string[];
  survey?: any;
}

export default function RequiredFields(props: RequiredFieldsProps) {
  const { setScreen, loading, data, survey } = props;
  const [isSurveyBuilder, setIsSurveyBuilder] = useState<boolean | undefined>();

  useEffect(() => {
    setIsSurveyBuilder(!window.location.href.includes("admin/survey/create"));
  }, []);

  return (
    <>
      <Modal.Body>
        <Text.P1 className="pb-4">
          Create a list of pairings with the reviewer and the reviewee pairs and
          upload the list to the platform. Below are the required fields to
          create pairings. You can download a sample excel/csv file to work the
          list.
        </Text.P1>
        {(survey?.data?.type === "SurveyIndexUpward" ||
        survey?.data?.type === "SurveyIndex360"
          ? ["rater_emp_id", "target_emp_id"]
          : data
        ).map((_d: string, i: number) => (
          <Text.P1 className="pb-2" key={i}>
            {_d}
          </Text.P1>
        ))}
      </Modal.Body>
      <Modal.Footer>
        <Div className="w-100">
          <Layout.Row>
            <Layout.Col className="col-7">
              <Text.P1
                className="pb-0 pointer"
                style={{ fontWeight: 500 }}
                onClick={() => getSamplePairingDownload({ f: "xlsx" })}
              >
                <Icon icon="far fa-arrow-alt-circle-down mr-1" />
                <u>Download sample excel(.xlsx) file</u>
              </Text.P1>
            </Layout.Col>
            <Layout.Col>
              {isSurveyBuilder && (
                <Button
                  type="button"
                  variant="outline-primary"
                  size="lg"
                  onClick={() => {
                    setScreen(5);
                  }}
                  className="ml-5"
                >
                  Add Pairings Manually
                </Button>
              )}
              <Button
                className="float-right"
                type="button"
                variant="primary"
                size="lg"
                onClick={() => {
                  setScreen(4);
                }}
                disabled={loading}
              >
                Next
              </Button>
            </Layout.Col>
          </Layout.Row>
        </Div>
      </Modal.Footer>
    </>
  );
}
