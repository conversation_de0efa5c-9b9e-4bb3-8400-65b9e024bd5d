import axios from "axios";
import util from "unmatched/utils";

export const reviewerStatsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.REVIEWER_STATS}`, config);
};
export const revieweeStatsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.REVIEWEE_STATS}`, config);
};
export const surveyStatsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.SURVEY_STATS}`, config);
};

export const surveyGraphStatsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_GRAPH_DATA}`, config);
};
