import { getSurveyByIdFact } from "pages/AdminApp/Survey/survey-api";
import React from "react";
import { Link, useParams } from "react-router-dom";
import {
  Div,
  Tabs,
  Tab,
  PageContainer,
  Text,
  Placeholder,
  Layout,
} from "unmatched/components";
import { useState, useQuery, useHistory, useDebounce } from "unmatched/hooks";
import useFilter from "pages/CommonFilters/hook";
// import util from "unmatched/utils";
import appUrls from "unmatched/utils/urls/app-urls";
import {
  getSectionAndQuestionFromID,
  // getExitSurveyDetails,
  getCommentsFromQuestionID,
  getOverallInfo,
  // getEngagmentDemographics,
  // getDemographicsSchema,
  patchCommentFlag,
  getExitSurveyItemized,
} from "../exit-analytics-api";
import Header from "../../../Shared/CustomHeader/CustomHeader";
import Heatmap from "./Heatmap/Heatmap";
import Itemwise from "./Itemwise/Itemwise";
import Overall from "./Overall/Overall";
import icons from "assets/icons/icons";
import styled from "styled-components";
import { Helmet } from "react-helmet";

const Fixed = styled(Div)`
  .nav-tabs {
    position: fixed;
    background: #fff;
    z-index: 1 !important;
    width: 100%;
    border-bottom: 1px solid rgb(222, 226, 230);
    padding-left: 90px;
    left: 0;
  }
`;

const { Graph } = icons;

const TYPES = {
  OVERALL: "overall",
  ITEMWISE: "itemwise",
  HEATMAP: "heatmap",
};

const ExitAnalyticsView = () => {
  const history = useHistory();
  const filtersState = useFilter();
  const urlParams = useParams<any>();
  const queryParams = useQuery();

  const [params, setParams] = useState({
    search: "",
    filter: queryParams.get("filter") || TYPES.OVERALL,
  });

  const onFilterChange = (filter: string) => {
    history.push(
      appUrls.admin.analytics.exit.getAnalyticsUrl(urlParams.id, filter)
    );
    setParams((_params: any) => ({
      ..._params,
      filter,
    }));
  };
  const [surveyVersionInfo, setSurveyInfo] = useState<any>({});
  const [sectionAndQuestion, setSectionAndQuestion] = useState<any>([]);
  const [overallData, setOverallData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<any>({
    id: "",
    name: "",
    section: "",
  });
  const [currentQuestionDetails, setCurrentQuestionDetails] = useState<any>([]);
  const [commentData, setCommentData] = useState<any>({});
  // const [demographicData, setDemographicData] = useState<any>({});
  // const [demographicSchema, setDemographicSchema] = useState<any>([]);
  const [commentFilters, setCommentFilter] = useState({
    sentiment: undefined,
    ordering: undefined,
    flag: undefined,
  });

  //Initial Call of they survey details
  React.useEffect(() => {
    filtersState.getFilters();
    call();
  }, []);

  const call = async () => {
    setIsLoading(true);
    try {
      const response = await getSectionAndQuestionFromID(urlParams.id);
      const surveyInfo = await getSurveyByIdFact(response.indexID);
      setSurveyInfo({ ...response, ...surveyInfo });


      const overallInfo = await getOverallInfo(
        { index_id: response.indexID }
      );
     
      setOverallData(overallInfo);
      // setSurveyInfo({ ...response, ...surveyInfo });
      // const demographicSchema = await getDemographicsSchema({
      //   index_id: response.indexID,
      // });
      // setDemographicSchema(demographicSchema);
      // if (demographicSchema.length > 0) {
      //   const demograhicsAPI = await getEngagmentDemographics({
      //     index_id: response.indexID,
      //     parent_cat: demographicSchema[0].key ?? "",
      //   });
      //   setDemographicData(demograhicsAPI);
      // }
      // setTimeout(async () => {
      //   console.log("response", surveyVersionInfo);
      //   if (surveyVersionInfo.hasDemographics) {
      //     alert("gfgf2")

      //   }
      // }, 500);

      if (response.sections.length > 0) {
        setSectionAndQuestion(
          response.sections.map((_dt: any) => {
            return {
              key: _dt.id,
              title: _dt.name,
              questions: _dt.components.map((_comp: any) => {
                return {
                  key: _comp.id,
                  title: _comp.label,
                  type: _comp.type,
                };
              }),
            };
          })
        );
        setCurrentQuestion({
          id: response.sections[0].components[0].id,
          name: response.sections[0].components[0].label,
          section: response.sections[0].id,
        });
      }
      setIsLoading(false);
    } catch (err: any) {
      console.log(err);
      setIsLoading(false);
      new Error(err?.message || "Error");
    }
  };

  React.useEffect(() => {
    if (currentQuestion.id.length === 0) return;
    getQuestionCall();
  }, [currentQuestion]);

  // const onDemographicChange = async (key: string) => {
  //   const demograhicsAPI = await getEngagmentDemographics({
  //     index_id: surveyVersionInfo.indexID,
  //     parent_cat: key,
  //   });

  //   setDemographicData(demograhicsAPI);
  // };

  const getQuestionCall = async (_filters?: any) => {
    setCommentFilter({
      sentiment: undefined,
      ordering: undefined,
      flag: undefined,
    });
    const qDetails = await getExitSurveyItemized({
      question_id: currentQuestion.id,
      ...filtersState.getParams(_filters || filtersState.selected),
    });
    setCurrentQuestionDetails(qDetails);
    getComments(1, _filters, true);
  };

  const getComments = async (
    page?: number,
    _filters?: any,
    isFilter?: boolean
  ) => {
    const addingFilter = () => {
      return isFilter ? undefined : commentFilters;
    };
    // console.log(isFilter);
    getCommentsFromQuestionID({
      question_id: currentQuestion.id,
      page: page ?? 1,
      page_size: 10,
      ...addingFilter(),
      ...filtersState.getParams(_filters || filtersState.selected),
    }).then((data: any) => {
      setCommentData(data);
    });
  };

  useDebounce(
    () => {
      if (
        commentFilters.flag !== undefined ||
        commentFilters.ordering !== undefined ||
        commentFilters.sentiment !== undefined
      ) {
        getComments();
      } else {
        return;
      }
    },
    200,
    [commentFilters]
  );

  const onFilterUpdate = (_filters: any) => {
    getQuestionCall(_filters);
  };

  const getFilterLink = (key: string, title: string) => {
    return (
      <Link
        className={params.filter === key ? "text-primary" : ""}
        to={appUrls.admin.analytics.exit.getAnalyticsUrl(
          urlParams.id,
          key
        )}
      >
        {title}
      </Link>
    );
  };

  const flagComment = async (id: string, flag: boolean) => {
    try {
      await patchCommentFlag({ id, flag });
      const comments = commentData.results.map((_d: any) => {
        if (_d.id === id) {
          // console.log("_d", _d);
          return {
            ..._d,
            flag,
          };
        }
        return _d;
      });
      setCommentData((_o: any) => {
        return { ..._o, results: comments };
      });
    } catch (err) {}
  };
  const getTabsTemplate = () => {
    return (
      <Fixed className="custom-tabs-2 new-tab-ui">
        <Tabs
          activeKey={params.filter}
          onSelect={(k: any) => onFilterChange(k)}
        >
          <Tab
            eventKey={TYPES.OVERALL}
            title={getFilterLink(TYPES.OVERALL, "Overall Analytics")}
          >
            <Div className="pt-4">
              <Overall
                data={overallData}
                setCurrentQuestion={setCurrentQuestion}
                sectionAndQuestion={sectionAndQuestion}
                onFilterChange={onFilterChange}
                isLoading={isLoading}
                // demographicData={demographicData}
                // demographicSchema={demographicSchema}
                // onDemographicChange={onDemographicChange}
                // surveyInfo={surveyVersionInfo}
              />
            </Div>
          </Tab>
          <Tab
            eventKey={TYPES.ITEMWISE}
            title={getFilterLink(TYPES.ITEMWISE, "Item-wise Analytics")}
          >
            <Div className="pt-4">
              <Itemwise
                user={params}
                comments={commentData}
                getComments={getComments}
                currentQuestionDetails={currentQuestionDetails}
                setCurrentQuestion={setCurrentQuestion}
                sectionAndQuestion={sectionAndQuestion}
                filtersState={filtersState}
                onFilterUpdate={onFilterUpdate}
                currentQuestion={currentQuestion}
                // demographicSchema={demographicSchema}
                isLoading={isLoading}
                commentFilters={commentFilters}
                setCommentFilter={setCommentFilter}
                flagComment={flagComment}
              />
            </Div>
          </Tab>
          <Tab
            eventKey={TYPES.HEATMAP}
            title={getFilterLink(TYPES.HEATMAP, "Comparative Analytics")}
          >
            <Div className="pt-4">
              <Heatmap
                filtersState={filtersState}
                // demographicSchema={demographicSchema}
                sectionAndQuestion={sectionAndQuestion}
                surveyInfo={surveyVersionInfo}
              />
            </Div>
          </Tab>
        </Tabs>
      </Fixed>
    );
  };

  return (
    <Div className="">
      <Helmet>
        <title>
          {surveyVersionInfo.name
            ? `Exit Analytics - ${surveyVersionInfo.name}`
            : "Loading..."}
        </title>
      </Helmet>
      <PageContainer>
        <Header
          title={
            <Text.H1 className="text-black mt-3 d-block" style={{minWidth: 360}}>
              {isLoading ? (
                <Placeholder width="col-12" />
              ) : (
                <>
                  {surveyVersionInfo.name}{" "}
                  
                </>
              )}
            </Text.H1>
          }
          breadcrumbs={[
            {
              label: "Analytics",
              icon: <Graph className="grey-icon__svg" />,
            },
            {
              label: "Exit Analytics",
              route: appUrls.admin.analytics.exit.list,
            },
            { label: surveyVersionInfo.name },
          ]}
          noShadow
          style={{ borderBottom: '0px !important' }}
        />
        <Layout.Container fluid className="pt-4">
          <Div>{getTabsTemplate()}</Div>
        </Layout.Container>
      </PageContainer>
    </Div>
  );
};

export default ExitAnalyticsView;
