import axios from "axios";
import api from "unmatched/utils/api";
import API_URLS from "unmatched/utils/urls/api-urls";

export const getSurveyQuestionsBySurveyIdFact = (
  id: number | string,
  meta?: any
) => {
  // const params = {
  //   type,
  // };
  const config = api.getConfigurations({}, meta);
  return axios.get(`${API_URLS.ADMIN_QUESTIONS}${id}`, config);
};

export const getSurveyQuestionsByVersionIdFact = (
  id: number | string,
  meta?: any
) => {
  // const params = {
  //   type,
  // };
  const config = api.getConfigurations({}, meta);
  return axios.get(`${API_URLS.ADMIN_QUESTIONS}${id}`, config);
};

export const getVisibilityMetaDetails = (
  id: number | string,
  meta?: any
) => {
  const config = api.getConfigurations({}, meta);
  return axios.get(`${API_URLS.ADMIN_VISIBILITY_META}?survey_version_id=${id}`, config);
}

export const postVisibilityFilters = (data: any) => {
  return axios.post(`${API_URLS.ADMIN_VISIBILITY_META}`, data); 
}