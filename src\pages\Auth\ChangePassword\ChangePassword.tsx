// Node modules
import React from "react";
import SetPasswordSchema, {
  ChangePasswordSchema,
  validatePassword,
} from "../Password/password-form";
import { useFormik, useCallback, useDispatch } from "unmatched/hooks";
import {
  Button,
  Div,
  Form,
  FormGroup,
  FormControl,
} from "unmatched/components";
// import Loader from "../Loader";
import PasswordForm from "../Password/PasswordForm";
import { useState } from "unmatched/hooks";
// import useSession from "unmatched/modules/session/hook";
import { setPasswordFact } from "../auth-api";
// import { getFieldErrorMessage, isFieldValid } from "unmatched/utils/formik";
// import { getUserFact } from "unmatched/modules/session/api";
import actions from "unmatched/modules/session/slice";
import useToastr from "unmatched/modules/toastr/hook";
import { SetPasswordSuccess } from "./SetPasswordSuccess";
import util from "unmatched/utils";

const { getFieldErrorMessage, isFieldValid } = util.formik;

const initialMeta = {
  content: "",
  success: false,
};

export default function ChangePassword(props: {
  user: any;
  closeModal: any;
  setPassSetComplete: any;
}) {
  const {
    user,
    user: { isPasswordSet },
    closeModal,
    setPassSetComplete: superPassSetComplete,
  } = props;

  const initState = {
    password: "",
    confirmPassword: "",
    tooltip: {
      number: false,
      alphabet: false,
      min: false,
      ...(isPasswordSet && { same: false }),
    },
    loading: false,
    error: {
      statusCode: 0,
      msg: "",
    },
  };
  // const session = useSession();
  const [meta, setMeta] = useState(initialMeta);
  const [state, setState] = useState(initState);
  const [passSetComplete, setPassSetComplete] = useState(false);
  const dispatch = useDispatch();
  const toastr = useToastr();

  const onSetChangePassword = async (data: any) => {
    setState((state: any) => ({
      ...state,
      loading: true,
    }));
    setMeta(initialMeta);
    try {
      const res = await setPasswordFact({ ...data });
      if (res?.data?.token) {
        // session.resetSession();
        // const {
        //   data: { token: authToken, expiry },
        // } = res;
        toastr.onSucces({ content: "Password changed successfully." });
        closeModal();
        window.location.href = `${window.location.origin}/#/logout`;
        // const userResponse = await getUserFact({ token: authToken });
        // session.login({
        //   token: authToken,
        //   expiry,
        //   user: userResponse,
        // });
      } else {
        dispatch(actions.setUser({ ...user, isPasswordSet: true }));
        setPassSetComplete(true);
        superPassSetComplete(true);
      }
    } catch (err) {
      setMeta((meta) => ({
        ...meta,
        content: "Wrong password. Please try again.",
        success: false,
      }));
    } finally {
      setState((state: any) => ({
        ...state,
        loading: false,
      }));
    }
  };
  const formikOptions = {
    initialValues: {
      ...(isPasswordSet && { oldPassword: "" }),
      password: "",
      confirmPassword: "",
    },
    validationSchema: isPasswordSet ? ChangePasswordSchema : SetPasswordSchema,
    onSubmit: (values: any) => {
      onSetChangePassword(values);
    },
  };

  const formik = useFormik(formikOptions);
  const onPasswordChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      formik.handleChange(e);
      const tooltip: any = validatePassword(e.target.value);
      setState((s) => ({
        ...s,
        tooltip: {
          ...tooltip,
          ...(isPasswordSet && {
            same: formik.values.oldPassword !== e.target.value,
          }),
        },
      }));
    },
    [formik]
  );
  const FeedbackTag = meta.success
    ? FormGroup.ValidFeedback
    : FormGroup.InValidFeedback;

  return !passSetComplete ? (
    <Form onSubmit={formik.handleSubmit}>
      <Div>
        <FeedbackTag text={meta.content} />
      </Div>
      <Div className="py-3">
        {isPasswordSet && (
          <FormGroup>
            <FormGroup.Label>Old Password</FormGroup.Label>
            <FormControl.Password
              name="oldPassword"
              autoFocus
              value={formik.values["oldPassword"] || ""}
              isValid={isFieldValid(formik, "oldPassword")}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              placeholder="Enter old password"
              style={{
                ...(getFieldErrorMessage(formik, "oldPassword") && {
                  borderColor: "#f34115",
                }),
              }}
            />
            <FormGroup.InValidFeedback
              text={getFieldErrorMessage(formik, "oldPassword")}
            />
          </FormGroup>
        )}
        <PasswordForm
          formik={formik}
          onPasswordChange={onPasswordChange}
          tooltip={state.tooltip}
          colXL={12}
        ></PasswordForm>
      </Div>
      <Button
        disabled={state.loading}
        className="float-right"
        size="lg"
        variant="primary"
        type="submit"
      >
        Submit
      </Button>
    </Form>
  ) : (
    <SetPasswordSuccess onDone={closeModal} />
  );
}
