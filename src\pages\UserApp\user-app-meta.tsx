import React from "react";
import { Icon } from "unmatched/components";
import util from "unmatched/utils";
import appUrls from "unmatched/utils/urls/app-urls";
// import icons from "../../assets/icons/icons";

// const { Cog, Chart } = icons;

const isLoggedIn = util.session.getToken;

const data = [
  {
    id: 1,
    title: "Home",
    icon: <Icon icon="far fa-home" />,
    getRoute: () => appUrls.user.dashboard.default,
    children: [],
  },
  // {
  //   id: 5,
  //   title: "FAQ",
  //   icon: <Icon icon="far fa-info-circle" />,
  //   getRoute: () => (isLoggedIn() ? appUrls.user.faq : appUrls.faq),
  //   children: [],
  // },
  {
    id: 4,
    title: "Contact Us",
    icon: <Icon icon="far fa-life-ring" />,
    getRoute: () => (isLoggedIn() ? appUrls.user.contactUs : appUrls.contactUs),
    children: [],
  },
];

export default data;
