import appUrls from "unmatched/utils/urls/app-urls";
import CreateDataLoad from "./CreateDataLoad/CreateDataLoad";
import ManageEmployeeData from "./ManageEmployeeData/ManageEmployeeData";
import ManageOngoingData from "./ManageOngoingData/ManageOngoingData";
import ManagePairingData from "./ManagePairingData/ManagePairingData";

const routes = [
  {
    name: "Add Employee/Participants",
    path: appUrls.admin.dataLoad.create,
    isExact: true,
    isPrivate: true,
    component: CreateDataLoad,
  },
  {
    name: "Manage Pairing Data",
    path: appUrls.admin.dataLoad.getManagePairingsUrl(":id"),
    isExact: true,
    isPrivate: true,
    component: ManagePairingData,
  },
  {
    name: "Manage Ongoing Pairing Data",
    path: appUrls.admin.dataLoad.getManageOngoingUrl(":id"),
    isExact: true,
    isPrivate: true,
    component: ManageOngoingData,
  },
  {
    name: "Manage A Employee Data",
    path: appUrls.admin.dataLoad.getManageEmployeessUrl(":id"),
    isExact: true,
    isPrivate: true,
    component: ManageEmployeeData,
  },
];

export default routes;
