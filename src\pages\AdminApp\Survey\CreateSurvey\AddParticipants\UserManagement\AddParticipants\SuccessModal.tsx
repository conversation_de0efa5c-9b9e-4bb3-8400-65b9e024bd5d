import React from "react";
import {
  Text,
  Layout,
  Div,
  CustomModal as <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "unmatched/components";
import Loader from "assets/images/Loader";
import icons from "assets/icons/icons";

export default function SuccessModal(props: any) {
  // React.useEffect(() => {
  //   const survey = props.survey?.data;
  //   if (survey) {
  //     console.log("here", props.survey.data)
  //     setIsOnGoingSurvey(!survey.isDraft && !survey.isLocked)
  //   }
  // }, [props.survey]);

  const ProcessViewer = () => {
    switch (props.apiResponse) {
      case null:
        return <Verify />;
      case !null:
        return <OnVerifySuccess apiResponse={props.apiResponse} />;
      default:
        return <OnVerifySuccess apiResponse={props.apiResponse} />;
    }
  };

  return (
    <>
      <Modal.Body>
        <Div
          className="d-flex align-items-center justify-content-center flex-column"
          style={{ minHeight: "50vh" }}
        >
          <ProcessViewer />
        </Div>
      </Modal.Body>
      <Modal.Footer>
        {props.apiResponse && (
          <Button
            className="float-right"
            onClick={() => {
              props.onHide();
              props.setScreen(1);
              props.setApiResponse(null);
            }}
          >
            Done
          </Button>
        )}
      </Modal.Footer>
    </>
  );
}

const Verify = () => {
  return (
    <>
      <Div className="d-inline">
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary f14">
              Uploading Records
            </Text.H2>
            <Text.P1 className="pb-4 f12">Please wait...</Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};

const OnVerifySuccess = (props: any) => {
  const data = props.apiResponse;
  const { SuccessTick } = icons;
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            {/* <Text.H2 className="pb-2 text-success f14">
              Verification in progress
            </Text.H2>
            <Text.P1 className="pb-4 f12">
              Keep revisiting this page to see if there was an issue while the
              pairings verification
            </Text.P1> */}
            <Text.H2 className="pb-2 text-success f14">
              All records are added/updated
            </Text.H2>
            <Text.P1 className="f12">
              {data.success_upload_count} new records found and added.
            </Text.P1>
            <Text.P1 className="f12">
              {data.rejected.duplicates} duplicates ignored.
            </Text.P1>
            <Text.P1 className="f12">
              {data.total_file_records} records found and validated in total.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        
      </Div>
    </>
  );
};
