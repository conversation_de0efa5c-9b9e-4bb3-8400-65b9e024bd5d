export enum SurveyStatus {
    ON_GOING = "ON_GOING",
    PAUSED = "PAUSED",
    SCHEDULED = "SCHEDULED",
    ENDED = "ENDED",
  }
  
  export enum NudgeSettings {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
  }
  
  export const SURVEY_STATES = {
    [SurveyStatus.ON_GOING]: {
      title: "Ongoing",
      variant: "success",
      canEnd: true,
      canPause: true,
      canRestart: false,
      canForceStart: false,
      canEditStartDate: false,
      canEditEndDate: true,
    },
    [SurveyStatus.PAUSED]: {
      title: "Paused",
      variant: "warning",
      canEnd: false,
      canPause: false,
      canRestart: true,
      canForceStart: false,
      canEditStartDate: true,
      canEditEndDate: true,
    },
    [SurveyStatus.SCHEDULED]: {
      title: "Scheduled",
      variant: "warning",
      canEnd: false,
      canPause: false,
      canRestart: false,
      canForceStart: true,
      canEditStartDate: true,
      canEditEndDate: true,
    },
    [SurveyStatus.ENDED]: {
      title: "Ended",
      variant: "light",
      canEnd: false,
      canPause: false,
      canRestart: false,
      canForceStart: true,
      canEditStartDate: false,
      canEditEndDate: false,
    },
  };
  
  export const SURVEY = {
    id: 1,
    title: "",
    name: "",
    nudgeSetting: NudgeSettings.LOW,
    // status: SurveyStatus.ON_GOING,
    status: SurveyStatus.PAUSED,
    // status: SurveyStatus.SCHEDULED,
    // status: SurveyStatus.ENDED,
    // startDate: "",
    // endDate: "",
  };
  
  export const NUDGE_OPTIONS = [
    {
      id: 1,
      key: NudgeSettings.LOW,
      title: "Low",
      description:
        "Weekly reminder emails to those who haven't logged in or haven't responded",
    },
    {
      id: 2,
      key: NudgeSettings.MEDIUM,
      title: "Medium (Recommended)",
      description:
        "Weekly reminder emails at the start and daily emails  towards the end to those who haven't logged in or haven't completed at least one survey",
    },
    {
      id: 3,
      key: NudgeSettings.HIGH,
      title: "High",
      description:
        "Weekly reminder emails at  the start. Twice a week follow up during the mid-survey period.  Daily emails towards the end to those who haven't logged in or haven't completed atleast one survey",
    },
  ];
  
  export const MANUAL_REMAINDERS = [
    {
      id: 1,
      key: "activationEmail",
      title: "Activation email",
      description: "all participants that are part of the survey.",
    },
    {
      id: 2,
      key: "activationRemainder",
      title: "Activation Reminder",
      description: "all participants that haven’t activated their accounts.",
    },
    {
      id: 3,
      key: "startSurveyRemainder",
      title: "Start Survey Reminder",
      description:
        "all participants that have activated their accounts but haven’t started the survey yet.",
    },
    {
      id: 4,
      key: "completeSurveyRemainder",
      title: "Complete Survey Reminder",
      description: "users with some pending submissions.",
    },
    {
      id: 5,
      key: "globalSurveyRemainder",
      title: "Global Survey Reminder",
      description: "all participants.",
    },
  ];
  
  export const CUSTOM_EMAIL_OPTIONS = [
    {
      id: 1,
      title: "All raters",
      description:
        "Weekly reminder emails to those who haven't logged in or haven't responded",
    },
    {
      id: 2,
      title: "All targets",
      description:
        "Weekly reminder emails at the start and daily emails  towards the end to those who haven't logged in or haven't completed at least one survey",
    },
    {
      id: 3,
      title: "All inactive raters",
      description:
        "Weekly reminder emails at  the start. Twice a week follow up during the mid-survey period.  Daily emails towards the end to those who haven't logged in or haven't completed atleast one survey",
    },
    {
      id: 4,
      title: "All activated raters",
      description:
        "Weekly reminder emails at  the start. Twice a week follow up during the mid-survey period.  Daily emails towards the end to those who haven't logged in or haven't completed atleast one survey",
    },
    {
      id: 5,
      title: "Admins Only",
      description:
        "Weekly reminder emails at  the start. Twice a week follow up during the mid-survey period.  Daily emails towards the end to those who haven't logged in or haven't completed atleast one survey",
    },
    {
      id: 6,
      title: "Include participants",
      description:
        "Weekly reminder emails at  the start. Twice a week follow up during the mid-survey period.  Daily emails towards the end to those who haven't logged in or haven't completed atleast one survey",
    },
  ];
  
  export const CUSTOM_EMAIL_INSERT_OPTIONS = [
    {
      id: 1,
      title: "Activation Link",
    },
    {
      id: 2,
      title: "Survey Link",
    },
    {
      id: 3,
      title: "First Name",
    },
    {
      id: 4,
      title: "Last Name",
    },
    {
      id: 5,
      title: "Survey Start",
    },
    {
      id: 6,
      title: "Survey End",
    },
    {
      id: 7,
      title: "Target Name",
    },
    {
      id: 8,
      title: "Firm Name",
    },
    {
      id: 9,
      title: "Survey Name",
    },
    {
      id: 10,
      title: "Target List",
    },
    {
      id: 11,
      title: "Rater's End Date",
    },
  ];

  export const CUSTOM_GENERIC_EMAIL_INSERT_OPTIONS = [
    {
      id: 1,
      title: "Activation Link",
    },
    {
      id: 3,
      title: "First Name",
    },
    {
      id: 4,
      title: "Last Name",
    },
    {
      id: 8,
      title: "Firm Name",
    },
  ];
  
  export const emailTemplateVarMap = {
    "&lt;Survey Start&gt;": "{{ survey_start }}",
    "&lt;Survey End&gt;": "{{ survey_end }}",
    "&lt;Firm Name&gt;": "{{ firm_name }}",
    "&lt;Survey Name&gt;": "{{ survey_name }}",
    "&lt;First Name&gt;": "{_ first_name _}",
    "&lt;Last Name&gt;": "{_ last_name _}",
    "&lt;Target Name&gt;": "{_ targets _}",
    "&lt;Activation Link&gt;": "{_ activation_link _}",
    "&lt;Survey Link&gt;": "{_ survey_link _}",
    "&lt;Target List&gt;": "{_ targets_ext _}",
    "&lt;Rater's End Date&gt;": "{_ target_end_date _}",
  };
  
  export const emailTemplateVarMapColorSub = {
    "&lt;Survey Start&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Survey Start&gt;</span>',
    "&lt;Survey End&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Survey End&gt;</span>',
    "&lt;Firm Name&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Firm Name&gt;</span>',
    "&lt;Survey Name&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Survey Name&gt;</span>',
    "&lt;First Name&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;First Name&gt;</span>',
    "&lt;Last Name&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Last Name&gt;</span>',
    "&lt;Target Name&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Target Name&gt;</span>',
    "&lt;Activation Link&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Activation Link&gt;</span>',
    "&lt;Survey Link&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Survey Link&gt;</span>',
    "&lt;Target List&gt;":
      '<span style="color: rgb(81, 140, 255);">&lt;Target List&gt;</span>',
    "&lt;Rater's End Date&gt;":
      `<span style="color: rgb(81, 140, 255);">&lt;Rater's End Date&gt;</span>`,
  };
  
  // workaround for non standard/stale templates, already stored in DB
  // can be removed later
  export const emailTemplateVarMap2 = {
    "&lt;Survey Start&gt;": "{{survey_start}}",
    "&lt;Survey End&gt;": "{{survey_end}}",
    "&lt;Firm Name&gt;": "{{firm_name}}",
    "&lt;Survey Name&gt;": "{{survey_name}}",
    "&lt;First Name&gt;": "{_ first_name _}",
    "&lt;Last Name&gt;": "{_ last_name _}",
    "&lt;Activation Link&gt;": "{_ activation_link _}",
    "&lt;Survey Link&gt;": "{_ survey_link _}",
    "&lt;Target List&gt;": "{_ targets_ext _}",
    "&lt;Rater's End Date&gt;": "{_ target_end_date _}",
  };
  
  export const emailTemplateVarMapText = {
    "<Survey Start>": "{{ survey_start }}",
    "<Survey End>": "{{ survey_end }}",
    "<Firm Name>": "{{ firm_name }}",
    "<Survey Name>": "{{ survey_name }}",
    "<First Name>": "{_ first_name _}",
    "<Last Name>": "{_ last_name _}",
    "<Target Name>": "{_ targets _}",
    "<Activation Link>": "{_ activation_link _}",
    "<Survey Link>": "{_ survey_link _}",
    "<Target List>": "{_ targets_ext _}",
    "<Rater's End Date>": "{_ target_end_date _}",
  };
  
  // workaround for non standard/stale templates, already stored in DB
  // can be removed later
  export const emailTemplateVarMapText2 = {
    "<Survey Start>": "{{survey_start}}",
    "<Survey End>": "{{survey_end}}",
    "<Firm Name>": "{{firm_name}}",
    "<Survey Name>": "{{survey_name}}",
    "<First Name>": "{_ first_name _}",
    "<Last Name>": "{_ last_name _}",
    "<Target Name>": "{_ targets _}",
    "<Activation Link>": "{_ activation_link _}",
    "<Survey Link>": "{_ survey_link _}",
    "<Target List>": "{_ targets_ext _}",
    "<Rater's End Date>": "{_ target_end_date _}",
  };
  