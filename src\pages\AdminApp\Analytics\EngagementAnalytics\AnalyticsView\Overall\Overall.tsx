import React, { useEffect } from "react";
import {
  Div,
  Layout,
  Text,
  //   ComboBasicFilter,
  //   ComboFilter,
  Table,
  Button,
  Placeholder,
  ComboBasicFilter,
  Icon,
} from "unmatched/components";
import { useTable } from "unmatched/hooks";

const Overall = (props: {
  data: any;
  sectionAndQuestion: any;
  setCurrentQuestion: Function;
  onFilterChange: Function;
  isLoading: boolean;
  demographicData: any;
  demographicSchema: any;
  onDemographicChange: Function;
  surveyInfo: any;
  isDemograhicLoading: boolean;
  demographicsMeta: any;
}) => {
  const {
    data,
    // sectionAndQuestion,
    setCurrentQuestion,
    onFilterChange,
    isLoading,
    demographicData,
    demographicSchema,
    onDemographicChange,
    surveyInfo,
    isDemograhicLoading,
    demographicsMeta,
  } = props;
  const tableMeta = useTable({});

  //   const [filters, setFilters] = React.useState({
  //     page: 1,
  //     totalPages: 0,
  //     section: "",
  //   });

  //   const onPageSelect = (page: number) => {
  //     setFilters({ ...filters, page });
  //   };
  const [currentSchema, setCurrentSchema] = React.useState("");
  useEffect(() => {
    setCurrentSchema(demographicSchema[0]?.key ?? "");
  }, [isLoading]);

  const getTimeRowsTemplate = (data: any, isPositive: boolean) => {
    return data.map((item: any, index: number) => {
      // const isEven = index % 2 === 0 || index === 0;
      // even={!isEven} key={item.key}
      return (
        <Table.Row key={index}>
          <Table.Data width="60px">
            <Text.P1>{index + 1}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.wording}</Text.P1>
          </Table.Data>
          <Table.Data width="150px">
            <Text.P1>
              {isPositive
                ? item.distribution_percentage.positive_percentage
                : item.distribution_percentage.negative_percentage}
            </Text.P1>
          </Table.Data>
          <Table.Data width="150px">
            <Text.P1>{item.mean}</Text.P1>
          </Table.Data>
          <Table.Data width="180px">
            <Text.P1 className="text-center">
              <Button
                variant="link"
                className="p-0 font-weight-normal fs-12"
                onClick={() => {
                  onFilterChange("itemwise");
                  setCurrentQuestion({
                    id: item.qid,
                    name: item.wording,
                    section: item.sid,
                  });
                }}
              >
                See detailed analytics
              </Button>
            </Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const qtFilters = {
    showUnflagged: false,
    cohart: {
      options: demographicSchema,
      selected: currentSchema,
    },
    applied: {
      options: [],
      selected: "",
    },
  };

  return (
    <Layout.Container fluid className="pt-3">
      <Text.H2 className="my-3">Survey Participation Statistics</Text.H2>

      <Div className="my-3">
        <Table
          columns={[
            { key: 0, label: "Total Participants", hasSort: false },
            { key: 1, label: "Not Visited", hasSort: false },
            { key: 2, label: "Visited", hasSort: false },
            { key: 3, label: "Participated", hasSort: false },
            { key: 4, label: "Participation %", hasSort: false },
          ]}
          isLoading={tableMeta.isLoading}
          rows={[{}]}
          customRows
          render={() => {
            return (
              <Table.Row>
                <Table.Data width="20%">
                  <Text.P1>
                    {isLoading ? (
                      <Placeholder width="col-4" />
                    ) : (
                      data?.stats?.total
                    )}
                  </Text.P1>
                </Table.Data>
                <Table.Data width="20%">
                  <Text.P1>
                    {isLoading ? (
                      <Placeholder width="col-4" />
                    ) : (
                      data?.stats?.notVisited
                    )}
                  </Text.P1>
                </Table.Data>
                <Table.Data width="20%">
                  <Text.P1>
                    {isLoading ? (
                      <Placeholder width="col-4" />
                    ) : (
                      data?.stats?.visited
                    )}
                  </Text.P1>
                </Table.Data>
                <Table.Data width="20%">
                  <Text.P1>
                    {isLoading ? (
                      <Placeholder width="col-4" />
                    ) : (
                      data?.stats?.participated
                    )}
                  </Text.P1>
                </Table.Data>
                <Table.Data width="20%">
                  <Text.P1>
                    {isLoading ? (
                      <Placeholder width="col-4" />
                    ) : (
                      data?.stats?.participatedPercentage
                    )}
                  </Text.P1>
                </Table.Data>
              </Table.Row>
            );
          }}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>

      {surveyInfo?.hasDemographics && (
        <Div>
          <Text.H3 className="mt-3">Participation Data</Text.H3>

          <Div className="row">
            <Div className="col-lg-6 col-md-8 col-12">
              <Div className="row my-2">
                <Div
                  className="col py-1 pr-0"
                  style={{ maxWidth: 150, minWidth: 150 }}
                >
                  <Text.P1>
                    <Icon icon="far fa-filter mr-3" /> Demographics
                  </Text.P1>
                </Div>
                <Div className="col" style={{ maxWidth: 250 }}>
                  <ComboBasicFilter
                    cohart={qtFilters.cohart}
                    applied={qtFilters.applied}
                    onCohartUpdate={(e: string) => {
                      setCurrentSchema(e);
                      onDemographicChange(e);
                      // setDemograhicLoading(true)
                    }}
                    onAppliedUpdate={() => ""}
                    isAppliedShown={false}
                    hideIcon
                  />
                </Div>
                <Div
                  className="col py-1 pr-0"
                  // style={{ maxWidth: 150, minWidth: 150 }}
                >
                  <Text.P1>
                    Participated:{" "}
                    {isLoading || isDemograhicLoading
                      ? "Loading..."
                      : demographicData.frequency}
                  </Text.P1>
                </Div>
              </Div>

              <Table
                columns={[
                  { key: 2, label: "No.", hasSort: false },
                  {
                    key: 3,
                    label: currentSchema,
                    hasSort: false,
                  },
                  {
                    key: 4,
                    label: "Participated",
                    hasSort: false,
                  },
                  { key: 5, label: "Percentage", hasSort: false },
                ]}
                isLoading={isLoading || isDemograhicLoading}
                rows={demographicData.results}
                customRows
                render={() => {
                  return demographicData.results.map(
                    (_dt: any, index: number) => (
                      <Table.Row>
                        <Table.Data width="50px">
                          <Text.P1>{index + 1}</Text.P1>
                        </Table.Data>
                        <Table.Data>
                          <Text.P1>{_dt[currentSchema]}</Text.P1>
                        </Table.Data>
                        <Table.Data>
                          <Text.P1>{_dt.participated}</Text.P1>
                        </Table.Data>
                        <Table.Data>
                          <Text.P1>{_dt.percentage}%</Text.P1>
                        </Table.Data>
                      </Table.Row>
                    )
                  );
                }}
                hasPagination
                activePage={demographicsMeta.page}
                pages={demographicsMeta.totalPages}
                onPageSelect={(n: number) =>
                  onDemographicChange(currentSchema, n)
                }
              />
            </Div>
          </Div>
        </Div>
      )}

      <Div>
        <Text.H3 className="mt-3">Summary of Strengths</Text.H3>
        <Div className="row">
          <Div className="col-md-8">
            <Text.P2 className="mb-4 mt-3">
              These are the top 5 areas in which most of respondents have rated
              highest.
            </Text.P2>
          </Div>
        </Div>
        <Table
          columns={[
            { key: 2, label: "No.", hasSort: false },
            {
              key: 3,
              label: "Question",
              hasSort: false,
            },
            {
              key: 4,
              label: "Positive Response",
              hasSort: false,
            },
            { key: 5, label: "Average (out of 5)", hasSort: false },
            { key: 11, label: "", hasSort: false },
          ]}
          isLoading={isLoading}
          rows={data?.top}
          customRows
          render={() => getTimeRowsTemplate(data.top, true)}
          hasPagination
          activePage={0}
          pages={0}
        />
      </Div>
      <Div>
        <Text.H3 className="mt-3">Opportunities for improvement</Text.H3>
        <Div className="row">
          <Div className="col-md-8">
            <Text.P2 className="mb-4 mt-3">
              These are the bottom 5 areas in which most of the respondents have
              rated lowest.
            </Text.P2>
          </Div>
        </Div>
        <Table
          columns={[
            { key: 2, label: "No.", hasSort: false },
            {
              key: 3,
              label: "Question",
              hasSort: false,
            },
            { key: 4, label: "Negative Response", hasSort: false },
            { key: 5, label: "Average (out of 5)", hasSort: false },
            { key: 11, label: "", hasSort: false },
          ]}
          isLoading={isLoading}
          rows={data?.bottom}
          customRows
          render={() => getTimeRowsTemplate(data.bottom, false)}
          hasPagination
          activePage={0}
          pages={0}
        />
      </Div>
    </Layout.Container>
  );
};

Overall.propTypes = {};

export default Overall;
