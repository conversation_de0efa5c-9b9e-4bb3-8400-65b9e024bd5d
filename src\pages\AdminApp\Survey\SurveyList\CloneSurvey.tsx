import React from "react";
import { useHistory } from "react-router";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import { cloneSurveyFact, getSurveyNamesFact } from "../survey-api";
import surveyCreateCore from "unmatched/survey/creation/components";

const CloneSurveyComponent = surveyCreateCore.CloneSurvey;

const CloneSurvey = (props: any) => {
  const toastr = useToastr();
  const history = useHistory();

  const onCloneSuccess = (data: any) => {
    history.push(util.appUrls.admin.survey.create.getUpwardReviewUrl(data.id));
  };

  return (
    <CloneSurveyComponent
      cloneSurveyFact={cloneSurveyFact}
      getSurveyNamesFact={getSurveyNamesFact}
      toastr={toastr}
      onCloneSuccess={onCloneSuccess}
      toggleClone={props.toggleClone}
    />
  );
};

export default CloneSurvey;
