import DataLoad from "./DataLoad/DataLoad";
import appUrls from "unmatched/utils/urls/app-urls";
import Survey from "./Survey/Survey";
import Dashboard from "./Dashboard/Dashboard";
import Analytics from "./Analytics/Analytics";
import Reports from "./Reports/Reports";
// import Settings from "./Settings/Settings";
import Email from "./Email/Email";

const routes = [
  {
    name: "Dashboard",
    path: appUrls.admin.home,
    isExact: false,
    isPrivate: true,
    component: Dashboard,
  },
  {
    name: "Analytics",
    path: appUrls.admin.analytics.default,
    isExact: false,
    isPrivate: true,
    component: Analytics,
  },
  {
    name: "Reports",
    path: appUrls.admin.reports.default,
    isExact: false,
    isPrivate: true,
    component: Reports,
  },
  // {
  //   name: "Settings",
  //   path: appUrls.admin.settings.getURL(),
  //   isExact: false,
  //   isPrivate: true,
  //   component: Settings,
  // },
  {
    name: "Survey",
    path: appUrls.admin.survey.default,
    isExact: false,
    isPrivate: true,
    component: Survey,
  },
  {
    name: "Email",
    path: appUrls.admin.email,
    isExact: false,
    isPrivate: true,
    component: Email,
  },
  {
    name: "ALL Pairings",
    path: appUrls.admin.dataLoad.default,
    isExact: false,
    isPrivate: true,
    component: DataLoad,
  },
];

export default routes;
