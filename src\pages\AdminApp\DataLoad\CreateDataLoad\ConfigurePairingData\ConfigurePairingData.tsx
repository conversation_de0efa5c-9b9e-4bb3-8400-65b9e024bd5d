import React from "react";
import { Div, CustomModal as Modal } from "unmatched/components";
import RequiredFields from "./RequiredFields";
import UploadFile from "./UploadFile";
// import PublishFile from "./PublishFile";
import ConfigureRules from "./ConfigureRules";
import { getRequiredPairFieldFact } from "../../dataload-api";

export default function ConfigurePairingData(props: any) {
  // let screen: number = props.screen;
  const [isLoading, setIsLoading] = React.useState(true);
  const [requiredFields, setRequiredFields] = React.useState([]);

  React.useEffect(() => {
    async function getRequiredFields() {
      const response = await getRequiredPairFieldFact();
      const data = await response.data.fields;
      setRequiredFields(data);
      setIsLoading(false);
    }
    getRequiredFields();
  }, []);
  const [screen, setScreen] = React.useState<number>(1);
  const ScreenView = (inPosition: number, props: any) => {
    switch (inPosition) {
      case 1:
        return (
          <RequiredFields
            {...props}
            setScreen={setScreen}
            loading={isLoading}
            data={requiredFields}
          />
        );
      case 2:
        return <UploadFile {...props} setScreen={setScreen} />;
      case 3:
        return <ConfigureRules {...props} setScreen={setScreen} />;
      default:
        return "";
    }
  };
  return (
    <Modal
      {...props}
      onHide={() => {
        setScreen(1);
        props.onHide();
      }}
      children={
        <>
          <Modal.Header closeButton>
            <Modal.Title>Add Pairings</Modal.Title>
          </Modal.Header>
          <Div>{ScreenView(screen, props)}</Div>
        </>
      }
    />
  );
}
