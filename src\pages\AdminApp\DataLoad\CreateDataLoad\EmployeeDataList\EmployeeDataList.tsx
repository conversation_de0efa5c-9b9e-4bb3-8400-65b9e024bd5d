import React from "react";
import { Text, Div, Table, Dropdown } from "unmatched/components";
import { Empty } from "../CreateDataLoad";
import { useTable, useHistory } from "unmatched/hooks";
import styled from "styled-components";
import appUrls from "unmatched/utils/urls/app-urls";
import {
  downloadUserFileInfoFact,
  getAllUserFilesFact,
  deleteUserFileFact,
} from "../../dataload-api";
import useToastr from "unmatched/modules/toastr/hook";
import util from "unmatched/utils";
import { keys, map, get } from "lodash";

export const StyledActionsDropdown = styled(Dropdown)`
  position: absolute;
  width: 60px;
  height: 38px;
  display: flex;
  margin-left: -8px;
  margin-top: -10px;
`;

export default function EmployeeDataList(props: any) {
  const { data, setUserData, isLoading, filters } = props;
  const toastr = useToastr();
  const tableMeta = useTable({});
  const history = useHistory();
  const [columnsData, setColumnsData] = React.useState<any>([
    // {
    //   key: 1,
    //   renderItem: getCheckAllTemplate,
    //   hasSort: false,
    // },
    { key: 2, label: "No.", hasSort: false },
    {
      key: 3,
      label: "Title",
      hasSort: true,
      sortKey: "title",
      sortValue: "asc",
    },
    {
      key: 4,
      label: "Total Records",
      hasSort: true,
      sortKey: "records",
      sortValue: "asc",
    },
    { key: 5, label: "Tags", hasSort: false },
    {
      key: 6,
      label: "Uploaded On",
      hasSort: true,
      sortKey: "uploaded_on",
      sortValue: "asc",
    },
    {
      key: 7,
      label: "Last Updated On",
      hasSort: true,
      sortKey: "last_uploaded_on",
      sortValue: "asc",
    },
    { key: 8, label: "Action", hasSort: false },
  ]);

  const getColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  React.useEffect(() => {
    tableMeta.updatePagination({ totalPages: data?.count_pages });
    //eslint-disable-next-line
  }, [data]);

  // const getCheckAllTemplate = () => (
  //   <FormGroup className="pb-1">
  //     <FormControl.Checkbox>
  //       <FormControl.Checkbox.Input
  //         checked={tableMeta.selectAll}
  //         onChange={(evt: any) => onCheckAll(evt.target.checked)}
  //       />
  //     </FormControl.Checkbox>
  //   </FormGroup>
  // );

  const checkSelected = (item: any) => {
    return tableMeta.isSelected(item.id);
  };

  // const onSelect = (item: any) => {
  //   tableMeta.onSelect(item, "id");
  // };

  // const onCheckAll = (checked: boolean) => {
  //   tableMeta.onSelectAll(data, checked, "id");
  // };

  const onUserFileDelete = async (file_id: string) => {
    try {
      await deleteUserFileFact(file_id);
      toastr.onSucces({
        title: "Success",
        content: "User File Deleted Successfully",
      });
      const dataUser = data.results.filter((item: any) => item.id !== file_id);
      setUserData((_d: any) => {
        return { ..._d, results: dataUser };
      });
    } catch (error: any) {
      toastr.onError(error);
    }
  };

  const onPageSelect = async (page: number) => {
    try {
      const response = await getAllUserFilesFact({
        page,
        search: "",
        page_size: 10,
        ordering: props.ordering,
      });
      setUserData(response.data);
      tableMeta.onPageSelect(page);
    } catch (e: any) {
      // throw new Error(e.message || "");
    }
  };

  const getRowsTemplate = () => {
    return data.results.map((item: any, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          onClick={() =>
            history.push(appUrls.admin.dataLoad.getManageEmployeessUrl(item.id))
          }
          even={!isEven}
          key={item.empId}
          selected={checked}
        >
          {/* <Table.Data width="30px">
              <FormGroup>
                <FormControl.Checkbox>
                  <FormControl.Checkbox.Input
                    onChange={() => onSelect(item)}
                    onClick={(evt: any) => evt.stopPropagation()}
                    checked={checked}
                  />
                </FormControl.Checkbox>
              </FormGroup>
            </Table.Data> */}
          <Table.Data width="70px">
            <Text.P1>{index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data width="40%">
            <Text.P1>
              {item.title ?? <i className="text-muted">No Name</i>}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.records}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.tags.length > 0 ? (
                item.tags.join(", ")
              ) : (
                <span className="text-muted">-</span>
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.uploaded_on,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {util.date.getBrowserTime(
                item.updated_at,
                "MMMM dd, yyyy, HH:mm"
              )}
            </Text.P1>
          </Table.Data>
          <Table.Data style={{ position: "relative" }}>
            <StyledActionsDropdown
              onClick={(e: any) => e.stopPropagation()}
              style={{ position: "absolute" }}
            >
              <OptionButton
                className="d-flex align-items-center justify-content-center btn-block"
                id={"dropdown-button-drop-start"}
                style={{ height: "100%" }}
              >
                ...
              </OptionButton>

              <Dropdown.Menu
                alignRight
                className="text-right shadow-lg"
                style={{ zIndex: 1 }}
              >
                <Dropdown.Item href="#">Reupload /Update</Dropdown.Item>
                <Dropdown.Item
                  href="#"
                  onClick={() =>
                    downloadUserFileInfoFact(item.id, "ERROR", () =>
                      toastr.errorToast("No Rejected Records Found")
                    )
                  }
                >
                  Rejected Records
                </Dropdown.Item>
                <Dropdown.Item href="#">Copy</Dropdown.Item>
                <Dropdown.Item
                  href="#"
                  onClick={() =>
                    downloadUserFileInfoFact(item.id, "DOWNLOAD", () => "")
                  }
                >
                  Download
                </Dropdown.Item>
                <Dropdown.Item
                  className="text-danger"
                  href="#"
                  onClick={() => onUserFileDelete(item.id)}
                >
                  Delete
                </Dropdown.Item>
              </Dropdown.Menu>
            </StyledActionsDropdown>
          </Table.Data>
        </Table.Row>
      );
    });
  };
  return (
    <Div className="mt-4">
      <Table
        columns={getColumns()}
        isLoading={isLoading}
        rows={data.results}
        customRows
        render={() => getRowsTemplate()}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={onPageSelect}
        onSort={(item: any) => {
          const label = util.label.getSortingLabel(
            item.sortKey,
            item.sortValue
          );
          setColumnsData((_columns: any) => {
            return Object.values(tableMeta.resetColumns(_columns, item));
          });
          props.setOrdering(label);
          props.dataCall({ empOrdering: label });
        }}
        {...(filters.search && { notFoundMsg: util.noSearchRecordsFoundMsg })}
      />
      {!isLoading && data.results.length === 0 && <Empty />}
    </Div>
  );
}

const OptionButton = styled(Dropdown.Toggle)`
  height: 10px;
  background: none !important;
  color: #c4c4c4 !important;
  padding: 0;
  border: none;
  position: relative;
  z-index: 0;
  &:focus,
  &:hover,
  &:active {
    background: none;
    color: #000 !important;
    outline: none;
    box-shadow: none !important;
  }
`;
