import React from "react";

const Context = React.createContext<any>({});

const AdminProvider = (props: any) => {
  const [width, setWidth] = React.useState(60);
  const [margin, setMargin] = React.useState(true);
  const [sidebar, setSidebar] = React.useState(true);

  return (
    <Context.Provider
      value={{
        width,
        margin,
        sidebar,
        setWidth,
        setMargin,
        setSidebar,
      }}
    >
      {props.children}
    </Context.Provider>
  );
};

export const useAdminContext = () => {
  const context = React.useContext(Context);
  const { width, margin, sidebar } = context;

  const setSidebar = (toggle: boolean) => {
    if (context.setSidebar) context.setSidebar(toggle);
  };

  const setMargin = (toggle: boolean) => {
    if (context.setMargin) context.setMargin(toggle);
  };

  return {
    width,
    margin,
    sidebar,
    setSidebar,
    setMargin,
  };
};

export default AdminProvider;
