import React from "react";
import { Link, Redirect, Route } from "react-router-dom";
import AUTH_ROUTES from "./auth-routes";
import { Layout, Text, Icon, Div, Span } from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import AppRoutes from "../AppRoutes";
import { useHistory, useEffect } from "unmatched/hooks";
import useSession from "unmatched/modules/session/hook";
import background from "assets/images/loginbg.png";
import styled from "styled-components";

const Wrap = styled(Div)`
  background-size: cover;
  height: 100vh;
  background-position: 100% center;
`;

const Highlighters = () => {
  return (
    <nav className="navbar navbar-light">
      <Div className="nav-item ml-auto mr-auto">
        <Text.P1>
          <Div className="pt-3 text-dark">
            <Span className="px-2">
              <Icon icon="fas fa-shield-alt" variant="primary" />{" "}
              <span className="pl-2">Highly Secure</span>
            </Span>
            <Span className="px-2">
              <Icon icon="fas fa-key" variant="primary" />{" "}
              <span className="pl-2">Military Grade</span> Encryption
            </Span>
            <Span className="px-2">
              <Icon icon="fas fa-lock" variant="primary" />{" "}
              <span className="pl-2">GDPR Compliant</span>
            </Span>
          </Div>
        </Text.P1>
      </Div>
    </nav>
  );
};

const Footer = () => {
  const year = new Date().getFullYear();
  return (
    <nav className="navbar navbar-light">
      <Div className="nav-item ml-auto mr-auto">
        <Text.P2>
          <Div className="pt-4">
            <a className="text-dark" rel="noreferrer" href={appUrls.terms}>
              Terms
            </a>{" "}
            •{" "}
            <a className="text-dark" rel="noreferrer" href={appUrls.privacy}>
              Privacy Policy
            </a>{" "}
            •{" "}
            <a
              className="text-dark"
              rel="noreferrer"
              href={appUrls.confidentiality}
            >
              Confidentiality
            </a>{" "}
            •{" "}
            <Link className="text-dark" to={appUrls.faq}>
              FAQ
            </Link>{" "}
            •{" "}
            <Link className="text-dark" to={appUrls.contactUs}>
              Contact Us
            </Link>
          </Div>
          <p className="m-0 text-center pt-3">Unmatched © {year}</p>
        </Text.P2>
      </Div>
    </nav>
  );
};

export default function Auth() {
  const history = useHistory();
  const { isLoggedIn } = useSession();

  useEffect(() => {
    if (isLoggedIn()) {
      history.push(appUrls.admin.default);
    }
  }, []);

  return (
    <Wrap style={{ backgroundImage: `url(${background})` }}>
      <Layout.Container>
        <AppRoutes routes={AUTH_ROUTES}>
          <Route exact path={appUrls.auth.default}>
            <Redirect to={appUrls.auth.login} />
          </Route>
        </AppRoutes>
        <Highlighters />
        <Footer />
      </Layout.Container>
    </Wrap>
  );
}
