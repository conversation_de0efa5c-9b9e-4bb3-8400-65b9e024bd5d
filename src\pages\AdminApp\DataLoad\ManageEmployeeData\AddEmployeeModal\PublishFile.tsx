import React from "react";
import {
  Text,
  Layout,
  Div,
  Button,
  // FormControl,
  // Form,
  // FormGroup,
  CustomModal as Modal,
} from "unmatched/components";
// import { Modal.Body, ModalFooter } from "../CreateDataLoad.style";
// import data from "../../../../../assets/images/images";
import icons from "assets/icons/icons";
import Loader from "assets/images/Loader";
import { Link } from "react-router-dom";

const PublishFile = () => {
  const [Status, setStatus] = React.useState(0);

  const ProcessViewer = () => {
    switch (Status) {
      case 1:
        return <OnVerifySuccess setStatus={setStatus} />;

      case 2:
        return <OnVerifyFailure />;

      default:
        return <></>;
    }
  };

  const footerButton = () => {
    if (Status === 0) {
      return <Div className="py-3"></Div>;
    } else if (Status === 1) {
      return (
        <Button
          className="float-right"
          type="button"
          variant="primary"
          size="lg"
          onClick={() => ""}
        >
          Save and Next
        </Button>
      );
    } else {
      return (
        <Button
          className="float-right"
          type="button"
          variant="outline-primary"
          size="lg"
          onClick={() => ""}
        >
          Reupload
        </Button>
      );
    }
  };
  return (
    <>
      <Modal.Body>
        <Div
          className="d-flex align-items-center justify-content-center flex-column"
          style={{ minHeight: "50vh" }}
        >
          {Status === 0 ? <Verify setStatus={setStatus} /> : <ProcessViewer />}
          {/* <OnVerifySuccess/> */}
        </Div>
      </Modal.Body>
      <Modal.Footer>{footerButton()}</Modal.Footer>
    </>
  );
};

const Verify = (props: any) => {
  React.useEffect(() => {
    setTimeout(() => {
      // let isSuccess = window.confirm("Confirm?");
      // if (isSuccess) {
      //   props.setStatus(1);
      // } else {
      //   props.setStatus(2);
      // }
      props.setStatus(1);
    }, 3000);
  }, [props]);
  return (
    <>
      <Div className="d-inline">
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary">
              Verifying required mandatory fields
            </Text.H2>
            <Text.P1 className="pb-4">Please wait...</Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col style={{ maxWidth: 70 }}>
            <Loader size={50} />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-primary">Verifying data</Text.H2>
            <Text.P1 className="pb-4">
              Checking for invalid and duplicate records
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};
const OnVerifySuccess = (props: any) => {
  const { SuccessTick } = icons;
  React.useEffect(() => {
    setTimeout(() => {
      // let isSuccess = window.confirm("Confirm?");
      // if (isSuccess) {
      //   props.setStatus(1);
      // } else {
      //   props.setStatus(2);
      // }
      props.setStatus(1);
    }, 3000);
  }, [props]);
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-success">
              Mandatory fields are verified!
            </Text.H2>
            <Text.P1 className="pb-4">
              Your pairings match the exisiting employee data.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <SuccessTick width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-success">
              All records are valid!
            </Text.H2>
            <Text.P1 className="">35 new records found and added.</Text.P1>
            <Text.P1 className="">
              12 existing records have been updated.
            </Text.P1>
            <Text.P1 className="pb-4">
              47 records found and validated in total.
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};

const OnVerifyFailure = () => {
  const { DangerAlert } = icons;
  return (
    <>
      <Div className="d-inline">
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <DangerAlert width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-danger">
              Mandatory fields couldn't verified!
            </Text.H2>
            <Text.P1 className="pb-4">Something wrong text.</Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row className="my-4">
          <Layout.Col style={{ maxWidth: 70 }}>
            <DangerAlert width="24px" height="24px" className="mx-3 my-2" />
          </Layout.Col>
          <Layout.Col>
            <Text.H2 className="pb-2 text-danger">Invalid records</Text.H2>
            <Text.P1 className="pb-4">The records are invalid.</Text.P1>
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col>
            <Text.P1 className="pb-2">
              Download Sample File{" "}
              <Link to="/" className="mx-3">
                Excel (.xls)
              </Link>{" "}
              <Link to="/" className="mx-3">
                CSV
              </Link>
            </Text.P1>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </>
  );
};

export default PublishFile;
