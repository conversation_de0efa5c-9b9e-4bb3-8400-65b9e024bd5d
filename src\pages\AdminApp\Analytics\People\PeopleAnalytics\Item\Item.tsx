import React, { useEffect } from "react";
import {
  Div,
  Layout,
  Text,
  Button,
  Icon,
  HeatMapTable,
  // Table,
  Dropdown,
  OverlayTrigger,
  Tooltip,
} from "unmatched/components";
// import CompareChart from "../../../LineChart/LineChart";
import DownloadExcel from "../../../DownloadExcel";
// import Legend from "../../../Legend";
// import LegendFor from "../../../LegendFor";
// import GRAPH_DATA from "../../../LineChart/graph-meta";
import ItemList from "../../../ItemList";
import {
  getAllSectionAndItemsFact,
  getItemizedGraphDataFact,
  getItemizedHeatMapFact,
} from "../../people-api";
import useToastr from "unmatched/modules/toastr/hook";
import { useXHR } from "unmatched/hooks";
import useFilter from "pages/CommonFilters/hook";
// import TimeSeriesGraph from "../../../TimeSeriesGraph/TimeSeriesGraph";
// import util from "unmatched/utils";
import SideSingleSelect from "./SideSingleSelect";
import SideMultiSelect from "./SideMultiSelect";
import useAnalyticsFilters from "pages/AdminApp/Analytics/Shared/AnalyticsFilters/hook";
import AnalyticsFilters from "pages/AdminApp/Analytics/Shared/AnalyticsFilters/AnalyticsFilters";
// import util from "unmatched/utils";
import TimeSeriesGraphNew from "pages/AdminApp/Analytics/TimeSeriesGraph/TimeSeriesGraphNew";
// import PropTypes from 'prop-types';

const TYPES = {
  "360": "surveyindex360",
  upward: "surveyindexupward",
  self: "surveyindexself",
  "360Group": "surveyindex360group",
};

const Itemized = (props: { userId: any; user: any }) => {
  const { user, userId } = props;

  const [showItems, toggleItems] = React.useState(false);
  const [showDropdown, setShowDropdown] = React.useState(false);

  const [showSingleSelect, setShowSingleSelect] = React.useState(false);
  const [showMultiSelect, setShowMultiSelect] = React.useState({
    upward: false,
    self: false,
  });

  const [singleItems, setSingleItems] = React.useState<any>([]);
  const [upwardItems, setUpwardItems] = React.useState<any>([]);
  const [selfItems, setSelfItems] = React.useState<any>([]);

  const [selectedItems, setSelelectedItems] = React.useState({
    upward: [],
    self: [],
  });

  const [items, setItems]: any = React.useState({
    upward: [],
    self: [],
    single: [],
    // ["360"]: [],
  });

  const [columns, setColumns]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });

  const toastr = useToastr();

  const graph = useXHR({
    defaultResponse: {
      data: [],
      labels: [],
    },
  });
  const filtersState = useFilter();

  const [activeSingleQuestion, setActiveSingleQuestion] = React.useState({
    id: "",
    name: "",
  });

  const [selectedTimeGraphCompareList, setSelectedTimeGraphCompareList] =
    React.useState<any>({});
  // const [selfYears, setSelfYears]: any = React.useState([]);

  useEffect(() => {
    if (!Object.values(selectedTimeGraphCompareList).length) return;
    const items = selections["single"];

    getGraphData(
      {
        compare_list: Object.values(selectedTimeGraphCompareList),
      },
      {
        ...items,
        id: activeSingleQuestion.id,
      }
      // selections["single"]
    );
  }, [selectedTimeGraphCompareList]);

  const onGraphLoad = (year?: any) => {
    selections["single"]
      .getFilters({
        year,
        user_id: user.id,
        // resource_types: TYPES["360"],
      })
      .then(
        (_filters: any) => {
          const filteredSurveys = _filters.surveys.filter(
            (item: any) => item.type !== TYPES["self"]
          );
          const [selectedSurvey] =
            filteredSurveys.filter((item: any) => item.categories.length) || [];
          if (!selectedSurvey) {
            toastr.warningToast("No Surveys Found");
            // selections["graph"].setFilters(_filters);
            return;
          }
          getQuestionsData(selectedSurvey.id, selectedSurvey.type, "single", {
            ..._filters,
            surveys: filteredSurveys,
            selected: {
              ..._filters.selected,
              surveys:
                selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
              types: selectedSurvey ? [selectedSurvey.type] : [],
              groups: [],
            },
          });
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: "Something went wrong.",
          });
        }
      );
  };
  const getGraphData = async (filters?: any, _selections?: any) => {
    graph.setLoading(true);
    console.warn(filters?.people);
    const payload = {
      ...(filters || {}),
      // people: [...(filters?.people || []), user.empId],
      rater_groups: _selections?.selected?.groups || [],
    };
    return getItemizedGraphDataFact(
      userId,
      _selections.id,
      _selections?.selected?.types[0],
      payload
    ).then(
      (response: any) => {
        selections["single"].setFilters(_selections);

        graph.onSuccess(response);
        return true;
      },
      () => {
        // console.log(err.response);
        graph.setLoading(false);
      }
    );
  };

  const getQuestionsData = (
    surveyId: string,
    type: string,
    src: string,
    _filters?: any
  ) => {
    // try {
    // const data = await
    getAllSectionAndItemsFact(surveyId, type, userId).then((res: any) => {
      if (src === "single") {
        setSingleItems(res.results);
        setActiveSingleQuestion({
          id: res?.results[0]?.id,
          name: res?.results[0]?.label,
        });
        getGraphData(
          {},
          {
            ..._filters,
            id: res?.results[0]?.id,
          }
        );
      } else if (src === "self") {
        setSelfItems(res.results);
      } else if (src === "upward") {
        setUpwardItems(res.results);
      }
    });
  };

  const getSelectedSurveyIndexs = (_selections?: any) => {
    if (_selections) {
      if (_selections.selected.groups && _selections.selected.groups.length) {
        return _selections.selected.groups;
      } else if (
        _selections.selected.surveys &&
        _selections.selected.surveys.length
      ) {
        return _selections.selected.surveys;
      }
    }
    return [];
  };

  const getDynamicColumns = (_meta: any) => {
    return _meta.map((item: any) => {
      return {
        id: item.field,
        key: item.field,
        label: `${item.display_name} Average`,
      };
    });
  };

  const getHeatMapData = (
    type: any,
    isDownload: boolean,
    _selections?: any
  ) => {
    const items = (is: any[]) => {
      if (is.length > 0) {
        return is;
      }
      return undefined;
    };

    getItemizedHeatMapFact(userId, {
      surveys: getSelectedSurveyIndexs(_selections),
      years: _selections?.selected.years || [],
      types: _selections?.selected.types || [],
      questions:
        type === "upward"
          ? items(selectedItems.upward)
          : items(selectedItems.self),
      isDownload,
    }).then(
      (response: any) => {
        selections[type].setFilters(_selections);
        setItems((_items: any) => {
          return {
            ..._items,
            [type]: response.data,
          };
        });
        setColumns({
          ...columns,
          [type]: getDynamicColumns(_selections.meta),
        });
      },
      (err: any) => {
        toastr.onError({ ...err, msg: "Something went wrong." });
      }
    );
  };

  useEffect(() => {
    if (userId) {
      filtersState.getFilters();
      onTableLoad("upward", "upward");
      onTableLoad("self", "self");
      onGraphLoad();
      // getQuestionsData(year, survey, "all");
    }
  }, [user]);

  const singleFilters = useAnalyticsFilters();

  const upwardFilters = useAnalyticsFilters();

  const selfFilters = useAnalyticsFilters();

  const selections: any = {
    upward: upwardFilters,
    self: selfFilters,
    single: singleFilters,
  };

  const getFiltersTemplate = () => {
    return (
      <>
        <Div className="row text-left m-0" style={{ maxWidth: 360 }}>
          <Div className="col p-0" style={{ maxWidth: 100 }}>
            <Text.P2 className="my-2">Choose Item</Text.P2>
          </Div>
          <Div className="col p-0">
            <Button
              variant="outline-primary"
              onClick={() => setShowSingleSelect(true)}
            >
              <Icon icon="far fa-list mr-2" />
              Show all items
            </Button>
          </Div>
        </Div>
        <Div
          className="border rounded-sm mt-2 row mx-0"
          style={{ background: "#FCEED0" }}
        >
          <Div className="pr-0 col" style={{ maxWidth: "calc(100% - 177px)" }}>
            <Div className="d-flex m-0 position-relative">
              <Div
                className="p-0"
                style={{
                  maxWidth: "calc(100% - 30px)",
                  minWidth: "calc(100% - 30px)",
                }}
              >
                <Text.P1
                  className="text-truncate flex-grow-1 fs-14 font-weight-bold text-left"
                  onClick={() => setShowDropdown(!showDropdown)}
                  style={{ marginTop: "0.7rem" }}
                >
                  {activeSingleQuestion.name}
                </Text.P1>
              </Div>
              <Div className="p-0" style={{ maxWidth: 30 }}>
                <Button
                  onClick={() => setShowDropdown(!showDropdown)}
                  variant="link"
                  className="pb-0 px-0 border-0 rounded-0"
                  style={{ marginTop: "0.3rem" }}
                >
                  <Icon icon="far fa-chevron-down" />
                </Button>
              </Div>
              <Dropdown style={{ position: "static" }} show={showDropdown}>
                <Dropdown.Toggle
                  variant="link"
                  className="px-0 py-0 mt-4"
                  id="dropdown-basic"
                  style={{ opacity: 0 }}
                ></Dropdown.Toggle>

                <Dropdown.Menu className="shadow w-100 mt-2 py-0" align="right">
                  {singleItems.map((item: any, i: number) => (
                    <Dropdown.Item
                      key={i}
                      className={`py-2 fs-12 border-bottom d-block`}
                      // href="#/"
                      as={Button}
                      onClick={() => {
                        setActiveSingleQuestion({
                          id: item.id,
                          name: item.label,
                        });
                        const items = selections["single"];
                        getGraphData(
                          {},
                          {
                            ...items,
                            id: item.id,
                          }
                        );
                        setShowDropdown(false);
                      }}
                    >
                      {item.label}
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>
            </Div>
          </Div>
          <Div
            style={{ maxWidth: 177, minWidth: 177 }}
            className="text-right px-0 col"
          >
            <Button
              variant="outline-primary"
              className="font-weight-normal fs-12 rounded-0 border-left border-top-0 border-bottom-0 border-right-0"
              disabled={!Navigation().isPrev}
              style={{ padding: ".7rem 1rem" }}
              onClick={() => Navigation().onPrev()}
            >
              <Icon icon="far fa-chevron-left mr-2" /> Previous
            </Button>
            <Button
              variant="outline-primary"
              className="font-weight-normal fs-12 rounded-0 border-left border-top-0 border-bottom-0 border-right-0"
              disabled={!Navigation().isNext}
              style={{ padding: ".7rem 1rem" }}
              onClick={() => Navigation().onNext()}
            >
              Next <Icon icon="far fa-chevron-right ml-2" />
            </Button>
          </Div>
        </Div>
      </>
    );
  };

  const getRows = (type: string) => {
    const list = items[type] || [];
    return list;
  };

  const getAllValues = (type: string) => {
    const values: any = [];
    items[type].forEach((item: any) => {
      const keys = [
        "revieweeAverage",
        "firmAverage",
        "departmentAverage",
        // "practiceGroupAverage",
        "titleAverage",
        "selfAverage",
      ];
      keys.forEach((key: string) => {
        if (item[key]) {
          values.push(item[key]);
        }
      });
    });
    return values;
  };
  const getHeatMapDataTemplate = (item: any, type: string) => {
    const { Data } = HeatMapTable;
    const values = getAllValues(type);
    return (
      <>
        <Data className="text-dark bg-light ">
          <OverlayTrigger
            key="bottom"
            placement="bottom"
            overlay={
              <Tooltip id="tooltip-bottom" className="fs-10">
                {item.label}
              </Tooltip>
            }
          >
            <Div className="text-left text-truncate" style={{ maxWidth: 300 }}>
              {item.label}
            </Div>
          </OverlayTrigger>
        </Data>
        <Data values={values} value={item.revieweeAverage}>
          {item.revieweeAverage}
        </Data>
        <Data values={values} value={item.firmAverage}>
          {item.firmAverage}
        </Data>
        {columns[type].map((col: any) => {
          return (
            <Data values={values} value={item[col.key]}>
              {item[col.key]}
            </Data>
          );
        })}
        <Data values={values} value={item.selfAverage}>
          {item.selfAverage}
        </Data>
      </>
    );
  };

  function Navigation(): {
    isNext: boolean;
    isPrev: boolean;
    onNext: Function;
    onPrev: Function;
  } {
    const currentIndex = singleItems.findIndex((item: any) => {
      return item.id === activeSingleQuestion.id;
    });

    const isPrev = singleItems.length > 0 && currentIndex > 0;
    const isNext = singleItems.length > currentIndex + 1;
    const items = selections["single"];
    const onNext = () => {
      if (!isNext) return;
      setActiveSingleQuestion({
        id: singleItems[currentIndex + 1].id,
        name: singleItems[currentIndex + 1].label,
      });
      getGraphData(
        {},
        {
          ...items,
          id: singleItems[currentIndex + 1].id,
        }
      );
    };
    const onPrev = () => {
      if (!isPrev) return;
      setActiveSingleQuestion({
        id: singleItems[currentIndex - 1].id,
        name: singleItems[currentIndex - 1].label,
      });
      getGraphData(
        {},
        {
          ...items,
          id: singleItems[currentIndex - 1].id,
        }
      );
    };

    return {
      isNext: isNext,
      isPrev: isPrev,
      onNext,
      onPrev,
    };
  }

  const onTableLoad = (type: string, src: string, year?: any) => {
    selections[type]
      .getFilters({
        year,
        user_id: user.id,
        resource_types:
          type === "self"
            ? TYPES["self"]
            : `${TYPES["upward"]},${TYPES["360"]},${TYPES["360Group"]}`,
      })
      .then(
        (_filters: any) => {
          // selections[type].setFilters(_filters);
          // debugger;
          const [selectedSurvey] = _filters.surveys || [];
          if (!selectedSurvey) {
            // toastr.warningToast(`No ${type} Surveys Found`);
            return;
          }
          let groups = [];
          if (
            [TYPES["360"], TYPES["360Group"]].includes(selectedSurvey?.type)
          ) {
            groups = selectedSurvey["rater_groups"].map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                title: item.title,
                type: item.resource_type,
                groupId: selectedSurvey.id,
              };
            });
          }

          getHeatMapData(type, false, {
            ..._filters,
            surveys: _filters.surveys,
            groups,
            selected: {
              ..._filters.selected,
              surveys:
                selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
              types: [selectedSurvey?.type],
              groups: groups.length ? [groups[0].groupId] : [],
            },
          });
          getQuestionsData(selectedSurvey?.id, selectedSurvey?.type, src);
        },
        (err: any) => {
          toastr.onError({
            ...err,
            msg: "Something went wrong.",
          });
        }
      );
  };

  const getColumnsData = (type: any) => {
    const output = [
      { id: 1, label: "Item" },
      { id: 2, label: "Reviewee Average" },
      { id: 4, label: "Firm Average" },
      ...columns[type],
      // { id: 3, label: "Department Cateogry Average" },
      // { id: 5, label: "Practice group Category Average" },
      // { id: 6, label: "Title Category Average" },
      { id: 7, label: "Self Average" },
    ];
    return output;
  };

  // const onFilterSelect = (data: any) => {
  //   const items = selections["single"];

  //   getGraphData(
  //     {
  //       ...data,
  //       people: data.people
  //         ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //         : [],
  //     },
  //     {
  //       ...items,
  //       id: activeSingleQuestion.id,
  //     }
  //     // selections["single"]
  //   );
  // };

  const onFilterSelectNew = (data: any, groupID?: any) => {
    const computedData = Object.entries(data).reduce((acc, [k, v]) => {
      if ((v as any).length) {
        (acc as any)[k] = v;
      }
      return acc;
    }, {});
    setSelectedTimeGraphCompareList((s: any) => {
      let newList = { ...s, [groupID]: computedData };
      newList = Object.entries(newList).reduce((acc, [k, v]) => {
        if (Object.keys(v as any).length) {
          (acc as any)[k] = v;
        }
        return acc;
      }, {});
      return newList;
    });
  };

  return (
    <Layout.Container fluid className="pt-3">
      {showSingleSelect && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          // hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <SideSingleSelect
            onClose={() => setShowSingleSelect(false)}
            sections={singleItems}
            selected={activeSingleQuestion.id}
            setSelected={(item: any) => {
              setActiveSingleQuestion(item);
              const items = selections["single"];
              getGraphData(
                {},
                {
                  ...items,
                  id: item.id,
                }
              );
            }}
            filteredComponent={() => (
              <AnalyticsFilters
                filters={selections["single"]}
                hideTypes
                config={{
                  surveys: {
                    multiple: false,
                  },
                  groups: {
                    multiple: false,
                  },
                  layout: {
                    year: {
                      sm: 5,
                    },
                    survey: {
                      sm: 7,
                    },
                    group: {
                      sm: 12,
                      className: "mt-3",
                    },
                  },
                }}
                onYearChange={(item: any) => {
                  // onTableLoad("single", "single", );
                  onGraphLoad(item.key);
                }}
                onSurveyChange={(items: any) => {
                  const item = selections["single"];
                  const [surveyId] = items;
                  const selected = item.surveys.find((s: any) => {
                    return items.includes(s.id);
                  });
                  const groups = selected["rater_groups"]?.map((item: any) => {
                    // debugger;
                    return {
                      id: item.id,
                      key: item.key,
                      title: item.title,
                      type: item.resource_type,
                      groupId: surveyId,
                      categories: item.categories || [],
                    };
                  });
                  getQuestionsData(selected.id, selected.type, "single", {
                    ...item,
                    groups,
                    selected: {
                      ...item.selected,
                      surveys: items,
                      groups: [],
                      types: [selected.type],
                    },
                  });
                }}
                onGroupChange={(items: any) => {
                  const item = selections["single"];
                  // const filteredGroups =
                  //   item.groups.filter((item: any) =>
                  //     items.includes(item.id)
                  //   ) || [];
                  getQuestionsData(
                    item.selected.surveys[0],
                    item.selected.types[0],
                    "single",
                    {
                      ...item,
                      // groups,
                      selected: {
                        ...item.selected,
                        groups: items,
                        types: [TYPES["360Group"]],
                      },
                    }
                  );
                }}
              />
            )}
          />
        </Layout.Sidebar>
      )}
      {showMultiSelect.upward && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          // hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <SideMultiSelect
            onClose={() => {
              const item = selections["upward"];
              setShowMultiSelect({
                upward: false,
                self: false,
              });
              getHeatMapData("upward", false, {
                ...item,
                selected: {
                  ...item.selected,
                },
              });
            }}
            sections={upwardItems}
            selected={selectedItems.upward}
            setSelected={setSelelectedItems}
            type="upward"
          />
        </Layout.Sidebar>
      )}
      {showMultiSelect.self && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          // hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <SideMultiSelect
            onClose={() => {
              const item = selections["self"];
              setShowMultiSelect({
                upward: false,
                self: false,
              });
              getHeatMapData("self", false, {
                ...item,
                selected: {
                  ...item.selected,
                },
              });
            }}
            sections={selfItems}
            selected={selectedItems.self}
            setSelected={setSelelectedItems}
            type="self"
          />
        </Layout.Sidebar>
      )}
      {showItems && (
        <Layout.Sidebar
          className="bg-light border-right"
          right
          hasHeader
          style={{ zIndex: 1050 }}
          width={400}
        >
          <ItemList onClose={() => toggleItems(false)} />
        </Layout.Sidebar>
      )}
      {/* <AnalyticsFilters
        filters={selections["single"]}
        hideTypes
        config={{
          surveys: {
            multiple: false,
          },
          groups: {
            multiple: false,
          },
          layout: {
            year: {
              sm: 5,
            },
            survey: {
              sm: 7,
            },
            group: {
              sm: 12,
              className: "mt-3",
            },
          },
        }}
        onYearChange={(item: any) => {
          console.log(item);
          onGraphLoad(item.key)
          // onTableLoad("single", "single", item.key);
        }}
        onSurveyChange={(items: any) => {
          const item = selections["single"];
          const [surveyId] = items;
          const selected = item.surveys.find((s: any) => {
            return items.includes(s.id);
          });
          const groups = selected["rater_groups"]?.map((item: any) => {
            return {
              id: item.id,
              key: item.key,
              title: item.title,
              type: item.resource_type,
              groupId: surveyId,
            };
          });
          // getHeatMapData("single", {
          //   ...item,
          //   groups,
          //   selected: {
          //     ...item.selected,
          //     surveys: items,
          //     groups: [],
          //     types: [selected.type],
          //   },
          // });
          getQuestionsData(selected.id, selected.type, "single");
        }}
        onGroupChange={(items: any) => {
          const item = selections["single"];

          getHeatMapData("single", {
            ...item,
            // groups,
            selected: {
              ...item.selected,
              // surveys: items,
              groups: items,
              types: [TYPES["360"]],
            },
          });
        }}
      /> */}
      <Div className="py-2 text-right">
        <Div className="py-3">{getFiltersTemplate()}</Div>
      </Div>

      <Div>
        {/* <Text.H3>Time Series Graph</Text.H3> */}
        {/* <Layout.Row className="pt-4 mx-0">
          <Layout.Col xl={9} className="border p-4">
            <CompareChart data={GRAPH_DATA.data} labels={GRAPH_DATA.labels} />
            <LegendFor legends={[]} />
          </Layout.Col>
          <Layout.Col>
            <Legend />
          </Layout.Col>
        </Layout.Row> */}
        {/* <TimeSeriesGraph
          filtersState={filtersState}
          onFilterSelect={onFilterSelect}
          userName={user.name}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
        /> */}
        <TimeSeriesGraphNew
          onFilterSelect={onFilterSelectNew}
          userName={user.name}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
        />
      </Div>
      {getRows("upward")?.length > 0 && (
        <>
          <Div>
            <DownloadExcel
              hideFilters
              title="Heat Map - Upward/360 Degree Review"
              onDownload={() => {
                // debugger;
                const item = selections["upward"];
                getHeatMapData("upward", true, {
                  ...item,
                  selected: {
                    ...item.selected,
                  },
                });
              }}
            />
          </Div>

          <Div className="row mb-3">
            <Div className="col-md-8 col-12">
              <AnalyticsFilters
                filters={selections["upward"]}
                hideTypes
                config={{
                  surveys: {
                    multiple: false,
                  },
                  groups: {
                    multiple: false,
                  },
                  layout: {
                    year: {
                      xl: 3,
                      sm: 4,
                    },
                    survey: {
                      xl: 3,
                      sm: 4,
                    },
                    group: {
                      xl: 4,
                      sm: 4,
                    },
                  },
                }}
                onYearChange={(item: any) => {
                  onTableLoad("upward", "upward", item.key);
                }}
                onSurveyChange={(items: any) => {
                  const item = selections["upward"];

                  const [surveyId] = items;
                  const selected = item.surveys.find((s: any) => {
                    return items.includes(s.id);
                  });
                  const groups = selected["rater_groups"]?.map((item: any) => {
                    return {
                      id: item.id,
                      key: item.key,
                      title: item.title,
                      type: item.resource_type,
                      groupId: surveyId,
                    };
                  });
                  getHeatMapData("upward", false, {
                    ...item,
                    groups,
                    selected: {
                      ...item.selected,
                      surveys: items,
                      groups: [],
                      types: [selected.type],
                    },
                  });
                  getQuestionsData(selected.id, selected.type, "upward");
                }}
                onGroupChange={(items: any) => {
                  const item = selections["upward"];
                  console.log(item);
                  getHeatMapData("upward", false, {
                    ...item,
                    // groups,
                    selected: {
                      ...item.selected,
                      // surveys: item.selected.surveys,
                      groups: items,
                      types: [TYPES["360"]],
                    },
                  });
                }}
              />
            </Div>
            <Div className="col text-right">
              <Button
                variant="outline-primary"
                onClick={() =>
                  setShowMultiSelect({ upward: true, self: false })
                }
              >
                <Icon icon="far fa-list mr-2" />
                Show all items
              </Button>
            </Div>
          </Div>
          <Div>
            <HeatMapTable
              columns={getColumnsData("upward")}
              values={getAllValues("upward")}
              rows={getRows("upward")}
              rowItem={(item: any) => getHeatMapDataTemplate(item, "upward")}
            />
          </Div>
        </>
      )}

      {getRows("self")?.length > 0 && (
        <>
          <Div>
            <DownloadExcel
              hideFilters
              title="Heat Map - Self Assessment"
              onDownload={() => {
                const item = selections["self"];
                getHeatMapData("self", true, {
                  ...item,
                  selected: {
                    ...item.selected,
                  },
                });
              }}
            />
          </Div>

          <Div className="row mb-3">
            <Div className="col-md-8 col-12">
              <AnalyticsFilters
                filters={selections["self"]}
                hideTypes
                config={{
                  surveys: {
                    multiple: false,
                  },
                  layout: {
                    year: {
                      xl: 3,
                      sm: 4,
                    },
                    survey: {
                      xl: 3,
                      sm: 4,
                    },
                  },
                }}
                onYearChange={(item: any) => {
                  onTableLoad("self", "self", item.key);
                }}
                onSurveyChange={(items: any) => {
                  const item = selections["self"];
                  getHeatMapData("self", false, {
                    ...item,
                    selected: {
                      ...item.selected,
                      surveys: items,
                    },
                  });
                }}
              />
            </Div>
            <Div className="col text-right">
              <Button
                variant="outline-primary"
                onClick={() =>
                  setShowMultiSelect({ self: true, upward: false })
                }
              >
                <Icon icon="far fa-list mr-2" />
                Show all items
              </Button>
            </Div>
          </Div>
          <Div>
            <HeatMapTable
              columns={getColumnsData("self")}
              values={getAllValues("self")}
              rows={getRows("self")}
              rowItem={(item: any) => getHeatMapDataTemplate(item, "self")}
            />
          </Div>
        </>
      )}
    </Layout.Container>
  );
};

Itemized.propTypes = {};

export default Itemized;
