import React, { useState, useEffect } from "react";
import { Div, CustomModal as Modal } from "unmatched/components";
import RequiredFields from "./RequiredFields";
import UploadFile from "./UploadFile";
import { getRequiredParticipantFieldFact } from "../participants-api";
// import ConfirmRules from "./ConfirmRules";
import SuccessModal from "./SuccessModal";
import { AddUserManually } from "./manual/AddUsersManually";

// import { AddPairingManually } from "./manual/AddPairingManually";
// import ConfirmExplicitEmailInvite from "./ConfirmExplicitEmailInvite";

export default function AddParticipants(props: any) {
  // let screen: number = props.screen;
  const [isLoading, setIsLoading] = useState(true);
  const [requiredFields, setRequiredFields] = useState([]);
  const [apiResponse, setApiResponse] = useState<null | any>(null);
  useEffect(() => {
    async function getRequiredFields() {
      const response = await getRequiredParticipantFieldFact();
      const data = await response.data.fields;
      setRequiredFields(data);
      setIsLoading(false);
    }
    getRequiredFields();
  }, []);

  const [screen, setScreen] = useState<number>(1);
  const ScreenView = (inPosition: number, props: any) => {
    switch (inPosition) {
      case 1:
        return (
          <RequiredFields
            {...props}
            setScreen={setScreen}
            loading={isLoading}
            data={requiredFields}
          />
        );
      case 2:
        return (
          <UploadFile
            {...props}
            setApiResponse={setApiResponse}
            setScreen={setScreen}
          />
        );
      case 3:
        return (
          <SuccessModal
            {...props}
            apiResponse={apiResponse}
            setApiResponse={setApiResponse}
            setScreen={setScreen}
          />
        );
      // case 4:
      //   return <ConfirmRules {...props} setScreen={setScreen} />;
      case 5:
        return (
          <AddUserManually
            {...props}
            surveyIndex={props.survey?.data?.id}
            setScreen={setScreen}
            // getPairings={getPairings}
          />
          // <AddPairingManually
          //   {...props}
          //   surveyIndex={props.survey?.data?.id}
          //   setScreen={setScreen}
          //   // getPairings={getPairings}
          // />
        );
      // case 6:
      //   return (
      //     <ConfirmExplicitEmailInvite
      //       {...props}
      //       surveyIndex={props.survey?.data?.id}
      //       setScreen={setScreen}
      //     />
      //   );
      default:
        return "";
    }
  };
  return (
    <Modal
      {...props}
      onHide={() => {
        setScreen(1);
        props.onHide();
        setApiResponse(null);
      }}
      children={
        <>
          <Modal.Header closeButton>
            <Modal.Title>Add Participants</Modal.Title>
          </Modal.Header>
          <Div>{ScreenView(screen, props)}</Div>
        </>
      }
    />
  );
}
