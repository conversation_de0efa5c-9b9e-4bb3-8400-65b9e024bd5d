import { DateTime } from "luxon";
import { QUESTION } from "./survey-enums";
import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";
import api from "unmatched/utils/api";
import API_URLS from "unmatched/utils/urls/api-urls";
import { getStats } from "pages/UserApp/Dashboard/SurveyList/surveys-api";

const getQuestionModel = (item: any) => {
  return {
    id: item.id,
    sectionId: item.section,
    question: item.label || item.text,
    type: item.resourcetype,
    mandatory: item.mandatory,
    textFeedback: item.collect_feedback,
    hasSwap: item.is_reverse_scale,
    name: item.name,
    radio: {
      options: item.options || [],
    },
    checkbox: {
      options: item.options || [],
    },
    rating: {
      start: item.range_start || 0,
      end: item.range_end || 0,
      tags: item.options || {},
    },
  };
};

const getSurveyIndexModel = (data: any) => {
  const serializeTimezone = (dt: string) => {
    const dateTime = dt.split("T");
    const date = dateTime[0].split("-").map((i) => parseInt(i));
    const time = dateTime[1].split(":").map((i) => parseInt(i));
    const _DT = new Date();
    _DT.setFullYear(date[0], date[1] - 1, date[2]);
    _DT.setHours(time[0]);
    _DT.setMinutes(time[1]);
    _DT.setSeconds(0);
    return _DT;
  };
  return {
    id: data.id,
    name: data.title || "",
    type: data.resourcetype,
    description: data.description || "",
    auxIndex: data.aux_survey_index,
    auxTitle: data.aux_survey_index_title,
    startDate: data.start ? serializeTimezone(data.start) : "",
    endDate: data.deadline ? serializeTimezone(data.deadline) : "",
    // pairings: data.pairing_files && data.pairing_files.map((item: any) => item.id),
    pairings: data.pairing_file ? [data.pairing_file] : [],
    pairingRules: data.pairing_rules,
    enablePairings: data.is_pairs_editable,
    enableSubmittedStatus: data.show_submitted_status,
    enableVersions: data.is_multiple_survey_versions,
    enableMinResponses: false,
    enableCustomWaviewer: false,
    enableRatingsCommentsWaveOff: false,
    enableRatingsWaveOff: false,
    builderStatus: data.save_status,
    updatedTime: util.date.getFormatedTime(new Date(), " MMMM dd, yyyy, HH:mm"),
    updatedAt: util.date.getFormatedTime(
      new Date(data.updated_at),
      " MMMM dd, yyyy, hh:mm a"
    ),
    users: data.users_file ? data.users_file : [],
    assesmentStart: data.assessment_period_start_date
      ? new Date(data.assessment_period_start_date)
      : "",
    assesmentEnd: data.assessment_period_end_date
      ? new Date(data.assessment_period_end_date)
      : "",
    hasDemographics: data.is_demographic_enabled,
    demographicsId: data.demographic_id || 0,
    isDraft: data.is_draft,
    isLocked: data.is_locked,
    upwardTitle: data.upward_title || null,
    thankText: data.thank_text,
    versions: data.surveys,
    waiverStatus: data.waiver_status,
    availableTimeZones: data.available_timezones,
    timezone: data.timezone,
  };
};

// const getQuestionApiModel = () => {

// };

export const onValidateSurveyFact = (id: any, meta?: any) => {
  // https://v2.unmatched.app/api/v2/survey/admin/index/{id}/verify
  const config = api.getConfigurations({}, meta);
  return axios
    .post(`/survey/admin/index/${id}/verify`, null, config)
    .then(({ data }: AxiosResponse) => {
      return {
        isSuccess: data.success,
        participants: {
          isValid: data.participants.is_valid,
          reason: data.participants.reason,
        },
        questions: {
          isValid: data.questionnaire.is_valid,
          reason: data.questionnaire.reason,
        },
        properties: {
          isValid: data.properties.is_valid,
          reason: data.properties.reason,
        },
        email: {
          isValid: data.email_template.is_valid,
          reason: data.email_template.reason,
        },
      };
    });
};

export const getAllUserFilesFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.GET_ALL_USER_FILES}`, config)
    .then((response: AxiosResponse) => {
      const { results, count_pages, count_items } = response?.data || {};
      return {
        data: results.map((data: any) => {
          return {
            id: data.id,
            title: data.title,
            uploadedOn: data.uploaded_on,
            records: data.records,
            tags: util.getCommaSeperatedFromArray(data.tags),
          };
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const getStatsDataParallel = async (surveys: any) => {
  const statsMap = await Promise.all(
    surveys.map(async (s: any) => {
      const res2 = await Promise.all(
        ["general", "admin", "report"].map((el: any) =>
          getStats("index", s.id, el)
        )
      );
      const res3 = await Promise.all(
        s.surveys.map(async (el: any) => ({
          [el.id]: await Promise.all(
            ["general", "admin", "report"].map((el2: any) =>
              getStats("version", el.id, el2)
            )
          ),
        }))
      );

      return {
        [s.id]: res2,
        versionStats: res3,
      };
    })
  );

  const og = surveys.map((item: any) => {
    const { surveys } = item || {};
    let [stats, admin_stats, report_stats] = statsMap.find((el) =>
      Object.keys(el).includes(item.id)
    )?.[item.id]; // deepscan-disable-line
    // const { admin_stats, report_stats, stats } = ;

    stats = stats.data;
    admin_stats = admin_stats.data;
    report_stats = report_stats.data;
    const {
      active_user: active,
      inactive_user: in_active,
      participated_percentage,
    } = admin_stats || {};
    const versions = surveys.map((ver: any) => {
      const versionStats = ver.admin_stats || {};
      return {
        id: ver.id,
        endDate: item.deadline,
        startDate: item.start,
        assesmentStart: item.assessment_period_start_date
          ? item.assessment_period_start_date + "T00:00:00Z"
          : "",
        assesmentEnd: item.assessment_period_end_date
          ? item.assessment_period_end_date + "T00:00:00Z"
          : "",
        type: item.resourcetype,
        title: ver.name || "-",
        enablePairings: item.is_pairs_editable,
        activeUsers: versionStats.active_user,
        inactiveUsers: versionStats.inactive_user,
        completed: versionStats.participated_percentage,
        reportStats: {
          totalEligible: report_stats?.total_eligible,
          totalReports: report_stats?.total_reports,
        },
        surveyVersionID: ver.id,
        stats,
      };
    });
    return {
      id: item.id,
      endDate: item.deadline,
      startDate: item.start,
      assesmentStart: item.assessment_period_start_date
        ? item.assessment_period_start_date + "T00:00:00Z"
        : "",
      assesmentEnd: item.assessment_period_end_date
        ? item.assessment_period_end_date + "T00:00:00Z"
        : "",
      type: item.resourcetype,
      title: item.title || "-",
      enablePairings: item.is_pairs_editable,
      enableSubmittedStatus: item.show_submitted_status,
      activeUsers: active,
      inactiveUsers: in_active,
      completed: participated_percentage,
      versions,
      reportStats: {
        totalEligible: report_stats?.total_eligible,
        totalReports: report_stats?.total_reports,
      },
      version: surveys[0]?.id,
      admin_stats: admin_stats,
      groupName: item.group_details?.group_name,
      stats,
    };
  });
  return og;
};

export const getSurveysFactV2 = (params: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: params.size,
      search: params.search,
      ordering: params.sort,
      resource_type: params.resource_type,
      include_historic: params.include_historic,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS_V2}${params.filter}/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      // getStatsData(results);
      const payload = {
        data: results.map((item: any) => {
          const { admin_stats, surveys, report_stats, stats } = item || {};
          const { active, in_active, participated_percentage } =
            admin_stats || {};
          const versions = surveys.map((ver: any) => {
            const versionStats = ver.admin_stats || {};
            return {
              id: ver.id,
              endDate: item.deadline,
              startDate: item.start,
              assesmentStart: item.assessment_period_start_date
                ? item.assessment_period_start_date + "T00:00:00Z"
                : "",
              assesmentEnd: item.assessment_period_end_date
                ? item.assessment_period_end_date + "T00:00:00Z"
                : "",
              type: item.resourcetype,
              title: ver.name || "-",
              enablePairings: item.is_pairs_editable,
              activeUsers: versionStats.active_user,
              inactiveUsers: versionStats.inactive_user,
              completed: versionStats.participated_percentage,
              reportStats: {
                totalEligible: report_stats?.total_eligible,
                totalReports: report_stats?.total_reports,
              },
              surveyVersionID: ver.id,
              stats,
            };
          });
          return {
            id: item.id,
            endDate: item.deadline,
            startDate: item.start,
            assesmentStart: item.assessment_period_start_date
              ? item.assessment_period_start_date + "T00:00:00Z"
              : "",
            assesmentEnd: item.assessment_period_end_date
              ? item.assessment_period_end_date + "T00:00:00Z"
              : "",
            type: item.resourcetype,
            title: item.title || "-",
            enablePairings: item.is_pairs_editable,

            enableSubmittedStatus: item.show_submitted_status,
            activeUsers: active,
            inactiveUsers: in_active,
            completed: participated_percentage,
            versions,
            reportStats: {
              totalEligible: report_stats?.total_eligible,
              totalReports: report_stats?.total_reports,
            },
            version: surveys[0]?.id,
            admin_stats: item.admin_stats,
            groupName: item.group_details?.group_name,
            stats,
          };
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
      return { ...payload, rawResults: results };
    });
};

export const getSurveysFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: params.size,
      search: params.search,
      ordering: params.sort,
      resource_type: params.resource_type,
      include_historic: params.include_historic,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS}${params.filter}/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      const payload = {
        data: results.map((item: any) => {
          const { admin_stats, surveys, report_stats, stats } = item || {};
          const { active, in_active, participated_percentage } =
            admin_stats || {};
          const versions = surveys.map((ver: any) => {
            const versionStats = ver.admin_stats || {};
            return {
              id: ver.id,
              endDate: item.deadline,
              startDate: item.start,
              assesmentStart: item.assessment_period_start_date
                ? item.assessment_period_start_date + "T00:00:00Z"
                : "",
              assesmentEnd: item.assessment_period_end_date
                ? item.assessment_period_end_date + "T00:00:00Z"
                : "",
              type: item.resourcetype,
              title: ver.name || "-",
              enablePairings: item.is_pairs_editable,
              activeUsers: versionStats.active_user || 0,
              inactiveUsers: versionStats.inactive_user || 0,
              completed: versionStats.participated_percentage || 0,
              reportStats: {
                totalEligible: report_stats?.total_eligible || 0,
                totalReports: report_stats?.total_reports || 0,
              },
              surveyVersionID: ver.id,
              stats,
              admin_stats: ver.admin_stats,
            };
          });
          return {
            id: item.id,
            endDate: item.deadline,
            startDate: item.start,
            assesmentStart: item.assessment_period_start_date
              ? item.assessment_period_start_date + "T00:00:00Z"
              : "",
            assesmentEnd: item.assessment_period_end_date
              ? item.assessment_period_end_date + "T00:00:00Z"
              : "",
            type: item.resourcetype,
            title: item.title || "-",
            enablePairings: item.is_pairs_editable,
            enableSubmittedStatus: item.show_submitted_status,
            activeUsers: active || 0,
            inactiveUsers: in_active || 0,
            completed: participated_percentage || 0,
            versions,
            reportStats: {
              totalEligible: report_stats?.total_eligible || 0,
              totalReports: report_stats?.total_reports || 0,
            },
            version: surveys[0]?.id,
            admin_stats: item.admin_stats,
            groupName: item.group_details?.group_name,
            stats,
          };
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
      return payload;
    });
};

export const get360SurveysFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: params.size,
      search: params.search,
      ordering: params.sort,
      resource_type: params.resource_type,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_360_SURVEYS}${params.filter}/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      const payload = {
        data: results.map((group: any) => {
          const { child_survey_indexes, report_stats } = group || {}; // deepscan-disable-line
          const versions = child_survey_indexes.map((item: any) => {
            const { admin_stats, report_stats } = item || {}; // deepscan-disable-line
            return {
              id: item.id,
              endDate: item.deadline,
              startDate: item.start,
              type: item.resourcetype,
              title: item.title || "-",
              enablePairings: item.is_pairs_editable,
              activeUsers: admin_stats.inactive_user || 0,
              inactiveUsers: admin_stats.active_user || 0,
              completed: admin_stats.participated_percentage || 0,
              versions,
              reportStats: {
                totalEligible: report_stats?.total_eligible || 0,
                totalReports: report_stats?.total_reports || 0,
              },
              adminStats: admin_stats,
            };
          });
          return {
            id: group.id,
            endDate: group.end_date,
            startDate: group.start,
            type: group.resourcetype,
            title: group.name || "-",
            enablePairings: group.is_pairs_editable,
            enableSubmittedStatus: group?.show_submitted_status,
            activeUsers: 0,
            inactiveUsers: 0,
            completed: 0,
            versions,
            reportStats: {
              totalEligible: report_stats?.total_eligible || 0,
              totalReports: report_stats?.total_reports || 0,
            },
            version: child_survey_indexes[0]?.id,
            admin_stats: group.admin_stats,
            groupName: group.group_details?.group_name,
          };
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
      // console.log(payload);
      return payload;
    });
};

export const getDraftSurveysFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: params.size,
      search: params.search,
      ordering: params.ordering,
      resource_type: params.resource_type,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS}${params.filter}/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((item: any) => ({
          id: item.id,
          endDate: item.deadline,
          startDate: item.start,
          type: item.resourcetype,
          title: item.title,
          enablePairings: item.is_pairs_editable,
          enableSubmittedStatus: item?.show_submitted_status,
          updatedAt: item.updated_at,
        })),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const getDraftSurveysFactV2 = (params: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      page: params.page,
      page_size: params.size,
      search: params.search,
      ordering: params.ordering,
      resource_type: params.resource_type,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS_V2}${params.filter}/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map((item: any) => ({
          id: item.id,
          endDate: item.deadline,
          startDate: item.start,
          type: item.resourcetype,
          title: item.title,
          enablePairings: item.is_pairs_editable,
          enableSubmittedStatus: item?.show_submitted_status,
          updatedAt: item.updated_at,
        })),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const getSurveyNamesFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS}all/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.map(({ id, title }: any) => ({
          id,
          title,
        })),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};
export const getDraftSurveyNamesFact = (params: any, meta?: any) => {
  const config = api.getConfigurations(params, meta);
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS}DRAFT/`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return { data: [], totalPages: 0 };
      const { results, count_pages, count_items } = data;
      if (!results) return { data: [], totalPages: 0 };
      return {
        data: results.filter(({ id, title, aux_survey_index }: any) => {
          if (aux_survey_index === null)
            return {
              id,
              title,
            };
          else return;
        }),
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const getSurveyByIdFactV2 = (id: number | string, meta?: any) => {
  // const params = {
  //   type,
  // };
  const config = api.getConfigurations({}, meta);
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS_V2}${id}`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return {};
      debugger
      return getSurveyIndexModel(data);
    });
};

export const getSurveyByIdFact = (id: number | string, meta?: any) => {
  // const params = {
  //   type,
  // };
  const config = api.getConfigurations({}, meta);
  return axios
    .get(`${API_URLS.ADMIN_SURVEYS}${id}`, config)
    .then(({ data }: AxiosResponse) => {
      if (!data) return {};
      return getSurveyIndexModel(data);
    });
};

export const assosiateParticipantFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.post(
    "/staff/files/associate-pairings/",
    {
      file_id: data.fileId,
      index_id: data.indexId,
    },
    config
  );
};

export const removeParticipantFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.post(
    "/staff/files/remove-associated-pairings/",
    {
      file_id: data.fileId,
      index_id: data.indexId,
    },
    config
  );
};

export const cloneSurveyFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.post(`/survey/admin/index/${data.id}/clone`, {}, config);
};

export const saveSurveyFact = (type: string, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  const surveyTypes: any = {
    SurveyIndexSelf: "New Self Evaluation Survey",
    SurveyIndexPair: "New Survey",
    SurveyIndexEngagement: "New Engagement Survey",
    SurveyIndex360: "New 360 Degree Feedback Survey",
  };
  return axios.post(
    `${API_URLS.ADMIN_SURVEYS}`,
    {
      title: surveyTypes[type] ?? "New Survey",
      is_draft: true,
      resourcetype: type,
      deadline: util.date.getDateAfter(30),
    },
    config
  );
};

export const linkSelfSurveyFact = (
  data: { selfID: string; id: string },
  meta?: any
) => {
  const config = api.getConfigurations({}, meta);

  return axios.post(
    `${API_URLS.ADMIN_SELF_CLONE}`,
    {
      self_id: data.selfID,
      upward_id: data.id,
    },
    config
  );
};

export const patchSurveyFact = (id: string, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  const formatedTimezoneDateTime = (date: Date) => {
    const zone = DateTime.local({ zone: data.timezone }).toFormat("ZZ");
    // const dt = new Date();

    console.log(
      `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${(
        "0" + date.getDate()
      ).slice(-2)}T${("0" + date.getHours()).slice(-2)}:${(
        "0" + date.getMinutes()
      ).slice(-2)}:00${zone}`
    );
    return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${(
      "0" + date.getDate()
    ).slice(-2)}T${("0" + date.getHours()).slice(-2)}:${(
      "0" + date.getMinutes()
    ).slice(-2)}:00${zone}`;
  };
  // debugger
  return axios
    .patch(
      `${API_URLS.ADMIN_SURVEYS}${id}/`,
      {
        title: data.name,
        resourcetype: data.type,
        start: formatedTimezoneDateTime(data?.startDate),
        deadline: formatedTimezoneDateTime(data?.endDate),
        assessment_period_start_date: data?.assesmentStart
          ? util.date.getDate(new Date(data?.assesmentStart))
          : null,
        assessment_period_end_date: data?.assesmentEnd
          ? util.date.getDate(new Date(data?.assesmentEnd))
          : null,
        is_pairs_editable: data?.enablePairings,
        show_submitted_status: data?.enableSubmittedStatus,
        is_multiple_survey_versions: data?.enableVersions,
        description: data?.description,
        is_demographic_enabled: data?.hasDemographics,
        thank_text: data?.thankText,
        waiver_status: data?.waiverStatus,
        timezone: data?.timezone,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return data ? getSurveyIndexModel(data) : {};
    });
};

export const submitSurveyFact = (id: number, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.patch(
    `${API_URLS.ADMIN_SURVEYS}${id}/`,
    {
      resourcetype: data.type,
      is_draft: data.isDraft,
    },
    config
  );
};

export const patchSurveyBuilderStatusFact = (
  data: any,
  status: string,
  meta?: any
) => {
  const config = api.getConfigurations({}, meta);
  return axios.patch(
    `${API_URLS.ADMIN_SURVEYS}${data.id}/`,
    {
      resourcetype: data.type,
      save_status: status,
    },
    config
  );
};

export const updateSurveyFact = (id: number, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.put(
    `${API_URLS.ADMIN_SURVEYS}${id}/`,
    {
      title: data.name,
      resourcetype: data.type,
      start: new Date(data.startDate),
      deadline: new Date(data.endDate),
      is_pairs_editable: data.enablePairings,
      show_submitted_status: data.enableSubmittedStatus,
      description: data.description,
    },
    config
  );
};

export const removeSurveyFact = (id: number, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.delete(`${API_URLS.ADMIN_SURVEYS}${id}`, config);
};

export const getSurveyVersionsFact = (index: number, meta?: any) => {
  const config = api.getConfigurations(
    {
      index,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEY_VERSIONS}`, config)
    .then(({ data }: AxiosResponse) => {
      return data.results.map((item: any) => {
        return {
          id: item.id,
          label: item.name,
          minResponses: item.min_response_count || 0,
          eligibilityId: item.report_eligibility_settings,
        };
      });
    });
};

export const createSurveyVersionFact = (index: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios
    .post(
      `${API_URLS.ADMIN_SURVEY_VERSIONS}`,
      {
        id: 0,
        index,
        name: "Version",
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return {
        id: data.id,
        label: data.name,
        minResponses: data.min_response_count || 0,
        eligibilityId: data.report_eligibility_settings,
      };
    });
};

export const cloneSurveyVersionFact = (index: any) => {
  // const config = api.getConfigurations({
  // }, meta);
  return axios
    .post(`${API_URLS.ADMIN_SURVEY_VERSIONS}${index}/clone/`)
    .then(({ data }: AxiosResponse) => {
      return {
        id: data.id,
        label: data.name,
        minResponses: data.min_response_count || 0,
      };
    });
};

export const patchSurveyVersionFact = (id: any, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.patch(
    `${API_URLS.ADMIN_SURVEY_VERSIONS}${id}`,
    {
      id: data.id,
      name: data.label,
      min_response_count: data.minResponses,
    },
    config
  );
};

export const patchMinResCountFact = (id: any, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.patch(
    `${API_URLS.ADMIN_SURVEY_VERSION_MIN_RES_COUNT}${id}/`,
    {
      id: data.id,
      name: data.label,
      min_response_count: data.minResponses,
    },
    config
  );
};

export const deleteSurveyVersionFact = (id: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.delete(`${API_URLS.ADMIN_SURVEY_VERSIONS}${id}`, config);
};

export const getSurveySectionsFact = (id: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      survey: id,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEY_SECTIONS}`, config)
    .then(({ data }) => {
      return data.results.map((item: any) => {
        return item;
      });
    });
};

export const destroySectionFact = (id: any) => {
  return axios.delete(`/survey/admin/section/${id}`);
};

export const cloneSectionFact = (id: any) => {
  return axios
    .post(`/survey/admin/section/${id}/clone/`)
    .then(({ data }: AxiosResponse) => {
      return {
        section: {
          ...data,
          components: undefined,
        },
        questions: data.components.map(getQuestionModel),
      };
    });
};

// https://v2.unmatched.app/api/v2/survey/admin/section/{id}/clone/

export const createSurveySectionFact = (id: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios
    .post(
      `${API_URLS.ADMIN_SURVEY_SECTIONS}`,
      {
        id,
        name: "Section",
        label: " ",
        survey: id,
      },
      config
    )
    .then(({ data }: AxiosResponse) => data);
};

export const patchSurveySectionFact = (id: any, name: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.patch(
    `${API_URLS.ADMIN_SURVEY_SECTIONS}${id}`,
    {
      id,
      name,
    },
    config
  );
};

export const getSurveyQuestionsFact = (id: any, meta?: any) => {
  const config = api.getConfigurations(
    {
      section__survey: id,
    },
    meta
  );
  return axios
    .get(`${API_URLS.ADMIN_SURVEY_QUESTIONS}`, config)
    .then(({ data }) => {
      return data.results.map(getQuestionModel);
    });
};

export const createSurveyQuestionFact = (id: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios
    .post(
      `${API_URLS.ADMIN_SURVEY_QUESTIONS}`,
      {
        order: 0,
        name: "Question",
        label: "Question",
        section: id,
        resourcetype: QUESTION.CHECKBOX,
      },
      config
    )
    .then(({ data }: AxiosResponse) => getQuestionModel(data));
};

export const createSpecificSurveyQuestionFact = (id: any, meta?: any, type?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios
    .post(
      `${API_URLS.ADMIN_SURVEY_QUESTIONS}`,
      {
        order: 0,
        name: "Question",
        label: "Question",
        section: id,
        resourcetype: type || QUESTION.CHECKBOX,
      },
      config
    )
    .then(({ data }: AxiosResponse) => getQuestionModel(data));
};

export const createSurveyInstructionFact = (id: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios
    .post(
      `${API_URLS.ADMIN_SURVEY_QUESTIONS}`,
      {
        order: 0,
        name: "Instruction",
        label: "Instruction",
        text: "Instruction",
        section: id,
        resourcetype: QUESTION.PARAGRAPH,
      },
      config
    )
    .then(({ data }: AxiosResponse) => getQuestionModel(data));
};

export const cloneSurveyQuestionFact = (id: any) => {
  // const config = api.getConfigurations({
  // }, meta);
  return axios
    .post(`${API_URLS.ADMIN_SURVEY_QUESTION}${id}/clone/`)
    .then(({ data }: AxiosResponse) => getQuestionModel(data));
};

export const patchSurveyQuestionFact = (id: any, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  const payload = {
    id,
    resourcetype: data.type,
    label: data.question,
    text: data.question,
    mandatory: data.mandatory,
    collect_feedback: data.textFeedback,
    section: data.sectionId,
    ...data.meta,
  };
  if (data.updatedType) {
    payload["updated_resource_type"] = data.updatedType;
  }
  return axios
    .patch(`${API_URLS.ADMIN_SURVEY_QUESTION}${id}`, payload, config)
    .then(({ data }: AxiosResponse) => getQuestionModel(data));
};

export const removeSurveyQuestionFact = (id: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.delete(`${API_URLS.ADMIN_SURVEY_QUESTION}${id}`, config);
};

export const patchCheckboxOptionsFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  const { id, checkbox, type } = data;
  return axios.patch(
    `${API_URLS.ADMIN_SURVEY_QUESTION}${id}`,
    {
      id,
      resourcetype: type,
      options: checkbox.options,
    },
    config
  );
};

export const patchRadioOptionsFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  const { id, radio, type } = data;
  return axios.patch(
    `${API_URLS.ADMIN_SURVEY_QUESTION}${id}`,
    {
      id,
      resourcetype: type,
      options: radio.options,
    },
    config
  );
};

export const patchRatingOptionsFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  const { id, rating, type, hasSwap } = data;
  return axios.patch(
    `${API_URLS.ADMIN_SURVEY_QUESTION}${id}`,
    {
      id,
      resourcetype: type,
      is_reverse_scale: hasSwap,
      range_start: 1,
      range_end: rating.range_end,
      options: rating.tags,
    },
    config
  );
};

export const getAllPairingsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.GET_ALL_PAIR_FILES}`, config)
    .then((response: AxiosResponse) => {
      const { count_pages, count_items } = response.data;
      const results = response.data.results.map((item: any) => {
        return {
          key: item.id,
          title: item.title,
          dateAdded: item.uploaded_on,
          tags: item.tags,
          recordCount: item.records,
        };
      });
      return {
        results,
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const mapFileToIndexFact = (
  fileId: string,
  surveyId: string,
  meta?: any
) => {
  const config = util.api.getConfigurations(
    {
      file_id: fileId,
      index_id: surveyId,
    },
    {
      ...meta,
    }
  );
  return axios.post(
    "staff/files/associate-pairings/",
    {
      file_id: fileId,
      index_id: surveyId,
    },
    config
  );
};

export const getAllPairFilesFact = (id: any, params: any, meta?: any) => {
  const { page, search, size } = params;
  const config = util.api.getConfigurations(
    { page, search, size },
    {
      ...meta,
    }
  );
  return axios
    .get(`staff/files/pairs/${id}/`, config)
    .then((response: AxiosResponse) => {
      const { count_pages, count_items } = response.data;
      const results = response.data.results.map((item: any) => {
        return {
          key: item.id,
          title: item.title,
          dateAdded: item.uploaded_on,
          tags: item.tags,
          recordCount: item.total_records,
        };
      });
      return {
        results,
        totalPages: count_pages || 4,
        totalElements: count_items,
      };
    });
};

export const getBuilderEmailTemplateFact = (indexId: any) => {
  return axios
    .get("/email/invite/", {
      params: {
        index_id: indexId,
      },
    })
    .then((response: AxiosResponse) => {
      return response.data;
    });
};

export const getTemplatesFact = (
  indexId?: any,
  is_generic?: any,
  builderType = "survey"
) => {
  return axios
    .get(`/email/templates/?builder-type=${builderType}`, {
      params: {
        ...(indexId && { index: indexId }),
        ...(is_generic && { is_generic: true }),
      },
    })
    .then((response: AxiosResponse) => {
      return response.data;
    });
};

export const sendEmailReminderFact = (data: any) => {
  return axios.post("/email/reminder/", {
    ...data,
  });
};

export const sendCustomEmailReminderFact = (data: any) => {
  return axios.post("/email/custom/", {
    ...data,
  });
};

export const patchEmailTemplateFact = (data: any, id: any) => {
  return axios
    .patch(`/email/templates/${id}`, data)
    .then((response: AxiosResponse) => {
      return response.data;
    });
};

export const publishDraftSurveyFact = (indexId: any) => {
  return axios.post("/email/invite/", {
    survey_index: indexId,
  });
};

export const launchSurveyFact = (indexId: any) => {
  // https://v2.unmatched.app/api/v2/survey/admin/launch-survey/
  return axios.post("/survey/admin/launch-survey/", {
    index_id: indexId,
  });
};

export const getAllCommentQuestion = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.GET_ALL_COMMENT_QUESTION}`, config)
    .then((response: AxiosResponse) => {
      const { count_pages, count_items } = response.data;
      const results = response.data.results.map((item: any) => {
        return {
          id: item.question_id,
          question: item.question_text,
          reviewed: item.reviewed,
          unreviewed: item.unreviewed,
          total: item.total,
        };
      });
      return {
        results,
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

export const getAllComments = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });

  return axios
    .get(`${util.apiUrls.GET_ALL_COMMENTS}`, config)
    .then((response: AxiosResponse) => {
      const {
        count_pages,
        count_items,
        question_text,
        next_qid,
        previous_qid,
      } = response.data;
      const results = response.data.results.map((item: any) => {
        const { first_name, last_name, email } = item.target || {};
        const targetName = util.getFullName({
          firstName: first_name,
          lastName: last_name,
          email,
        });
        return {
          id: item.id,
          comment: item.edited_comment,
          original: item.original_comment,
          sentiment: item.sentiment,
          target: targetName,
          isBookmarked: item.is_bookmarked,
          isFlagged: item.flag,
          isReviewed: item.is_reviewed,
          question: item.question_text,
          updatedAt: item.updated_on,
          editedBy: item.comment_edited_by
            ? item.comment_edited_by.first_name +
              " " +
              item.comment_edited_by.last_name
            : null,
        };
      });
      return {
        results,
        title: question_text.label,
        next: next_qid,
        previous: previous_qid,
        totalPages: count_pages,
        totalElements: count_items,
      };
    });
};

interface PatchDataFormat {
  id: string;
  isReviewed: boolean;
  comment: string;
}
export const patchReview = (data: PatchDataFormat, meta?: any) => {
  const params = {
    edited_comment: data.comment,
    mark_as_reviewed: data.isReviewed,
  };
  const config = api.getConfigurations({}, meta);
  return axios
    .patch(`${util.apiUrls.COMMENT_OPERATION(data.id)}`, { ...params }, config)
    .then((response: AxiosResponse) => {
      const item = response.data;
      return {
        id: item.id,
        comment: item.edited_comment,
        original: item.original_comment,
        target: item.target.first_name + " " + item.target.last_name,
        isBookmarked: item.is_bookmarked,
        isReviewed: item.is_reviewed,
        question: item.question_text,
        updatedAt: item.updated_on,
        editedBy: item.comment_edited_by
          ? item.comment_edited_by.first_name +
            " " +
            item.comment_edited_by.last_name
          : null,
      };
    });
};

interface BookmarkDataFormat {
  commentId: string;
}
export const patchBookmark = (data: BookmarkDataFormat, meta?: any) => {
  const params = {
    comment: data.commentId,
  };
  const config = api.getConfigurations({}, meta);
  return axios.post(
    `${util.apiUrls.BOOKMARK_OPERATION}`,
    { ...params },
    config
  );
};

export const getDemographicsDataFact = (id: [string, number], meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios
    .get(`/survey/demographics/${id}/`, config)
    .then(({ data }: AxiosResponse) => {
      const { section } = data;
      const { components } = section;
      return {
        id,
        sections: [
          {
            id: section.id,
            label: section.label,
            name: section.name,
            order: section.order,
          },
        ],
        questions: components
          .filter((item: any) => item.resourcetype !== "QuestionNumber")
          .map(getQuestionModel),
      };
    });
};

export const getWavierQuestionFact = (id: any) => {
  // https://v2.unmatched.app/api/v2/survey/admin/report-eligibility/{id}/
  return axios
    .get(`/survey/report-eligibility/${id}/`)
    .then(({ data }: AxiosResponse) => {
      const wavier = data?.waiver_question_detailed || {};
      return {
        question: wavier.label,
        options: wavier.options
          ? Object.entries(wavier.options).map(([key, value]: any) => {
              return {
                id: `${key}-${value}`,
                label: key,
                value,
              };
            })
          : [],
        id: data?.waiver_question,
      };
    });
};

export const patchWavierQuestionFact = (data: any, id: any) => {
  return axios
    .patch(`survey/admin/component/${id}`, {
      id,
      label: data,
      text: data,
      resourcetype: "QuestionWaiver",
      type: "Waiver",
    })
    .then((response: AxiosResponse) => {
      return response.data;
    });
};

export const updateSurveyEndDateFact = (data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  return axios.post(
    "/survey/admin/index/deadline",
    {
      index_id: data.indexID,
      deadline: data.deadline,
    },
    config
  );
};
export const patchSurveyPropertyFact = (id: string, data: any, meta?: any) => {
  const config = api.getConfigurations({}, meta);
  // debugger
  return axios
    .patch(
      `${API_URLS.ADMIN_SURVEYS}${id}/`,
      {
        show_submitted_status: data?.enableSubmittedStatus,
      },
      config
    )
    .then(({ data }: AxiosResponse) => {
      return data ? getSurveyIndexModel(data) : {};
    });
};

export const getUsersByCompositeCriteria = (data: any) => {
  return axios.post("/survey/admin/composite-to-users/", {
    composite_filter: { ...data },
  });
};

export const addPossibleRaters = (data: any, id: any, selectedTab: any) => {
  const segment =
    selectedTab === "raters" ? "possible-raters" : "possible-targets";
  const name =
    selectedTab === "raters" ? "possible_raters" : "possible_targets";
  return axios.post(`/survey/admin/index/${id}/${segment}/`, {
    [name]: data,
  });
};

// export const getPossibleRaters = (id: any) => {
//   return axios
//     .get(`/survey/admin/index/${id}/possible-raters/`)
//     .then(({ data }: AxiosResponse) => data);
// }

// getLogParticipants

export const getLogParticipants = (params: any, id: any) => {
  return axios
    .get(`/logs/email-recipients/?email_activity_log=${id}`, {
      params,
    })
    .then(
      (res: AxiosResponse) => {
        return {
          results: res.data.results.map((item: any) => {
            return {
              key: item.sent_to?.id,
              email: item.sent_to?.email,
              empId: item.sent_to?.emp_id,
              firstName: item.sent_to?.first_name,
              id: item.sent_to?.id,
              lastName: item.sent_to?.last_name,
              metadata: item.sent_to?.metadata,
            };
          }),
          totalPages: res.data.count_pages,
          totalElements: res.data.count_items,
        };
      },
      (err) => {
        console.log({ err });
        return {
          results: [],
        };
      }
    );
};

export const getPossibleRaters = (allParams: any, id: any) => {
  const { selectedTab, ...params } = allParams;
  const segment =
    selectedTab === "raters" ? "possible-raters" : "possible-targets";

  return axios
    .get(`/survey/admin/index/${id}/${segment}/`, {
      params,
    })
    .then(
      (res: AxiosResponse) => {
        return {
          results: res.data.results.map((item: any) => {
            return {
              key: item.id,
              email: item.email,
              empId: item.emp_id,
              firstName: item.first_name,
              id: item.id,
              lastName: item.last_name,
              metadata: item.metadata,
            };
          }),
          totalPages: res.data.count_pages,
          totalElements: res.data.count_items,
        };
      },
      (err) => {
        console.log({ err });
        return {
          results: [],
          totalPages: 0,
          totalElements: 0,
        };
      }
    );
};

export const getNudgeSettingsFact = (allParams: any, id: any) => {
  return axios
    .get(`survey/admin/nudge-settings/?index=${id}`, {
      params: allParams,
    })
    .then(
      (res: AxiosResponse) => {
        return res.data;
      },
      (err) => {
        console.log({ err });
        return {
          results: [],
        };
      }
    );
};

export const postNudgeSettingFact = (data: any) => {
  return axios.post(`/survey/admin/nudge-settings/`, {
    ...data,
  });
};

export const patchNudgeSettingFact = (data: any, id: any) => {
  return axios.patch(`/survey/admin/nudge-settings/${id}/`, {
    ...data,
  });
};

export const deleteNudgeSettingFact = (id: any) => {
  return axios.delete(`/survey/admin/nudge-settings/${id}/`, {});
};

export const getEmailLogs = (
  id: any,
  params: any,
  dedicatedTemplates: string
) => {
  return axios
    .get(
      `/logs/email-logs/?index_id=${id}&email_types=${
        dedicatedTemplates
          ? dedicatedTemplates
          : "REMINDER_INACTIVE,REMINDER_ACTIVE,CUSTOM,SPECIAL,SURVEY_INVITATION"
      }`,
      {
        params,
      }
    )
    .then(
      (res: AxiosResponse) => {
        return res;
      },
      (err) => {
        console.log({ err });
        return {
          results: [],
        };
      }
    );
};

export const deletePossibleRatersFact = (
  data: any,
  id: any,
  selectedTab: string
) => {
  const segment =
    selectedTab === "raters" ? "possible-raters" : "possible-targets";
  const name =
    selectedTab === "raters" ? "possible_raters" : "possible_targets";
  return axios.delete(`/survey/admin/index/${id}/${segment}/`, {
    data: {
      [name]: data,
    },
  });
};
