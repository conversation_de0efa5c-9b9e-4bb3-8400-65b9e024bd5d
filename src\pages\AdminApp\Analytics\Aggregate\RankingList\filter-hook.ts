import React from "react";

const useFilters = () => {
  const [year, setYear] = React.useState({
    options: [],
    search: "",
    selected: "",
  });

  // const [name, setName] = React.useState({
  //   options: [
  //     { id: 1, key: "sample", title: "2021" },
  //     { id: 2, key: "sample2", title: "2020" },
  //     { id: 3, key: "sample3", title: "2019" },
  //   ],
  //   search: "",
  //   selected: "",
  // });

  // const [empId, setEmpId] = React.useState({
  //   options: [
  //     { id: 1, key: "sample", title: "2021" },
  //     { id: 2, key: "sample2", title: "2020" },
  //     { id: 3, key: "sample3", title: "2019" },
  //   ],
  //   search: "",
  //   selected: "",
  // });

  // const onSearchName = (search: string) => {
  //   setName((_name: any) => {
  //     return {
  //       ..._name,
  //       search,
  //     };
  //   });
  // };

  // const onFilterName = (selected: any) => {
  //   setName((_name: any) => {
  //     return {
  //       ..._name,
  //       selected,
  //     };
  //   });
  // };

  const onSearchYear = (search: string) => {
    setYear((_name: any) => {
      return {
        ..._name,
        search,
      };
    });
  };

  const onFilterYear = (selected: any) => {
    setYear((_name: any) => {
      return {
        ..._name,
        selected,
      };
    });
  };

  const setYears = (_years: any) => {
    setYear({
      ...year,
      options: _years,
    });
  };

  // const onSearchEmpId = (search: any) => {
  //   setEmpId((_name: any) => {
  //     return {
  //       ..._name,
  //       search,
  //     };
  //   });
  // };

  // const onFilterEmpId = (selected: any) => {
  //   setEmpId((_name: any) => {
  //     return {
  //       ..._name,
  //       selected,
  //     };
  //   });
  // };

  return {
    name,
    year,
    // empId,
    // onSearchName,
    // onFilterName,
    setYears,
    onSearchYear,
    onFilterYear,
    // onSearchEmpId,
    // onFilterEmpId,
  };
};

export default useFilters;
