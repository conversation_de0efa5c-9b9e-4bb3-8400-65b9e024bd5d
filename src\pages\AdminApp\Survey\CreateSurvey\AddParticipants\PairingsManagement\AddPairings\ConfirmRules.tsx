import React from "react";
import { Div, CustomModal as <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "unmatched/components";
import React<PERSON><PERSON> from "react-json-view";
import { Link } from "react-router-dom";

export default function ConfirmRules(props: any) {
  return (
    <>
      <Modal.Body>
        <Text.H2>Confirm Rules</Text.H2>
        <Text.P1 className="mt-2">
        Below are the rules that will be applied for validating each of the pairings. Contact <Link to="#" onClick={() => window.location = 'mailto:<EMAIL>'}><EMAIL></Link> for any support on the rules. 
        </Text.P1>
        <Div className="d-flex  flex-column pt-4" style={{ minHeight: "50vh" }}>
          <ReactJson src={props.survey?.data?.pairingRules || {}} />
        </Div>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={() => props.setScreen(2)} variant="primary" className="float-right">
          Confirm and Upload
        </Button>
      </Modal.Footer>
    </>
  );
}

