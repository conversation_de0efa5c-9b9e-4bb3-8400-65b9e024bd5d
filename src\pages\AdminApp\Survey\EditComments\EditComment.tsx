import React from "react";
import { useDebounce } from "react-use";
import {
  Div,
  FormControl,
  Layout,
  CustomModal as Modal,
  Text,
  Button,
  Icon,
  Sentiment,
} from "unmatched/components";
import util from "unmatched/utils";
import ModalHeader from "../../ModalHeader";
import InLineDiff from "unmatched/components/InlineDiff";
// import { DiffViewer } from "./DiffViewer";

// import PropTypes from 'prop-types'

const Pannel = (props: {
  children: React.ReactNode;
  title: string;
  loading: boolean;
  sentiment?: string;
  editedBy?: any;
  updatedAt?: string;
}) => {
  return (
    <Div
      className={`${util.getUtilClassName({ pb: 0 })}  h-100 position-relative`}
    >
      <Text.P1>
        {props.title}{" "}
        {!props.sentiment ? null : props.loading ? (
          <i>(Loading...)</i>
        ) : (
          <>
            -{" "}
            <Sentiment sentiment={props.sentiment || ""} className="d-inline" />
          </>
        )}
      </Text.P1>
      {props.editedBy && (
        <Text.P2
          className="position-absolute"
          style={{ fontSize: 10, top: 2, right: 0 }}
        >
          Edited on {util.date.getBrowserTime(props.updatedAt, "dd/MM/yyyy")} by{" "}
          {props.editedBy}
        </Text.P2>
      )}
      <Div className={`h-100${util.getUtilClassName({ py: 2 })}`}>
        {props.children}
      </Div>
    </Div>
  );
};

const EditComment = (props: any) => {
  const {
    onHide,
    show,
    selected,
    onCommentChange,
    onNext,
    onPrev,
    onSave,
    loading,
    isNext,
    isPrev,
    pageNav,
  } = props;

  const [comment, setComment] = React.useState(selected.comment);
  const [startedEditing, setStartedEditing] = React.useState(false);
  const [showEdits, setShowEdits] = React.useState(false);

  // const [nav, setNav] = React.useState(0);

  useDebounce(
    () => {
      if (startedEditing) return onCommentChange(comment, false);
    },
    1000,
    [comment]
  );

  useDebounce(
    () => {
      if (pageNav !== 0) return setComment(selected.comment);
    },
    1,
    [pageNav]
  );

  return (
    <Modal show={show} backdrop="static" centered dialogClassName="modal-70w">
      <ModalHeader
        title="Comment"
        onHide={() => {
          onHide();
          setStartedEditing(false);
        }}
      />
      <Modal.Body className="pb-0">
        {/* <Button
          variant="link"
          className="text-primary fs-12 p-0"
          style={{ marginRight: 120, width: 88 }}
          onClick={() => {
            setShowEdits((s) => !s);
          }}
          disabled={loading}
        >
          <Icon
            icon={(showEdits ? "fas fa-eye-slash" : "fas fa-eye") + ` mr-2`}
          />
          {showEdits ? `Hide ` : "Show"} Edits
        </Button> */}
        {/* <DiffViewer oldComment={selected?.original} newComment={comment} /> */}
        <Div className="row">
          <Div className="col-12 col-sm-6">
            <Pannel
              title="Original Comment"
              sentiment={selected.sentiment}
              loading={loading}
            >
              <FormControl.Textarea
                className="border"
                style={{
                  height: "calc(50vh - 30px)",
                  borderBottomLeftRadius: 0,
                  borderBottomRightRadius: 0,
                }}
                value={loading ? "Loading..." : selected?.original}
                disabled={true}
              />
              <FormControl.Text
                disabled
                className="text-muted fs-12  border-left border-right border-bottom rounded-bottom"
                value={
                  loading
                    ? "Loading..."
                    : selected?.target
                    ? `Receiver: ${selected?.target}`
                    : ""
                }
                style={{
                  height: 35,
                  borderTopLeftRadius: 0,
                  borderTopRightRadius: 0,
                }}
              />
              {/* <Div className="row justify-content-between position-absolute w-100 mx-0" >
                <Div className="col-12 px-0">
                  
                </Div>
              </Div> */}
            </Pannel>
          </Div>
          <Div className="col-12 col-sm-6">
            <Pannel
              title="Edit Comment"
              editedBy={selected?.editedBy}
              updatedAt={selected?.updatedAt}
              loading={loading}
            >
              {showEdits ? (
                <div
                  id="box"
                  style={{
                    height: "calc(50vh - 30px)",
                    border: "1px solid #dee2e6",
                    padding: 10,
                    backgroundColor: "#fcfcfc",
                    borderRadius: 4,
                  }}
                >
                  <InLineDiff
                    oldValue={selected?.original}
                    newValue={comment}
                  />
                </div>
              ) : (
                <FormControl.Textarea
                  className="border"
                  style={{
                    height: "calc(50vh - 30px)",
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                  }}
                  value={loading ? "Loading..." : comment}
                  onChange={(e: any) => {
                    setComment(e.target.value);
                    if (!startedEditing) setStartedEditing(true);
                  }}
                />
              )}

              <Div
                className="row justify-content-between position-absolute w-100 mx-0 border-left border-right border-bottom rounded-bottom py-1"
                style={{ height: 35 }}
              >
                <Div className="col-12">
                  <Div className="text-right">
                    <Button
                      variant="link"
                      className="text-primary fs-12 p-0"
                      style={{ marginRight: 120, width: 88 }}
                      onClick={() => {
                        setShowEdits((s) => !s);
                      }}
                      disabled={loading}
                    >
                      <Icon
                        icon={
                          (showEdits ? "fas fa-eye-slash" : "fas fa-eye") +
                          ` mr-2`
                        }
                      />
                      {showEdits ? `Hide ` : "Show"} Edits
                    </Button>
                    {selected?.original !== comment && (
                      <Button
                        variant="link"
                        className="text-primary fs-12 p-0"
                        // style={{ bottom: 10 }}
                        onClick={() => {
                          setStartedEditing(true);
                          setComment(selected?.original);
                        }}
                        disabled={loading}
                      >
                        <Icon icon="fal fa-sync mr-2" />
                        Restore Comment
                      </Button>
                    )}
                    {comment && comment.length > 0 && (
                      <Button
                        variant="link"
                        className="text-danger fs-12 p-0 ml-3"
                        onClick={() => {
                          setStartedEditing(true);
                          setComment("");
                        }}
                        disabled={loading}
                      >
                        <Icon icon="fal fa-trash mr-1" /> Delete Comment
                      </Button>
                    )}
                  </Div>
                </Div>
              </Div>
            </Pannel>
          </Div>
        </Div>
      </Modal.Body>
      <Div className={util.getUtilClassName({ p: 3 })}>
        <Layout.Row>
          <Layout.Col className={util.getUtilClassName({ textAlign: "right" })}>
            {selected?.isReviewed ? (
              <Button
                className={`${util.getUtilClassName({
                  mr: 2,
                })} text-success fs-12`}
                variant="link"
                disabled={loading}
              >
                <Icon icon="fal fa-check mr-2" /> Done
              </Button>
            ) : (
              <Button
                className={util.getUtilClassName({ mr: 2 })}
                onClick={() => onSave(selected?.id, comment, true)}
                variant="primary"
                disabled={loading}
              >
                Mark as reviewed
              </Button>
            )}
            <Button
              className={util.getUtilClassName({ mr: 2 })}
              onClick={() => {
                onPrev(selected?.id);
              }}
              variant="outline-primary"
              disabled={loading || isPrev(selected?.id)}
            >
              <Icon icon="fal fa-chevron-left" />
            </Button>
            <Button
              onClick={() => {
                onNext(selected?.id);
              }}
              variant="outline-primary"
              disabled={loading || isNext(selected?.id)}
            >
              <Icon icon="fal fa-chevron-right" />
            </Button>
          </Layout.Col>
        </Layout.Row>
      </Div>
    </Modal>
  );
};

const dummyFunction = () => "";

EditComment.defaultProps = {
  onHide: dummyFunction,
  show: false,
  selected: {},
  onCommentChange: dummyFunction,
  onPrevious: dummyFunction,
  onSave: dummyFunction,
};

export default EditComment;
