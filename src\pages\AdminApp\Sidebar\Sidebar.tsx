import React from "react";
import { useHistory } from "unmatched/hooks";
import { Div, SidebarMenuItem } from "unmatched/components";
import DASHBOARD_LINKS from "../admin-app-meta";
import styled from "styled-components";
import { Link } from "react-router-dom";
// import util from "unmatched/utils";

export const MenuFooter = styled(Div)`
  position: absolute;
  bottom: 0px;
  width: 100%;
`;

export default function Sidebar() {
  // const { onLogout, name } = props;
  const history = useHistory();

  return (
    <Div className="dashboard-sidebar">
      <Div className="text-center">
        {DASHBOARD_LINKS.filter((el: any) => el.id !== 6).map((item: any) => (
          <Div key={`sidebar-${item.id} un-sidebar`}>
            <SidebarMenuItem
              buttonContent={item.icon}
              title={item.title}
              items={item.children}
              route={item.route}
              isActive={history.location.pathname.includes(item.route)}
              as={Link}
              pathName={history.location.pathname}
            />
          </Div>
        ))}
        {/* <MenuFooter>
          <SidebarMenuItem
            isActive
            buttonContent={name}
            bottom
            title="Session"
            items={settingRoutes}
          />
        </MenuFooter> */}
      </Div>
    </Div>
  );
}
