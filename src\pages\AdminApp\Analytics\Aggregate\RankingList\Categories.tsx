import React from "react";
import { Table, Text, Layout } from "unmatched/components";
// import appUrls from "unmatched/utils/urls/app-urls";
import { useTable } from "unmatched/hooks";
// import useFilters from "./filter-hook";
// import JSON_DATA from "./people-meta";
// import PeopleFilters from "./PeopleFilters";
import TableContainer from "./TableContainer";
// import { getCategoryAnalyticsFromId } from "../../analytics-api";
// import { Link } from "react-router-dom";
import { getCategoryRankingFact } from "../aggregate-api";
import util from "unmatched/utils";
import AnalyticsFilters from "../../Shared/AnalyticsFilters/AnalyticsFilters";
import useAnalyticsFilters from "../../Shared/AnalyticsFilters/hook";
// import useToastr from "unmatched/modules/toastr/hook";
import useFilter from "pages/CommonFilters/hook";
// import PropTypes from 'prop-types'

interface User {
  key: string;
  overallAverage: string;
  selfAverage: string;
  categoryAverage: string;
  frequency: string;
  target: {
    id: string;
    empId: string;
    firstName: string;
    lastName: string;
    email: string;
    metadata: any;
  };
  index: {
    id: string;
    title: string;
  };
}

const defaultSelected = { id: "", key: "", title: "", values: [] };

const getFilterOptions = (_filters: any) => {
  const filters = _filters || {};
  return util.lib.entries(filters).map((item: any) => {
    const [key, value] = item;
    return {
      id: key,
      key,
      title: value.label,
      values: value.values,
    };
  });
};

// interface Request {
//   page?: number;
//   sort?: string;
// }

const getSurveys = (_filters: any) => {
  const hasGroups = _filters.selected.groups.length;
  return {
    groups: _filters.selected.groups,
    surveys: hasGroups ? [] : _filters.selected.surveys,
    type: hasGroups ? ["surveyindex360"] : _filters.selected.types || [],
  };
};

const Categories = () => {
  // const { id } = props;
  const tableMeta = useTable({
    page: 1,
    size: 10,
    totalPages: 10,
  });
  const [users, setUsers] = React.useState<Array<User>>([]);
  // const [categories, setCategories] = React.useState<Array<any>>([]);

  const [columns, setColumns]: any = React.useState({
    // emp_id: { label: "Emp ID", sortValue: "", hasSort: true, order: 1 },
    label: {
      label: "Name",
      sortValue: "",
      hasSort: true,
      isDynamic: false,
      order: 1,
    },
    frequency: {
      label: "No of Ratings",
      sortValue: "",
      hasSort: true,
      isDynamic: false,
      order: 2,
    },
  });
  // const [dynamicColumns, setDynamicColumns] = React.useState({});

  // const [userColumns, setUserColumns] = React.useState({});

  const [categoryColumns, setCategoryColumns] = React.useState({});
  const [selected, setSelected] = React.useState(defaultSelected);
  const [isLoaded, setLoaded] = React.useState(false)
  const metaFilters = useFilter();
  let filterOptions = getFilterOptions(metaFilters.filters);

  const filters = useAnalyticsFilters();

  // const toastr = useToastr();

  React.useEffect(() => {
    metaFilters.getFilters((_metaFilters: any) => {
      filterOptions = getFilterOptions(_metaFilters);
      const [item] = filterOptions;
      if (item) {
        setSelected(item);
      }
      onTableLoad("", item.key);
    });
    //eslint-disable-next-line
  }, []);

  const onTypeChange = (item: any) => {
    const surveys =
      filters.surveysData.filter((_survey: any) => {
        if (
          _survey.type === "surveyindex360group" &&
          item.key === "surveyindex360"
        )
          return true;
        return _survey.type === item.key;
      }) || [];
    const [selectedSurvey]: any = surveys || {};
    const groups: any = selectedSurvey.rater_groups?.map((group: any) => ({
      id: group.id,
      key: group.id,
      title: group.title,
      groupId: selectedSurvey.id,
      groupKey: group["rater_group"],
    }));
    getCategoryAnalytics(
      {},
      {
        ...filters,
        groups,
        selected: {
          ...filters.selected,
          types: [
            item.key === "surveyindex360" ? "surveyindex360group" : item.key,
          ],
          surveys:
            selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
          groups: [],
        },
      },
      selected.key
    );
  };

  const onTableLoad = (year?: any, meta?: any) => {
    filters
      .getFilters({
        year,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          getCategoryAnalytics(
            {},
            {
              ..._filters,
              surveys: _filters.surveys.filter(
                (item: any) => selectedSurvey?.type === item.type
              ),
              selected: {
                ..._filters.selected,
                surveys:
                  selectedSurvey && selectedSurvey.id
                    ? [selectedSurvey.id]
                    : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                // groups: params?.groups || _filters.selected.groups,
              },
            },
            meta
          );
        },
        (err: any) => {
          console.log(err);
          tableMeta.setLoading(false);
        }
      );
  };

  const getCategoryAnalytics = async (
    { sort }: any,
    _filters?: any,
    meta?: any
  ) => {
    try {
      tableMeta.setLoading(true);
      const peopleAnalytics = await getCategoryRankingFact(
        {
          year: _filters.selected.years,
          sort,
          ...getSurveys(_filters),
        },
        meta
      );
      filters.setFilters(_filters);
      tableMeta.setLoading(false);
      setUsers(peopleAnalytics.data.results);
      
      if(isLoaded){
        return
      }
      setCategoryColumns((_columns : any) => {
        let output = {};
        peopleAnalytics.categoryColumns.forEach((item: any) => {
          output = {
            ..._columns,
            ...output,
            [item.key]: { label: item.label, isDynamic: true, hasSort: true },
          };
        });
        return {
          ..._columns,
          ...output,
        };
      });
      setLoaded(true)
    } catch (e) {
      console.log(e);
      tableMeta.setLoading(false);
    }
  };

  const getFiltersTemplate = () => {
    return (
      <>
        <Layout.Row className="mb-4">
          <Layout.Col className="" xl="3">
            <Table.Filter
              title="Select"
              selected={selected.key}
              selectedLabel={selected.title}
              options={filterOptions}
              onSelect={(item: any) => {
                setSelected(item);
                getCategoryAnalytics({}, filters, item.key);
              }}
            />
          </Layout.Col>
        </Layout.Row>
        <AnalyticsFilters
          filters={filters}
          hideGroups
          config={{
            surveys: {
              multiple: false,
            },
          }}
          onYearChange={(item: any) => {
            onTableLoad(item.key);
          }}
          onTypeChange={onTypeChange}
          onSurveyChange={(items: any) => {
            const selectedSurvey: any =
              filters.surveys.find((item: any) => items.includes(item.id)) ||
              {};
            const groups: any = selectedSurvey.rater_groups?.map(
              (group: any) => ({
                id: group.id,
                key: group.id,
                title: group.title,
                groupId: selectedSurvey.id,
                groupKey: group["rater_group"],
              })
            );
            getCategoryAnalytics(
              {},
              {
                ...filters,
                groups,
                selected: {
                  ...filters.selected,
                  surveys: items,
                  groups: [],
                },
              },
              selected.key
            );
          }}
          onGroupChange={(items: any) => {
            getCategoryAnalytics(
              {},
              {
                ...filters,
                selected: {
                  ...filters.selected,
                  groups: items,
                },
              },
              selected.key
            );
          }}
        />
      </>
    );
  };

  const getColumnsData = () => {
    const columnsArray = tableMeta.getColumnsArray(columns);
    // const [userInfo, ] = tableMeta.splitColumns(columnsArray);
    // const cats = tableMeta
    //   .filterColumns(tableMeta.getColumnsArray(categoryColumns))
    //   .map((_d: any) => {
    //     console.log(_d)
    //     return { ..._d, hasSort: true, sortValue: '' };
    //   });
    const output = [
      ...columnsArray,
      ...tableMeta.filterColumns(tableMeta.getColumnsArray(categoryColumns)),
      // ...tableMeta.getColumnsArray(categoryColumns)
      // ...tableMeta.getColumnsArray(dynamicColumns),
      // ...cats
    ];
    return output;
  };

  const getRowsTemplate = () => {
    return users.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.name}>
          <Table.Data>
            <Text.P1>{item.name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.frequency}</Text.P1>
          </Table.Data>
          {/* <Table.Data>
            <Link to={appUrls.admin.analytics.people.getAnalyticsUrl(item.id)}>
              <Text.P1>{item.name}</Text.P1>
            </Link>
          </Table.Data> */}
          {/* {tableMeta.getColumnsArray(userColumns).map(({ key }: any) => {
            return (
              <Table.Data key={key}>
                <Text.P1>
                  {item[key] || (
                    <span className="text-muted">
                      <i>Unavailable</i>
                    </span>
                  )}
                </Text.P1>
              </Table.Data>
            );
          })} */}
          {tableMeta
            .filterColumns(tableMeta.getColumnsArray(categoryColumns))
            .map(({ key }: any) => {
              return (
                <Table.Data key={key}>
                  <Text.P1>
                    {/* {JSON.stringify(rest)} */}
                    {item[key] ?? (
                      <span className="text-muted">
                        <i>Unavailable</i>
                      </span>
                    )}
                  </Text.P1>
                </Table.Data>
              );
            })}
        </Table.Row>
      );
    });
  };

  if (!users?.length) return null;

  return (
    <TableContainer
      title={"Ranking - By Category Average"}
      filters={getFiltersTemplate()}
    >
      
      <Table
        tableClass="bg-white"
        columns={getColumnsData()}
        isLoading={tableMeta.isLoading}
        rows={users}
        customRows
        render={() => getRowsTemplate()}
        onSort={(item: any) => {
          getCategoryAnalytics(
            {
              sort: util.label.getSortingLabel(item.key, item.sortValue),
            },
            filters,
            selected.key
          );
          // setColumns(tableMeta.resetColumns(columns, item));
          if (item.isDynamic) {
            // setDynamicColumns(tableMeta.resetColumns(dynamicColumns, item));
            setCategoryColumns(tableMeta.resetColumns(categoryColumns, item) || {});
        
          } else {
            setColumns(tableMeta.resetColumns(columns, item) || {});
        
          }
          //
        }}
        onNavigate={({ nextArrow, prevArrow }: any) => {
          const arr = tableMeta.getColumnsArray(categoryColumns);
          if (nextArrow) {
            const index =
              arr.findIndex((item: any) => item.key === nextArrow) + 1;
            tableMeta.setRenderIndex(
              index >= arr.length ? arr.length - 1 : index
            );
          } else if (prevArrow) {
            const index =
              arr.findIndex((item: any) => item.key === prevArrow) - 1;
            tableMeta.setRenderIndex(index < 0 ? 0 : index);
          }
        }}
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
      />
    </TableContainer>
  );
};

// Categories.propTypes = {

// }

export default Categories;
