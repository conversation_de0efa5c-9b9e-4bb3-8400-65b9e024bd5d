// Node modules
import React, { useState } from "react";
import { debounce } from "lodash";
import "react-quill/dist/quill.snow.css";
import Header from "../Header";
import {
  Text,
  Layout,
  Div,
  Nav,
  ComboFilter,
  Button,
  MultiSelect,
  Icon,
  // FormControl,
  // ComboBasicFilter,
} from "unmatched/components";
import { useCreateSurveyContext } from "../Provider";
import useFilter from "pages/CommonFilters/hook";
import {
  addPossibleRaters,
  getUsersByCompositeCriteria,
} from "../../survey-api";
import TableCommon from "./components/TableCommon";
import {
  optionStyles,
  CustomOption,
} from "pages/AdminApp/Email/components/QuickSend";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";
import useToastr from "unmatched/modules/toastr/hook";
import { UMTab } from "unmatched/components/Tabs";
import { DateTime } from "luxon";
// import { useQuery } from "unmatched/hooks";
// import appUrls from "unmatched/utils/urls/app-urls";

const Rules = (props: any) => {
  let { survey } = useCreateSurveyContext();

  if (props.survey) {
    survey = props.survey;
  }

  const [userOptions, setUserOptions] = useState<any>([]);
  const [refreshRatersTable, setRefreshRatersTable] = useState(false);
  const [addType, setAddType] = useState("composite");

  //   const queryParams = useQuery();
  const compFiltersState = useFilter();
  const { showToast } = useToastr();

  React.useEffect(() => {
    compFiltersState.getFilters();
    // const getRaters = async () => {
    //     const raters = await getPossibleRaters(survey.data?.id);
    //     setRaters(raters);
    // }
    // survey.data?.id && getRaters();
  }, [survey.data?.id]);

  const [filter, setFilter] = useState("raters");
  const [selectedEmails, setSelectedEmails] = useState<any>([]);

  const onFilterChange = (_filter: string) => {
    setFilter(_filter);
  };

  const addUsersByCriteria = async () => {
    const res = await getUsersByCompositeCriteria(compFiltersState?.selected);
    if (res?.status === 200 && res.data?.filtered_users?.length) {
      await addPossibleRaters(res.data.filtered_users, survey.data?.id, filter);
      setRefreshRatersTable(true);
      compFiltersState.onSelect({});
      showToast({
        variant: "success",
        title: "Users added successfully",
        content: "New users were added successfully",
      });
    }
  };

  const addUsersByEmail = async () => {
    if (selectedEmails.length) {
      await addPossibleRaters(
        selectedEmails.map((user: any) => user.id),
        survey.data?.id,
        filter
      );
      setRefreshRatersTable(true);
      setSelectedEmails([]);
      showToast({
        variant: "success",
        title: "Users added successfully",
        content: "New users were added successfully",
      });
    }
  };

  const getComputedUsers = (users: any) => {
    const usersData = users.map((u: any) => ({
      ...u,
      label: u.email,
      value: u.id,
    }));
    return usersData;
  };

  const getUserOptions = debounce((search: string) => {
    if (!search) return;
    searchUsersFact("", { search })
      .then((res) => {
        const data = getComputedUsers(res.data?.results || []);
        setUserOptions(data);
      })
      .catch((err) => console.log(err));
  }, 200);

  const emailInputProps = {
    closeMenuOnSelect: false,
    isCustom: true,
    onInputChange: getUserOptions,
    isMulti: true,
    placeholder: "",
    options: userOptions,
    CustomOption,
    styles: optionStyles,
    postfix: () =>
      selectedEmails.length > 0 ? (
        <span
          onClick={(e: any) => {
            e.stopPropagation();
            setSelectedEmails([]);
          }}
        >
          <Icon
            className="mr-2 fw-300 fs-12 cursor-pointer"
            icon="fas fa-times"
          />
        </span>
      ) : null,
    customValContainer: true,
  };

  const getNavItem = (title: string, key: string) => {
    return (
      <UMTab
        eventKey={key}
        activeKey={filter}
        onClick={() => {
          onFilterChange(key);
          setSelectedEmails([]);
          compFiltersState.onSelect({});
        }}
      >
        {title}
      </UMTab>
    );
  };

  const renderAddType = () => {
    return (
      <>
        <Div className="section-title mb-2">
          Add {filter === "raters" ? "Reviewers" : "Reviewees"}
        </Div>
        <MultiSelect
          closeMenuOnSelect
          options={[
            {
              label: `Add ${getContext()} by composite filter`,
              value: "composite",
            },
            { label: `Add ${getContext()} by email`, value: "email" },
          ]}
          value={
            addType === "composite"
              ? {
                  label: `Add ${getContext()} by composite filter`,
                  value: "composite",
                }
              : { label: `Add ${getContext()} by email`, value: "email" }
          }
          isMulti={false}
          onSelect={async (selected: any) => {
            setAddType(selected.value);
          }}
          className="review-select"
        />
      </>
    );
  };

  const getContext = () => {
    return filter === "raters" ? "reviewers" : "reviewees";
  };

  return (
    <Div>
      <Header
        metaItem={
          <Text.P1>
            {survey?.data?.updatedAt &&
              DateTime.fromISO(
                new Date(survey.data.updatedAt).toISOString()
              ).toFormat(" LLL dd, yyyy, hh:mm a")}
          </Text.P1>
        }
        styles={{ height: 100 }}
        title={
          <Div>
            <Text.H1 className="pb-2">{survey.data.name}</Text.H1>
            <Div className="sticky-tabs-container">
              <Nav className="nav-tabs sticky">
                {getNavItem("Reviewers", "raters")}
                {getNavItem("Reviewees", "targets")}
              </Nav>
            </Div>
          </Div>
        }
        breadcrumbs={props.breadcrumbs}
      />
      <Layout.Container style={{ marginTop: 22 }} className="py-3 mb-3" fluid>
        <Div className="mt-4 mb-4 w-50">{renderAddType()}</Div>
        {/* <Layout.Row style={{ marginBottom: 10 }} className="my-4">
          <Layout.Col xl={4}>
            <FormControl.Radio>
              <FormControl.Radio.Input
                checked={addType === "composite"}
                onChange={() => {
                  setAddType("composite");
                }}
              />
              <FormControl.Radio.Label>
                Add {getContext()} by composite filter
              </FormControl.Radio.Label>
            </FormControl.Radio>
          </Layout.Col>
          <Layout.Col xl={5}>
            <FormControl.Radio>
              <FormControl.Radio.Input
                checked={addType === "email"}
                onChange={() => {
                  setAddType("email");
                }}
              />
              <FormControl.Radio.Label>
                Add {getContext()} by email
              </FormControl.Radio.Label>
            </FormControl.Radio>
          </Layout.Col>
        </Layout.Row> */}
        {addType === "composite" && (
          <Layout.Row className="my-2">
            <Layout.Col lg={11} xl={11}>
              <Div style={{ fontSize: 11 }}>
                Choose a criteria to add them as {getContext()}
              </Div>
              <ComboFilter
                filters={compFiltersState.filters}
                selected={compFiltersState.selected}
                onFilterSelect={(_selected: any) => {
                  compFiltersState.onSelect(_selected);
                  // getUsers(_selected, { search: tableMeta.search });
                }}
                onSubmit={() => ""}
              />
            </Layout.Col>
            <Layout.Col className="d-flex align-items-end" lg={1} xl={1}>
              <Button
                variant="primary"
                onClick={(e: any) => {
                  e.stopPropagation();
                  addUsersByCriteria();
                }}
                className="mr-2"
              >
                Add
              </Button>
            </Layout.Col>
          </Layout.Row>
        )}
        {addType === "email" && (
          <Layout.Row className="my-2">
            <Layout.Col lg={11} xl={11}>
              <Div style={{ fontSize: 11 }}>
                Search and add {getContext()} by email
              </Div>
              <MultiSelect
                {...emailInputProps}
                onSelect={(users: any) => {
                  setSelectedEmails(users);
                }}
                hasError={false}
                wrap
                placeholder="Search users by email or name"
                value={selectedEmails}
              />
            </Layout.Col>
            <Layout.Col className="d-flex align-items-end" lg={1} xl={1}>
              <Button
                variant="primary"
                onClick={(e: any) => {
                  e.stopPropagation();
                  addUsersByEmail();
                }}
                className="mr-2"
              >
                Add
              </Button>
            </Layout.Col>
          </Layout.Row>
        )}
        <TableCommon
          refreshRatersTable={refreshRatersTable}
          setRefreshRatersTable={setRefreshRatersTable}
          filter={filter}
        />
      </Layout.Container>
    </Div>
  );
};

export default Rules;
