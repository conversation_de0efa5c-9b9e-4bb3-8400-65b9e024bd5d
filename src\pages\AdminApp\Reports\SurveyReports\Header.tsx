import React from "react";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import { Text, Span, Icon } from "unmatched/components";
// import PropTypes from 'prop-types'

const Header = (props: any) => {
  const { survey, layout, metaTemplate, breadcrumbs, title } = props;

  const getHeaderTitleTemplate = () => {
    return <Text.H2>{survey.name}</Text.H2>;
  };

  return (
    <CustomHeader
      style={{ marginLeft: layout.marginLeft }}
      title={title || getHeaderTitleTemplate()}
      information={
        <>
          <Span className="text-muted px-3 fs-8 pt-1">
            <Icon icon="far fa-circle" />
          </Span>
          <Text.H2 className="text-muted fs-14 fw-400">
            {survey.startDate} - {survey.endDate}
          </Text.H2>
        </>
      }
      metaItem={metaTemplate}
      breadcrumbs={breadcrumbs}
    />
  );
};

// Header.propTypes = {

// }

export default Header;
