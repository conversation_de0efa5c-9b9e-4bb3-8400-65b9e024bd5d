import React, { useEffect } from "react";
import _ from "lodash";
import {
  Div,
  FormControl,
  Text,
  Layout,
  Tabs,
  Tab,
  Table,
  PageContainer,
} from "unmatched/components";
import { useTable, useState, useXHR, useHistory } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import appUrls from "unmatched/utils/urls/app-urls";
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
import { getSurveysFact, getSurveysFactV2 } from "pages/AdminApp/Survey/survey-api";
import util from "unmatched/utils";
// import { Survey } from "unmatched/utils/enums";
import { get360ReportLists } from "../reports-api";
import loading from "assets/images/load.gif";

const { Survey } = util.enums;

const SurveyList = () => {
  const tableMeta = useTable({
    totalPages: 0,
    size: 15,
    page: 1,
    totalItems: 500,
  });
  // React.useEffect(() => {
  //   getSurveys();
  // }, []);

  const surveyData = useXHR({ defaultResponse: [] }, "surveys");
  const toastr = useToastr();
  const [filter, setFilter] = useState<any>(Survey.Upward);
  const [temp, setTemp] = React.useState("");
  const history = useHistory();
  const [disableTabs, setDisableTabs] = useState(false);

  const [ordering, setOrdering] = useState({
    360: "title",
    other: "-deadline",
  });

  const [params, setParams] = useState({
    search: "",
    // filter: Survey.Upward,
    size: 15,
    page: 1,
    totalElements: 0,
    totalPages: 0,
    include_historic: true,
  });

  useEffect(() => {
    getSurveys(params);
  }, [filter, params.search]);

  const [columnsData, setColumnsData] = useState<any>(
    getRspColumns(Survey.Upward)
  );

  const getSurveys = (lParams?: any) => {
    surveyData.setLoading(true);
    setDisableTabs(true);
    if (filter === Survey._360Degree) {
      get360ReportLists({
        page: tableMeta.page,
        size: tableMeta.size,
        include_historic: true,
        ...(lParams || {}),
        ordering: lParams?.ordering || ordering[360],
        search: params.search.length > 0 ? params.search : undefined,
      }).then(
        ({ data, totalPages, totalElements }: any) => {
          tableMeta.updatePagination({ totalPages, totalItems: totalElements });
          surveyData.setLoading(false);
          surveyData.setSurveys(data);
          setDisableTabs(false);
        },
        (err: any) => {
          surveyData.onError(err);
          toastr.onError(err);
          setDisableTabs(false);
        }
      );
    } else {
      const reqParams = {
        // search,
        include_historic: true,
        filter: "ENDED",
        page: tableMeta.page,
        size: tableMeta.size,
        resource_type: `${
          filter === Survey.Upward ? `${filter},${Survey.Self}` : filter
        }`,
        ...(lParams || {}),
        sort: lParams?.sort || ordering.other,
        search: params.search.length > 0 ? params.search : undefined,
      };
      getSurveysFactV2(
        reqParams,
        surveyData.getMetaConfig()
      ).then(
        ({ data, totalPages, totalElements }: any) => {
          getSurveysFact(
            reqParams,
            surveyData.getMetaConfig()
          ).then(
            ({ data }: any) => {
              surveyData.setSurveys(data);
              setDisableTabs(false);
            }
          );
          tableMeta.updatePagination({ totalPages, totalItems: totalElements });
          surveyData.setLoading(false);
          surveyData.setSurveys(data);
        },
        (err: any) => {
          surveyData.onError(err);
          toastr.onError(err);
          setDisableTabs(false);
        }
      );
    }
  };

  const getColumns = () => {
    let columnsList = _.keys(columnsData);
    columnsList = _.map(columnsList, (key: string) => ({
      ..._.get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const onFilterChange = (tab: string) => {
    setColumnsData(getRspColumns(tab));
    // setParams((p: any) => ({
    //   ...p,
    //   filter: tab,
    // }));
    setFilter(tab);
    tableMeta.updatePagination({ page: 1, totalItems: null });
  };

  const onSearchChange = (search: string) => {
    const page = 1;
    tableMeta.onPageSelect(page);
    setParams((_params: any) => ({
      ..._params,
      search,
      page: 1,
    }));
  };

  const getTabsTemplate = () => (
    <Div className="custom-tabs-2 new-tab-ui">
      <Tabs
        activeKey={filter}
        {...(!surveyData.isLoading && !disableTabs && {
          onSelect: (tab: any) => onFilterChange(tab),
        })}
      >
        <Tab eventKey={Survey.Upward} title="Upward Surveys" />
        <Tab eventKey={Survey._360Degree} title="360 Surveys" />
        <Tab eventKey={Survey.Engagement} title="Engagement Surveys" />
      </Tabs>
    </Div>
  );

  const getSearchInput = () => {
    return (
      <FormControl.Search
        placeholder="Search for surveys"
        value={temp}
        onChange={(evt: any) => setTemp(evt.target.value)}
        onSearch={onSearchChange}
      />
    );
  };

  const renderCellLoading = () => {
    return <Div><img width="15" height="15" src={loading} /></Div>
  }

  return (
    <PageContainer>
      <CustomHeader
        title={
          <Div>
            <Text.H1 className="pb-2">Reports</Text.H1>
            <Div
              className="sticky-tabs-container"
              style={{ position: "absolute" }}
            >
              {getTabsTemplate()}
            </Div>
          </Div>
        }
        metaItem={getSearchInput()}
        style={{ padding: "25px 30px 30px" }}
      />
      <Layout.Container fluid className="pt-5">
        {/* <Div>{getTabsTemplate()}</Div> */}
        <Div className="pt-4">
          <Table
            isLoading={surveyData.isLoading}
            columns={getColumns()}
            rows={surveyData.surveys}
            onRowClick={(item: any) => {
              history.push(
                filter === Survey._360Degree
                  ? appUrls.admin.reports.getSurvey360ReportsUrl(item.id)
                  : appUrls.admin.reports.getSurveyReportsUrl(item.id)
              );
            }}
            render={(item: any, index: number) => {
              return (
                <>
                  <Table.Data>
                    {/* <Text.P1>{index + 1}</Text.P1> */}
                    <Text.P1>
                      {tableMeta.size * (tableMeta.page - 1) + index + 1}.
                    </Text.P1>
                  </Table.Data>
                  <Table.Data>
                    <Text.P1 className="text-black">{item.title}</Text.P1>
                  </Table.Data>
                  {filter !== Survey._360Degree && (
                    <>
                      <Table.Data>
                        <Text.P1>
                          {item.assesmentStart
                            ? util.date.getBrowserTime(item.assesmentStart, " LLL dd, yyyy, HH:mm")
                            : "-"}
                        </Text.P1>
                      </Table.Data>
                      <Table.Data>
                        <Text.P1>
                          {item.assesmentEnd
                            ? util.date.getBrowserTime(item.assesmentEnd, " LLL dd, yyyy, HH:mm")
                            : "-"}
                        </Text.P1>
                      </Table.Data>
                    </>
                  )}
                  {filter === Survey.Engagement ? (
                    <Table.Data>
                      <Text.P1>{item.admin_stats?.participated ?? renderCellLoading()}</Text.P1>
                    </Table.Data>
                  ) : (
                    <>
                      <Table.Data>
                        <Text.P1>{item.reportStats.totalEligible ?? renderCellLoading()}</Text.P1>
                      </Table.Data>
                      <Table.Data>
                        <Text.P1>{item.reportStats.totalReports ?? renderCellLoading()}</Text.P1>
                      </Table.Data>
                    </>
                  )}
                </>
              );
            }}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.sortKey,
                item.sortValue
              );
              setColumnsData((_columns: any) => {
                return tableMeta.resetColumns(_columns, item);
              });
              setOrdering((s: any) => ({
                ...s,
                [filter === Survey._360Degree ? "360" : "other"]: label,
              }));
              getSurveys({ ordering: label });
            }}
            onPageSelect={(number: number) => {
              tableMeta.onPageSelect(number);
              getSurveys({ page: number });
            }}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            size={tableMeta.size}
            totalItems={tableMeta.totalItems}
            {...(params.search && {
              notFoundMsg: util.noSearchRecordsFoundMsg,
            })}
          />
        </Div>
      </Layout.Container>
    </PageContainer>
  );
};

export default SurveyList;

const commonCols = {
  sNo: { label: "No." },
  title: { label: "Title", hasSort: true, sortValue: "asc", sortKey: "title" },
};

const condCols = {
  startDate: {
    label: "Assessment Start Date",
    hasSort: true,
    sortValue: "dsc",
    sortKey: "assessment_period_start_date",
  },
  endDate: {
    label: "Assessment End Date",
    hasSort: true,
    sortValue: "dsc",
    sortKey: "assessment_period_end_date",
  },
};

const reportCols = {
  totalEligible: {
    label: "Total Eligible",
    hasSort: false,
    sortValue: "",
    sortKey: "total_eligible",
  },
  totalReports: {
    label: "Total Reports",
    hasSort: false,
    sortValue: "",
    sortKey: "total_reports",
  },
};

const getRspColumns = (surveyType: any) =>
  ({
    [Survey.Upward as any]: {
      ...commonCols,
      ...condCols,
      ...reportCols,
    },
    [Survey._360Degree as any]: {
      ...commonCols,
      ...reportCols,
    },
    [Survey.Engagement as any]: {
      ...commonCols,
      ...condCols,
      totalParticipated: { label: "Total Participated" },
    },
    [Survey.Self as any]: {
      ...commonCols,
      ...condCols,
      ...reportCols,
    },
  }[surveyType] || {});
