import CreateQuestions from "./CreateQuestions/CreateQuestions";
import CreateSurveyProperties from "./CreateSurveyProperties/CreateSurveyProperties";
import SendSurvey from "./SendSurvey/SendSurvey";
import appUrls from "unmatched/utils/urls/app-urls";
import PreviewSurvey from "./PreviewSurvey/PreviewSurvey";
import AddParticipants from "./AddParticipants/AddParticipants";
import FAQ from "./FAQs/FAQs";
import Rules from "./Rules/Rules";
// import AddUsers from "./AddUsers/AddUsers";
// import AddPairings from "./AddPairings/AddPairings";

const routes = [
  {
    name: "Survey Builder - Questionnaire",
    path: appUrls.admin.survey.create.getQuestionsUrl(":id", ":versionId"),
    isExact: false,
    isPrivate: true,
    component: CreateQuestions,
  },
  {
    name: "Survey Builder",
    path: appUrls.admin.survey.create.getUpwardReviewUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: CreateSurveyProperties,
  },
  {
    name: "Survey Builder - Participants",
    path: appUrls.admin.survey.create.getParticipantsUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: AddParticipants,
  },
  {
    name: "Survey Builder - Participants",
    path: appUrls.admin.survey.create.getRulesUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: Rules,
  },
  {
    name: "Survey Builder - Send Survey",
    path: appUrls.admin.survey.create.getPublishUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: SendSurvey,
  },
  {
    name: "Survey Builder - Preview",
    path: appUrls.admin.survey.create.getPreviewUrl(":id", ":versionId"),
    isExact: false,
    isPrivate: true,
    component: PreviewSurvey,
  },
  {
    name: "Survey Builder - FAQs",
    path: appUrls.admin.survey.create.getFAQUrl(":id"),
    isExact: false,
    isPrivate: true,
    component: FAQ,
  },
];

export default routes;
