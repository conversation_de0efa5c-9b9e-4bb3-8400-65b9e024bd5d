import React from "react";
import { Redirect, Route } from "react-router-dom";
import appUrls from "unmatched/utils/urls/app-urls";
import DASHBOARD_ROUTES from "./dashboard-routes";
import AppRoutes from "../../AppRoutes";

export default function Dashboard() {
  return (
    <AppRoutes routes={DASHBOARD_ROUTES}>
      <Route exact path={appUrls.user.dashboard.default}>
        <Redirect to={appUrls.user.dashboard.surveyList} />
      </Route>
    </AppRoutes>
  );
}
