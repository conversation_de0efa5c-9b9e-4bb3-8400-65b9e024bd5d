import axios, { AxiosResponse } from "axios";
// import { QUESTION } from "unmatched/utils/enums";
import util from "unmatched/utils";
const { QUESTION } = util.enums;

const getQuestions = (_questions: any, responses: any) => {
  return (_questions || [])
    .filter((item: any) => item.resourcetype !== "QuestionNumber")
    .map((item: any) => {
      // const responses = responses || [];
      // const comments = responsesData.data.comment_responses || [];

      // const responses: any = [];
      const response: any =
        responses.find((res: any) => res.question === item.id) || {};
      // const comment: any = comments.find(
      //   (res: any) => res.question === item.id
      // ) || { id: 0, value: "" };

      let output: any = {
        id: item.id,
        responseId: 0,
        categoryID: item.section,
        title: item.label,
        type: item.resourcetype,
        isMandatory: item.mandatory,
        hasFeedback: item.collect_feedback,
        hasSwap: item.is_reverse_scale,
        radio: {
          selected: "",
          options: item.options || [],
        },
        checkbox: {
          selected: [],
          options: item.options || [],
        },
        rating: {
          selected: "",
          tags: item.options || {},
        },
        isDemographic: true,
        // commentId: response.id || 0,
        // feedback: response.value,
      };
      if (response && response.id) {
        const { id, value, is_valid } = response;
        output = {
          ...output,
          responseId: id,
          isValid: is_valid,
          radio: {
            selected: value,
            options: item.options || [],
          },
          checkbox: {
            selected: value || [],
            options: item.options || [],
          },
          rating: {
            selected: value,
            tags: item.options || {},
          },
        };
        if (item.resourcetype === QUESTION.Input) {
          output = {
            ...output,
            commentId: response.id || 0,
            feedback: response.value,
          };
        }
      }
      return output;
    });
};

