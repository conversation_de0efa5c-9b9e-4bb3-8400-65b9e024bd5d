// Node modules
import React, { useEffect, useState } from "react";
// Helpers
import SURVEY_ROUTES from "./create-survey-routes";
import useCustomLayout from "../../Shared/custom-layout-hook";
// Components
import Sidebar from "./Sidebar";
import AppRoutes from "../../../AppRoutes";
import Provider, { useCreateSurveyContext } from "./Provider";
import { Layout, PageContainer, Div, Image, Nav, Dropdown, DeleteConfirmation, Icon } from "unmatched/components";
import { useParams, useHistory } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import {
  getSurveyVersionsFact,
  cloneSurveyVersionFact,
  getBuilderEmailTemplateFact,
  getSurveyByIdFactV2,
  deleteSurveyVersionFact,
  // onValidateSurveyFact,
} from "../survey-api";
import util from "unmatched/utils";
import SurveyVersions from "./SurveyVersions";
import useSession from "unmatched/modules/session/hook";
import ValidateSurvey from "./ValidateSurvey/ValidateSurvey";
import DemographicsSidebar from "./DemographicsSidebar/DemoGraphicsSidebar";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import { UMTab } from "unmatched/components/Tabs";
import { OptionButton } from "./AddParticipants/PairingsManagement/FilesList/FilesList";

const { Paste } = icons;

const getCurrentState = (
  state: string,
  surveyId: number,
  versionId: number
) => {
  const { get } = util.lib;
  const { SurveyStatus } = util.enums;
  const {
    getQuestionsUrl,
    getParticipantsUrl,
    getPreviewUrl,
    getPublishUrl,
    getUpwardReviewUrl,
  } = util.appUrls.admin.survey.create;
  const data = {
    [SurveyStatus.Properties]: getUpwardReviewUrl(surveyId),
    [SurveyStatus.Questionnaire]: getQuestionsUrl(surveyId, versionId),
    [SurveyStatus.AddPairings]: getParticipantsUrl(surveyId),
    [SurveyStatus.Preview]: getPreviewUrl(surveyId, versionId),
    [SurveyStatus.Send]: getPublishUrl(surveyId),
  };
  return get(data, state) || getUpwardReviewUrl(surveyId);
};

const CreateSurvey = () => {
  const layout = useCustomLayout();
  const [canValidate, setCanValidate] = React.useState<boolean>(false);
  const [deleteVersion, setDeleteVersion] = useState<number | null>(null);
  const params: any = useParams();
  const {
    survey,
    versionsData,
    activeVersionId,
    // validateForms,
    setCompleted,
    questionsData,
    sectionsData,
    versionId,
    email,
    setDemographics,
  } = useCreateSurveyContext();
  const toastr = useToastr();
  const history = useHistory();
  const { client } = useSession();
  const [viewOnly, setViewOnly] = useState<boolean>(false);
  const [sendInvite, setSendInvite] = React.useState(true);


  useEffect(() => {
    if (viewOnly) disableInputs();
  });

  const disableInputs = () => {
    const surveyContainer =
      document
        .querySelector("#create-survey-container")
        ?.getElementsByTagName("*") || [];
    const pagination =
      document.querySelector("#pagination")?.getElementsByTagName("*") || [];

    util.enableDisableTree(surveyContainer, true);
    util.enableDisableTree(pagination, false);
  };

  const getSurvey = (id: number | string) => {
    survey.setLoading(true);
    getSurveyByIdFactV2(id).then(
      (response: any) => {
        survey.onSuccess(response);
        const { builderStatus, id } = response;
        setViewOnly(!response.isDraft);
        getVersions();
        if (builderStatus) {
          setCompleted(builderStatus);
          const currentState = getCurrentState(
            builderStatus,
            id,
            activeVersionId
          );
          if (
            history.location.pathname.includes("/questions") &&
            history.location.pathname !== currentState
          ) {
            return;
          }
          history.push(currentState);
        }
      },
      (err: any) => {
        survey.onError(err);
        toastr.onError(err);
      }
    );
  };

  const getVersions = () => {
    versionsData.setLoading(true);
    getSurveyVersionsFact(params.id).then(
      (response: any) => {
        versionsData.onSuccess(response);
      },
      (err: any) => {
        versionsData.onError(err);
      }
    );
  };

  const navigateVersion = (id: any, link?: string) => {
    const { getQuestionsUrl, getPreviewUrl } = util.appUrls.admin.survey.create;
    history.push(
      link !== "preview"
        ? getQuestionsUrl(survey.data.id, id)
        : getPreviewUrl(survey.data.id, id)
    );
  };

  const onTabSelect = ({ id }: any, link?: string) => {
    if (id === versionId) return;
    if (!sectionsData.data.length) {
      toastr.errorToast("Atleast one section is required");
    } else if (!questionsData.data.length) {
      toastr.errorToast("Atleast one question is required");
    } else {
      navigateVersion(id, link);
    }
  };

  const onVersionClone = ({ id }: any) => {
    survey.setSaving(true);
    cloneSurveyVersionFact(id).then((response: any) => {
      survey.setSaving(false);
      versionsData.setData([...versionsData.data, response]);
      onTabSelect(response);
    });
  };

  
  const onVersionDelete = (_versions: any) => {
    const [item] = _versions || [];
    versionsData.setData(_versions);
    navigateVersion(item && item.id ? item.id : 0);
  };

  const onVersionDeleteNew = () => {
    
    deleteSurveyVersionFact(deleteVersion).then(() => {
      const versions = versionsData.data;
      const updated = versions.filter((item: any) => item.id !== deleteVersion);
      const [item] = versions || [];
      versionsData.setData(updated);
      navigateVersion(item && item.id ? item.id : 0);
      setDeleteVersion(null);
    });

    // const [item] = _versions || [];
    // versionsData.setData(_versions);
    // navigateVersion(item && item.id ? item.id : 0);
  };

  const getVersionsTemplate = (link: string) => {
    if (survey.data.type === util.enums.Survey.Upward) {
      // const versions = versionsData.data;
      return null;
      // return (
      //   <SurveyVersions
      //     versions={versions}
      //     canEdit={link === "editSurvey"}
      //     onTabSelect={(item: any) => onTabSelect(item, link)}
      //     onDelete={onVersionDelete}
      //     onClone={onVersionClone}
      //     activeKey={activeVersionId}
      //   />
      // );
    } else if (
      survey.data.type === util.enums.Survey.Engagement &&
      survey.data.hasDemographics
    ) {
      return <DemographicsSidebar />;
    }
    return null;
  };

  const getVersionsTemplateTwo = (onAddNewVersion: string) => {
    if (survey.data.type === util.enums.Survey.Upward) {
      const versions = versionsData.data;
      return (
        <Nav className="nav-tabs sticky versions-list">
          {versions.map((version: any) => {
            return (
              <UMTab
                key={version.id}
                styles={{ padding: "0px 20px 0 10px" }}
                eventKey={version.id}
                activeKey={
                  versions.find((version: any) => {
                    return version.id === activeVersionId;
                  })?.id
                }
                onClick={() => onTabSelect(version, "editSurvey")}
              >
                <div style={{ display: "flex", flexDirection: "row-reverse" }}>
                  <div>{version.label}</div>
                  {version.id === activeVersionId && <Dropdown
                    style={{ position: "absolute", marginRight: "-21px" }}
                  >
                    <OptionButton className="version-options">...</OptionButton>

                    <Dropdown.Menu
                      alignRight
                      className="text-right shadow-lg vo-dd"
                      style={{ zIndex: 1 }}
                    >
                      <Dropdown.Item href="#" onClick={() => onVersionClone(version)}>
                      Clone version 
                      </Dropdown.Item>
                      <Dropdown.Item href="#" className="danger-text" onClick={() => setDeleteVersion(version.id)}>
                      Delete version 
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>}
                </div>
              </UMTab>
            );
          })}
          <UMTab
            key={"add"}
            eventKey={"add"}
            onClick={onAddNewVersion}
          >
            <Div style={{ color: '#518cff' }}>+Add Version</Div>
          </UMTab>
        </Nav>
      );
    }
    return null;
  };

  const onRouteChange = (route?: string) => {
    history.push(route || "");
    setDemographics(false);
    // validateForms().then(
    //   () => {
    //     history.push(route || "");
    //     setDemographics(false);
    //   },
    //   (err: any) => {
    //     toastr.showToast({
    //       show: true,
    //       title:
    //         err && err.message
    //           ? err.message
    //           : "Please check the forms before navigating",
    //     });
    //   }
    // );
  };

  const getEmailData = () => {
    getBuilderEmailTemplateFact(params.id).then((response) => {
      email.onSuccess(response);
    });
  };

  const onSubmit = () => {
    setCanValidate(true);
  };

  React.useEffect(() => {
    getSurvey(params.id);
    getEmailData();
  }, []);

  if (survey.isLoading || versionsData.isLoading) {
    return (
      <Div
        className="d-flex align-items-stretch justify-content-center"
        style={{ height: "100vh" }}
      >
        <Image
          src={client.loader}
          style={{
            width: "100px",
            height: "auto",
          }}
        />
      </Div>
    );
  }

  return (
    <>
      <Layout.Sidebar
        className="dark-sidebar"
        hasHeader={false}
        style={{ marginLeft: layout.sidebar.marginLeft, zIndex: 1050 }}
        width={layout.sidebar.width}
      >
        <Sidebar
          getVersions={getVersionsTemplate}
          onRouteChange={onRouteChange}
          onSubmit={onSubmit}
          viewOnly={viewOnly}
        />
      </Layout.Sidebar>
      <PageContainer
        style={{ marginLeft: layout.container.marginLeft }}
        id="create-survey-container"
      >
        <AppRoutes
          breadcrumbs={[
            {
              label: "Surveys",
              icon: <Paste className="grey-icon__svg" />,
              route: appUrls.admin.survey.default,
            },
            { label: viewOnly ? "View Survey" : "Edit Survey" },
            { label: survey.data.name || "Untitled" },
          ]}
          viewOnly={viewOnly}
          routes={SURVEY_ROUTES}
          setSendInvite={setSendInvite}
          sendInvite={sendInvite}
          getVersions={getVersionsTemplateTwo}
        />
        <ValidateSurvey
          indexId={survey.data.id}
          show={canValidate}
          onHide={() => setCanValidate(false)}
          sendInvite={sendInvite}
        />
        <DeleteConfirmation
          label="version"
          show={!!deleteVersion}
          onCancel={() => setDeleteVersion(null)}
          onDelete={onVersionDeleteNew}
        />
      </PageContainer>
    </>
  );
};

const Wrapper = (props: any) => {
  return (
    <Provider>
      <CreateSurvey {...props} />
    </Provider>
  );
};

export default Wrapper;
