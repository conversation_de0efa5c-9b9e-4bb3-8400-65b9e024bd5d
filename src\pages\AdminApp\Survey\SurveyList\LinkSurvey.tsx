import React from "react";
import {
  Button,
  Div,
  Text,
  Layout,
  FormGroup,
  FormControl,
  Table,
} from "unmatched/components";
import { useDebounce, useTable, useXHR } from "unmatched/hooks";
import useToastr from "unmatched/modules/toastr/hook";
import { getDraftSurveyNamesFact, linkSelfSurveyFact } from "../survey-api";
// import PropTypes from 'prop-types'

const LinkSurvey = (props: any) => {
  const [canLink, setCanLink] = React.useState(false);

  const [search, setSearch] = React.useState("");
  const [isTouched, setIsTouched] = React.useState(false);
  const surveys = useXHR({ defaultResponse: [] });
  const [selected, setSelected] = React.useState("");
  const toastr = useToastr();
  const tableMeta = useTable({});

  const onSearchChange = (val: string) => {
    setSearch(val);
    if (isTouched) {
    } else {
      setIsTouched(true);
    }
  };

  const getSurveys = (page?: number) => {
    surveys.setLoading(true);
    getDraftSurveyNamesFact({
      page: page ?? 1,
      resource_type: "SurveyIndexUpward,SurveyIndex360",
      search: search.length > 0 ? search : undefined,
      include_aux: "False",
      page_size: 10,
    }).then(
      ({ data, totalPages }: any) => {
        surveys.onSuccess(data);
        tableMeta.updatePagination({
          totalPages,
        });
      },
      (err: any) => {
        surveys.onError(err);
        toastr.onError(err);
      }
    );
  };

  useDebounce(
    () => {
      if (isTouched) {
        getSurveys();
        tableMeta.updatePagination({
          page: 1,
        });
      } else {
        return;
      }
    },
    500,
    [search]
  );

  const createSurvey = () => {
    props.onCreateSelfEvaluation(1);
  };

  const onSelfLink = async () => {
    try {
      await linkSelfSurveyFact({ selfID: props.surveyID, id: selected });
      props.onCreateSelfEvaluation(props.surveyID);
    } catch (err: any) {
      surveys.onError(err);
      toastr.onError(err);
    }
  };
  const onSearch = () => "";

  const onConfirm = () => {
    setCanLink(true);
    getSurveys();
  };

  const getConfirmationTemplate = () => {
    return (
      <Div className="p-4">
        <Text.P1 className="pb-4">
          Do you wish to link the self evaluation survey as an extension to an
          upward review or 360 degree feedback review?
        </Text.P1>
        <Button
          variant="outline-primary"
          onClick={() => props.onCreateSelfEvaluation(props.surveyID)}
        >
          No, Proceed as stand alone survey
        </Button>
        <Button className="ml-2" variant="primary" onClick={onConfirm}>
          Yes
        </Button>
      </Div>
    );
  };

  const getSurveysTemplate = () => {
    const getColumns = () => {
      return [
        { key: 1, label: "", hasSort: false },
        { key: 2, label: "#", hasSort: false },
        { key: 3, label: "Survey", hasSort: false },
      ];
    };
    const getRows = () => {
      return surveys.data.map((item: any, index: number) => {
        const isEven = index % 2 === 0 || index === 0;
        return (
          <Table.Row
            even={!isEven}
            key={item.key}
            selected={selected === item.id}
          >
            <Table.Data width="30px">
              <FormGroup key={item.key}>
                <FormControl.Radio>
                  <FormControl.Radio.Label>
                    {/* {item.title} */}
                  </FormControl.Radio.Label>
                  <FormControl.Radio.Input
                    checked={selected === item.id}
                    onChange={(evt: any) =>
                      evt.target.value ? setSelected(item.id) : ""
                    }
                  />
                </FormControl.Radio>
              </FormGroup>
            </Table.Data>
            <Table.Data width="45px">
              <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
            </Table.Data>
            <Table.Data>
              <Text.P1>{item.title}</Text.P1>
            </Table.Data>
          </Table.Row>
        );
      });
    };

    // return ;
    return (
      <>
        {/* {surveys.data.map((item: any) => {
          return (
            <FormGroup key={item.key}>
              <FormControl.Radio>
                <FormControl.Radio.Label>{item.title}</FormControl.Radio.Label>
                <FormControl.Radio.Input
                  checked={selected === item.id}
                  onChange={(evt: any) =>
                    evt.target.value ? setSelected(item.id) : ""
                  }
                />
              </FormControl.Radio>
            </FormGroup>
          );
        })} */}
        <Table
          columns={getColumns()}
          isLoading={surveys.isLoading}
          rows={surveys.data}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={(d: any) => {
            tableMeta.updatePagination({ page: d });
            getSurveys(d);
          }}
        />
      </>
    );
    // if (surveys.isLoading) {
    //   return <Loader text="Loading Surveys..." />;
    // }
    // return surveys.data.map((item: any) => {
    //   return (
    //     <FormGroup key={item.key}>
    //       <FormControl.Radio>
    //         <FormControl.Radio.Label>{item.title}</FormControl.Radio.Label>
    //         <FormControl.Radio.Input
    //           checked={selected === item.id}
    //           onChange={(evt: any) =>
    //             evt.target.value ? setSelected(item.id) : ""
    //           }
    //         />
    //       </FormControl.Radio>
    //     </FormGroup>
    //   );
    // });
  };

  const getFooterTemplate = () => {
    return (
      <Div className="text-right">
        <Button className="ml-2" variant="primary" onClick={onSelfLink}>
          Continue
        </Button>
      </Div>
    );
  };

  const getLinkingTemplate = () => {
    return (
      <Div className="p-4">
        <Text.P1 className="pb-4">
          Choose from the list of draft upward review surveys to link with. Or
          you can
          <Button
            className="text-underline px-2 fs-14 fw-400 py-0"
            onClick={createSurvey}
            variant="link"
          >
            Create a stand alone
          </Button>
        </Text.P1>
        <Layout.Flex>
          <FormGroup>
            <FormGroup.Label className="fs-12">Draft Surveys</FormGroup.Label>
            <FormControl.Search
              placeholder="Search for surveys"
              value={search}
              onSearch={onSearch}
              size="sm"
              onChange={(evt: any) => onSearchChange(evt.target.value)}
            />
          </FormGroup>
        </Layout.Flex>
        <Div>{getSurveysTemplate()}</Div>
        {getFooterTemplate()}
      </Div>
    );
  };

  return canLink ? getLinkingTemplate() : getConfirmationTemplate();
};

// LinkSurvey.propTypes = {

// }

export default LinkSurvey;
