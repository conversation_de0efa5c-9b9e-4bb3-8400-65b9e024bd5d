import React from "react";
import { Div, <PERSON><PERSON>ontainer, <PERSON>, Button } from "unmatched/components";
import images from "../../../assets/images/images";

const { ReportsLoader } = images;

const ReportsNoDataFound = (props: any) => {
  const { generateReport, loaders } = props;
  return (
    <PageContainer
      className="justify-content-center align-items-center d-flex"
      noHeader
    >
      <Div className="align-self-center text-center justify-content-center">
        <ReportsLoader width="200px" height="179.5px" />
        <Text.H2 className="pt-4">Reports are being Generated</Text.H2>
        <Text.P1
          className="w-75 ml-auto mr-auto py-4"
          style={{ minWidth: "100%" }}
        >
          Reports are being generated. Please check back at a later time.
          <br />
          Approximate time for generation : <b>1 day</b> from the Survey end
          date.
        </Text.P1>
        <Button
          className="btn btn-primary"
          style={{ fontSize: 14, width: 145 }}
          onClick={generateReport}
          disabled={loaders.generate}
        >
          {loaders.generate ? <Loader /> : "Generate Report"}
        </Button>
      </Div>
    </PageContainer>
  );
};
const Loader = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 38 38"
      xmlns="http://www.w3.org/2000/svg"
      stroke="#fff"
    >
      <g fill="none" fill-rule="evenodd">
        <g transform="translate(1 1)" stroke-width="2">
          <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
          <path d="M36 18c0-9.94-8.06-18-18-18">
            <animateTransform
              attributeName="transform"
              type="rotate"
              from="0 18 18"
              to="360 18 18"
              dur="1s"
              repeatCount="indefinite"
            />
          </path>
        </g>
      </g>
    </svg>
  );
};

export default ReportsNoDataFound;
