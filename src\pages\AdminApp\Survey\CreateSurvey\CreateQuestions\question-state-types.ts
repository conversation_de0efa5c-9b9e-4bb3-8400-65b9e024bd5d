import { QuestionType } from "../../survey-types";

export interface DynamicSection {
  [key: string]: {
    id: number;
    title: string;
    questions: Array<number>;
  };
}

export interface DynamicQuestion {
  [key: string]: {
    id: number;
    isValid: boolean;
    sectionId: number;
    question: string;
    markAsMandatory: boolean;
    collectTextFeedback: boolean;
    type: QuestionType;
    rankingId: number;
    radioId: number;
    checkboxId: number;
    inputId: number;
    dropdownId: number;
  };
}
export interface SectionsInterface {
  list: Array<number>;
  content: DynamicSection;
}

export interface QuestionsInterface {
  list: Array<number>;
  content: DynamicQuestion;
}

export interface OptionsInterface {
  list: Array<number>;
  content: {
    [key: string]: {
      id: number;
      questionId: number;
      type: string;
      order: number;
    };
  };
}

export interface RankingInterface {
  list: Array<number>;
  content: {
    [key: string]: {
      id: number;
      questionId: number;
      scale: number;
      options: Array<number>;
    };
  };
  options: OptionsInterface;
}

export interface DropdownInterface {
  list: Array<number>;
  content: {
    [key: string]: {
      id: number;
      questionId: number;
      options: Array<number>;
    };
  };
  options: OptionsInterface;
}

export interface RadioInterface {
  list: Array<number>;
  content: {
    [key: string]: {
      id: number;
      questionId: number;
      options: Array<number>;
    };
  };
  options: OptionsInterface;
}

export interface CheckboxInterface {
  list: Array<number>;
  content: {
    [key: string]: {
      id: number;
      questionId: number;
      options: Array<number>;
    };
  };
  options: OptionsInterface;
}

export interface InputInterface {
  list: Array<number>;
  content: {
    [key: string]: {
      id: number;
      questionId: number;
    };
  };
}
