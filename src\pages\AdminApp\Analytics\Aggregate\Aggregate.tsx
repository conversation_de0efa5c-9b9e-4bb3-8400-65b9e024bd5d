import React from "react";
// import _ from "lodash";
import { Link } from "react-router-dom";
import {
  Div,
  Text,
  Layout,
  Tabs,
  Tab,
  PageContainer,
} from "unmatched/components";
import { useState, useQuery, useHistory } from "unmatched/hooks";
import appUrls from "unmatched/utils/urls/app-urls";
import Header from "../../Shared/CustomHeader/CustomHeader";
import Overall from "./Overall/Overall";
import Category from "./Category/Category";
import Item from "./Item/Item";
import Rankinglist from "./RankingList/RankingList";
import icons from "assets/icons/icons";
import styled from "styled-components";

const { Graph } = icons;

const TYPES = {
  OVERALL: "overall",
  CATEGORY: "catgory",
  ITEM: "item",
  RANKING: "ranking",
};

const Fixed = styled(Div)`
  .nav-tabs {
    position: fixed;
    background: #fff;
    z-index: 1 !important;
    width: 100%;
    border-bottom: 1px solid rgb(222, 226, 230);
    padding-left: 90px;
    left: 0;
  }
`;

const Aggregate = () => {
  const history = useHistory();

  const queryParams = useQuery();

  const [params, setParams] = useState({
    filter: queryParams.get("filter") || TYPES.OVERALL,
    sort: [],
    size: 0,
    page: 1,
    totalElements: 20,
    totalPages: 4,
  });

  const onFilterChange = (filter: string) => {
    history.push(appUrls.admin.analytics.getAggregateUrl(filter));
    setParams((_params: any) => ({
      ..._params,
      filter,
    }));
  };

  // const onPageChange = (page: number) => {
  //   setParams((_params: any) => ({
  //     ..._params,
  //     page,
  //   }));
  // };

  const getFilterLink = (key: string, title: string) => {
    return (
      <Link
        className={params.filter === key ? "text-primary" : ""}
        to={appUrls.admin.analytics.getAggregateUrl(key)}
      >
        {title}
      </Link>
    );
  };

  const getTabsTemplate = () => {
    return (
      <Fixed className="custom-tabs-2 new-tab-ui">
        <Tabs
          activeKey={params.filter}
          onSelect={(k: any) => onFilterChange(k)}
        >
          <Tab
            eventKey={TYPES.OVERALL}
            title={getFilterLink(TYPES.OVERALL, "Overall Analytics")}
          >
            {params.filter === TYPES.OVERALL && (
              <Div className="pt-4">
                <Overall />
              </Div>
            )}
          </Tab>
          <Tab
            eventKey={TYPES.CATEGORY}
            title={getFilterLink(TYPES.CATEGORY, "Category-wise Analytics")}
          >
            {params.filter === TYPES.CATEGORY && (
              <Div className="pt-5">
                <Category />
              </Div>
            )}
          </Tab>
          <Tab
            eventKey={TYPES.ITEM}
            title={getFilterLink(TYPES.ITEM, "Item-wise Analytics")}
          >
            {params.filter === TYPES.ITEM && (
              <Div style={{ paddingTop: 25 }}>
                <Item />
              </Div>
            )}
          </Tab>
          <Tab
            eventKey={TYPES.RANKING}
            title={getFilterLink(TYPES.RANKING, "Ranking List")}
          >
            {params.filter === TYPES.RANKING && (
              <Div className="pt-4">
                <Rankinglist />
              </Div>
            )}
          </Tab>
        </Tabs>
      </Fixed>
    );
  };

  return (
    <PageContainer>
      <Header
        title={<Text.H1 className="pb-2">Aggregate Analytics</Text.H1>}
        breadcrumbs={[
          {
            label: "Analytics",
            icon: <Graph className="grey-icon__svg" />,
          },
          { label: "Aggregate Analytics" },
        ]}
        noShadow
        style={{ borderBottom: '0px !important' }}
      />
      <Layout.Container fluid className="pt-3">
        <Div>{getTabsTemplate()}</Div>
      </Layout.Container>
    </PageContainer>
  );
};

export default Aggregate;
