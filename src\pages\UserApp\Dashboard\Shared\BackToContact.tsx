import React from "react";
import { <PERSON> } from "react-router-dom";
import appUrls from "unmatched/utils/urls/app-urls";
import { Div } from "unmatched/components";

export default function BackToContact({ id }: any) {
  return (
    <Div className="pl-3 py-2">
      <Link
        to={appUrls.user.dashboard.upwardReview.getUrl(id)}
        className="fs-14"
      >
        Survey List
      </Link>
    </Div>
  );
}
