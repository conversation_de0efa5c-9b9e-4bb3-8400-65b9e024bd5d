import React from "react";
import { Route, Redirect, useParams } from "react-router-dom";
import appUrls from "unmatched/utils/urls/app-urls";
import AppRoutes from "../../../AppRoutes";
import UPWARD_ROUTES from "./upward-review-routes";

interface ParamTypes {
  id?: string;
}

export default function UpwardReview() {
  const { id } = useParams<ParamTypes>();

  return (
    <AppRoutes routes={UPWARD_ROUTES}>
      <Route exact path={appUrls.user.dashboard.upwardReview.getUrl(id || "")}>
        <Redirect
          to={appUrls.user.dashboard.upwardReview.getPairingsUrl(id || "")}
        />
      </Route>
    </AppRoutes>
  );
}
