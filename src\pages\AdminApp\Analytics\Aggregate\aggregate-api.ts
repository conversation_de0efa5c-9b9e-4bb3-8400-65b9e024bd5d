import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";
import api from "unmatched/utils/api";

// const getMetaAvgValues = (avgs: any, labels: any) => {
//   let output = {};
//   labels.forEach((item: any) => {
//     output = {
//       ...output,
//       [`${item.field}`]: avgs[item.field] || "-",
//     };
//   });
//   return output;
// };

const getCategories = (categories: any) => {
  // const { standalone, linked } = self;
  const output = categories.map((item: any) => ({
    key: item.id,
    label: item.name,
  }));
  return output;
};

export const getOverallGraphDataFact = (userId: any, data: any, type: any) => {
  const config = api.getConfigurations(
    {
      resource_type: type,
    },
    {}
  );
  return axios
    .post(`/analytics/aggregate/graph/overall/`, data, config)
    .then((response: AxiosResponse) => {
      const { data, rating_legends, rater_groups, default_filters } =
        response.data;
      const [item] = data;
      const labels = util.lib.keys(item?.data || {});
      if (labels.length > 9) {
        throw new Error("You cannot select more that 9 filters");
      } else {
        return {
          graph: {
            data: data.map((o: any, index: number) => {
              return {
                ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
                year: o.year,
                id: index + 1,
              };
            }),
            labels,
          },
          defaultFilters: default_filters || {},
          groups: rater_groups || ["Overall"],
          legends: util.lib
            .entries(rating_legends)
            .map(([key, value]: any) => ({ id: key, title: value })),
        };
      }
    });
};

export const getCategoryGraphDataFact = (params: any, data: any) => {
  const config = api.getConfigurations(
    {
      resource_type: params.type,
    },
    {}
  );
  return axios
    .post(
      `/analytics/aggregate/graph/category/${params.categoryId}/`,
      data,
      config
    )
    .then(({ data }: AxiosResponse) => {
      const [item] = data.data;
      return {
        data: data.data.map((o: any, index: number) => {
          return {
            ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
            year: o.year,
            id: index + 1,
          };
        }),
        defaultFilters: data?.default_filters,
        labels: util.lib.keys(item?.data || {}),
      };
    });
};

export const getItemizedGraphDataFact = (
  type: any,
  questionId: string,
  data: any
) => {
  const config = api.getConfigurations(
    {
      resource_type: type,
    },
    {}
  );
  return axios
    .post(`/analytics/aggregate/graph/item/${questionId}/`, data, config)
    .then(({ data }: AxiosResponse) => {
      const [item] = data.data;
      return {
        data: data.data.map((o: any, index: number) => {
          return {
            ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
            year: o.year,
            id: index + 1,
          };
        }),
        labels: util.lib.keys(item?.data || {}),
        defaultFilters: data?.default_filters,
      };
    });
  // .then(({ data }: AxiosResponse) => {
  //   const [item] = data;
  //   return {
  //     data: data.map((o: any, index: number) => {
  //       return {
  //         ...util.lib.mapValues(o.data, (val: any) => Number(val) || 0),
  //         year: o.year,
  //         id: index + 1,
  //       };
  //     }),
  //     labels: util.lib.keys(item?.data || {}),
  //   };
  // });
};

export const getOverallTimeseriesTableFact = (meta: any, type: any) => {
  const config = api.getConfigurations(
    {
      resource_type: type,
      metadata: meta,
    },
    {}
  );
  return axios
    .get(`/analytics/aggregate/overall/table/`, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      return {
        data: data.results.map((item: any) => {
          // const id =
          //   item.year && item.target
          //     ? `${item?.target.id}-${item.year}`
          //     : index;
          const id = item.survey_id;
          return {
            id,
            year: item.year,
            name: item.survey_title,
            firmAverage: item.firm_average,
            ...item.avgs,
          };
        }),
      };
    });
};

export const getCategoryHeatMapFact = (filters?: any) => {
  const config = api.getConfigurations({}, {});
  const payload = {
    resource_type: filters?.types[0],
    survey_id: filters?.surveys[0],
    year: filters?.years[0],
    metadata: filters?.meta,
  };
  return axios
    .post(`/analytics/aggregate/category/table/`, payload, config)
    .then(({ data }: AxiosResponse) => {
      return {
        selectedYear: data.selected_year,
        selectedSurvey: data.selected_survey,
        data: data.data.map((item: any) => {
          const send = {
            id: item.category_id,
            name: item.name,
            firmAverage: item.firm_avg || "0",
            ...item.avgs,
          };
          return send;
        }),
      };
    });
};

export const getItemizedHeatMapFact = (filters?: any) => {
  const config = api.getConfigurations({}, {});
  const payload = {
    resource_type: filters?.types[0],
    survey_id: filters?.surveys[0],
    year: filters?.years[0],
    metadata: filters?.meta,
    question_ids: filters?.questions || [],
  };
  return axios
    .post(`/analytics/aggregate/item/table/`, payload, config)
    .then(({ data }: AxiosResponse) => {
      return {
        selectedYear: data.selected_year,
        selectedSurvey: data.selected_survey,
        data: data.data.map((item: any) => {
          const send = {
            id: item.category_id,
            name: item.label,
            firmAverage: item.firm_avg || "0",
            ...item.avgs,
          };
          return send;
        }),
      };
    });
};

export const getAllSectionAndItemsFact = (surveyId: string, type: string) => {
  const config = api.getConfigurations(
    {
      survey_id: surveyId,
      resource_type: type,
    },
    {}
  );

  return axios
    .get(`/analytics/items/`, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      return {
        results: data.results.map((item: any) => {
          return {
            id: item.id,
            label: item.label,
            type: item.question_type,
            options: item.options,
          };
        }),
      };
    });
};

// export const getItemizedHeatMapFact = (userId: any, filters?: any) => {
//   const payload = {
//     resource_type: filters?.types[0],
//     survey_id: filters?.surveys[0],
//     year: filters?.years[0],
//   };
//   const config = api.getConfigurations({}, {});
//   return axios
//     .post(`/analytics/timeseries/table/${userId}/item/`, payload, {
//       ...config,
//     })
//     .then(({ data }: AxiosResponse) => {
//       // const mappedSurveys = []
//       //   data.surveys.map((item: any) => ({
//       //     id: item.id,
//       //     key: item.id,
//       //     title: item.title,
//       //   })) || [];
//       return {
//         selectedYear: data.selected_year,
//         selectedSurvey: data.selected_survey,
//         data: data.data.map((item: any) => {
//           let rows = {};
//           util.lib.entries(item.metadata_avgs).forEach(([key, value]: any) => {
//             rows = {
//               ...rows,
//               [key]: value,
//             };
//           });
//           const send = {
//             id: item.section_id,
//             name: item.name,
//             revieweeAverage: item.reviewee_avg || 0,
//             firmAverage: item.firm_avg,
//             selfAverage: item.self_avg,
//             ...rows,
//           };
//           return send;
//         }),
//       };
//       // return {
//       //   years:
//       //     data.years.map((item: any) => ({
//       //       id: item,
//       //       key: item,
//       //       title: item,
//       //     })) || [],
//       //   selectedYear: data.selected_year,
//       //   surveys: mappedSurveys,
//       //   selectedSurvey:
//       //     mappedSurveys.find((item: any) => item.id === data.selected_survey) ||
//       //     {},
//       //   data: data.data.map((item: any) => ({
//       //     id: item.section_id,
//       //     label: item.label,
//       //     // year: item.year,
//       //     // title: item.index,
//       //     revieweeAverage: item.reviewee_avg || 0,
//       //     firmAverage: item.firm_avg,
//       //     departmentAverage: item.metadata_avgs.department || 0,
//       //     // practiceGroupAverage: 4.5,
//       //     titleAverage: item.metadata_avgs.title,
//       //     selfAverage: item.self_avg,
//       //     // sentiment:
//       //     //   typeof item.comment_sentiment === "string"
//       //     //     ? item.comment_sentiment
//       //     //     : "-",
//       //   })),
//       // };
//     });
// };

export const getDetailOverallDataFact = (groupId?: any, metadata?: any) => {
  const config = api.getConfigurations(
    {
      survey_id: groupId,
      resource_type: "surveyindex360",
      metadata,
    },
    {}
  );
  return axios
    .get(`/analytics/aggregate/overall/table/`, {
      ...config,
    })
    .then(({ data }: AxiosResponse) => {
      const groups =
        data.groups.map((item: any) => ({
          id: item.id,
          key: item.id,
          title: item.name,
        })) || [];
      return {
        groups,
        group:
          groups.find((item: any) => item.id === data["seclected_group"]) || {},
        data: data.results.map((item: any) => {
          const id = item.survey_id;
          return {
            id,
            year: item.year,
            name: item.survey_title,
            firmAverage: item.firm_average,
            ...item.avgs,
          };
        }),
      };
    });
};

export const getOverallRankingsFact = (params?: any, metaKey?: any) => {
  let surveys = [];
  if (params.groups.length) {
    surveys = params.groups;
  } else if (params.surveys.length) {
    surveys = params.surveys;
  } else {
    throw new Error("No Surveys Found");
  }
  const config = api.getConfigurations(
    {
      metadata: metaKey,
      resource_type: params.type[0],
      year: params.year[0],
      survey_id: surveys[0],
      ordering: params.ordering
    },
    {}
  );
  return axios
    .get(`analytics/aggregate/overall/ranking/`, config)
    .then(({ data }: AxiosResponse) => {
      return data.data.map((item: any) => ({
        name: item.label,
        overall_avg: item.overall_average,
        frequency: item.frequency,
      }));
    });
};

export const getCategoryRankingFact = (params: any, meta?: any) => {
  let surveys = [];
  if (params.groups.length) {
    surveys = params.groups;
  } else if (params.surveys.length) {
    surveys = params.surveys;
  } else {
    throw new Error("No Surveys Found");
  }
  const config = api.getConfigurations(
    {
      // page: params.page,
      // page_size: 10,
      // search: params.search,
      ordering: params.sort,
      survey_id: surveys[0],
      year: params.year[0],
      resource_type: params.type[0],
      metadata: meta || "title",
    },
    {}
  );
  return axios
    .get(`/analytics/aggregate/category/ranking/`, config)
    .then(({ data }: AxiosResponse) => {
      const categories = data.category_details;
      const results = data.data;
      // const userColumns = meta.map((item: any) => ({
      //   key: item.field,
      //   label: item.display_name,
      // }));
      const categoryColumns = getCategories(categories);
      const result = results.map((item: any) => {
        return {
          name: item.label,
          frequency: item.frequency,
          ...item,
        };
      });
      return {
        data: {
          results: result,
        },
        // userColumns,
        categoryColumns,
      };
    });
};
