import React from "react";
import {
  Modal,
  Button,
  Icon,
  FormGroup,
  FormControl,
  Text,
  Layout,
  Card,
  Div,
  // FormikInput,
  // Form,
} from "unmatched/components";
// import { useFormik } from "unmatched/hooks";
// import util from "unmatched/utils";
import ModalHeader from "../../../../ModalHeader";
// import { sendEmailReports } from "pages/AdminApp/Reports/reports-api";

// const { yup } = util.formik;

const icons = {
  DOWNLOAD: "fal fa-file-download",
  EMAIL: "fal fa-paper-plane",
};

// const EmailForm = (props: any) => {
//   const { email, onSubmit } = props;
//   const initialValues: any = { email };
//   const validationSchema: any = {
//     email: yup
//       .string()
//       .email("Email field is not valid")
//       .required("Email field is required"),
//   };
//   const formik = useFormik({
//     initialValues,
//     validationSchema: yup.object().shape(validationSchema),
//     onSubmit: (values) => {
//       onSubmit && onSubmit(values);
//     },
//   });
//   return (
//     <Form onSubmit={formik.handleSubmit}>
//       <Layout.Row className="my-3">
//         <Layout.Col className="align-self-center" xl={4}>
//           <FormikInput.Email
//             key="email"
//             placeholder="Custom Mail address"
//             formik={formik}
//           />
//         </Layout.Col>
//         <Layout.Col className="align-self-center px-0">
//           <Button variant="light" type="submit">
//             <Icon icon="far fa-plus" />
//           </Button>
//         </Layout.Col>
//       </Layout.Row>
//     </Form>
//   );
// };

export default function EmailReports(props: any) {
  const {
    show,
    onHide,
    selectedFilters,
    // onDownload,
    user,
    // users,
    total,
    selected,
    onReportSend,
  } = props;

  const [option, setOption] = React.useState<number | null>(null);
  const onEmailSend = async () => {
    const payLoad = () => {
      if (selected.length > 0) {
        setOption(null);
        return {
          user_ids: selected,
        };
      }
      if (user.id || user.empId) {
        setOption(null);
        return {
          user_ids: [user.id],
        };
      }

      if (option === 1) {
        setOption(null);
        return;
      }
      if (option === 2) {
        setOption(null);
        return {
          metadata_filters: selectedFilters,
        };
      }
    };

    await onReportSend(payLoad());
  };

  const getSelectedFilters = () => {
    const filters: any[] = [];
    Object.keys(selectedFilters).map((key) => {
      selectedFilters[key].map((item: string) => filters.push(item));
    });
    return filters.join(", ");
  };
  const getMessageTemplate = () => {
    if (selected.length > 0) {
      return (
        <FormGroup className="p-3">
          <FormControl.Radio>
            <FormControl.Radio.Label>
              <Text.H3>To selected Participants</Text.H3>
              <Text.P2>
                Email reports to {selected.length} selected particpants.
              </Text.P2>
            </FormControl.Radio.Label>
            <FormControl.Radio.Input
              checked={true}
              onChange={() => ""}
            ></FormControl.Radio.Input>
          </FormControl.Radio>
        </FormGroup>
      );
    }
    if (user.id || user.empId) {
      return (
        <>
          <Text.P2>Email report to participant</Text.P2>
          <Card className="my-3">
            <Div className="p-3">
              <Layout.Flex>
                <Layout.FlexItem className="pr-5 pl-1">
                  {/* {JSON.stringify(user)} */}
                  <Text.H3>
                    {user.firstName} {user.lastName}
                  </Text.H3>
                </Layout.FlexItem>
                <Layout.FlexItem className="pr-5 pl-1">
                  <Text.H3>{user.email}</Text.H3>
                </Layout.FlexItem>
              </Layout.Flex>
            </Div>
          </Card>
          {/* <Layout.Flex>
            <Layout.FlexItem className="pr-5 pl-1">
              <FormControl.Checkbox>
                <FormControl.Checkbox.Label>
                  Participant Email
                </FormControl.Checkbox.Label>
                <FormControl.Checkbox.Input
                  checked={meta.participant}
                  onChange={(event: any) =>
                    setMeta((_meta) => ({
                      ..._meta,
                      participant: event.target.checked,
                    }))
                  }
                />
              </FormControl.Checkbox>
            </Layout.FlexItem>
            <Layout.FlexItem className="pr-5 pl-1">
              <FormControl.Checkbox>
                <FormControl.Checkbox.Label>
                  Custom Email
                </FormControl.Checkbox.Label>
                <FormControl.Checkbox.Input
                  checked={meta.custom}
                  onChange={(event: any) =>
                    setMeta((_meta) => ({
                      ..._meta,
                      custom: event.target.checked,
                    }))
                  }
                />
              </FormControl.Checkbox>
            </Layout.FlexItem>
          </Layout.Flex> */}
          {/* <EmailForm email={""} /> */}
        </>
      );
    }
    return (
      <FormGroup className="p-3">
        <FormControl.Radio>
          <FormControl.Radio.Label>
            <Text.H3>To all Participants</Text.H3>
            {/* <Text.P2>Email reports to all participants.</Text.P2> */}

            <Text.P2>Email individual reports all participants.</Text.P2>
          </FormControl.Radio.Label>
          <FormControl.Radio.Input
            // checked
            checked={option === 1}
            // onChange={() => ""}
            onChange={() => setOption(1)}
          ></FormControl.Radio.Input>
        </FormControl.Radio>
        <br />
        {Object.keys(selectedFilters).length > 0 && (
          <FormControl.Radio>
            <FormControl.Radio.Label>
              <Text.H3>To the filtered Participants</Text.H3>
              <Text.P2>
                {/* Emails will be sent to  reports to To Participants matching the criteria. */}
                {/* {Object.keys(selectedFilters).length}{" "}  */}
                Emails will be sent to {total}{" "}
                filtered participant ({getSelectedFilters()}).
              </Text.P2>
            </FormControl.Radio.Label>
            <FormControl.Radio.Input
              // checked={false}
              checked={option === 2}
              onChange={() => setOption(2)}
            ></FormControl.Radio.Input>
          </FormControl.Radio>
        )}
        {/* <div className="mt-2">
          {selectedFilters && JSON.stringify(selectedFilters)}
        </div> */}
      </FormGroup>
    );
  };

  return (
    <Modal show={show} backdrop="static" centered size="lg">
      <ModalHeader title="Email Reports" onHide={() => onHide()} />
      <Modal.Body>{getMessageTemplate()}</Modal.Body>
      <Modal.Footer className="text-right my-4 border-none">
        <Button
          variant="primary"
          className="mr-2"
          onClick={() => onEmailSend()}
        >
          <Icon icon={icons.EMAIL} className="mr-1" />
          Email Reports
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
