import React, { useEffect } from "react";
import {
  Div,
  Layout,
  Placeholder,
  Text,
  ScrollToBottom,
  Button,
  CustomModal as Modal,
} from "unmatched/components";
import useSidebar from "../../Shared/sidebar-hook";
import useSurvey from "../../Shared/hooks/survey-hook";
import QuestionsProgress from "../../Shared/QuestionsProgress";
import Categories from "../../Shared/Categories";
import BackToContact from "../../Shared/BackToContact";
// import Ranking from "../../Shared/Options/Ranking";
import { useHistory, useParams } from "react-router-dom";
import util from "unmatched/utils";
import SubmitResponses from "../../Shared/SubmitResponses";
import { getOptionsTemplate } from "../../Shared/Options";
// import { QUESTION } from "unmatched/utils/enums";
import { useWindowSize } from "react-use";
import styled from "styled-components";
import useToastr from "unmatched/modules/toastr/hook";
import surveyTakeCore from "unmatched/survey/take/components";
import {
  patchCommentResponseFact,
  postCommentResponseFact,
  removeCommentResponseFact,
} from "../../dashboard-api";

const { QuestionCard, TakeSurveyContainer, TakeSurveyFooter } = surveyTakeCore;
import useUtil from "../../Shared/hooks/util-hook";
import { getAllFAQs } from "pages/AdminApp/Settings/settings-api";
import FAQView from "../../Shared/FAQView";

const { QUESTION } = util.enums;

// interface Question {
//   id: number;
//   title: string;
//   feedback?: string;
//   type: string;
//   radio?: any;
//   checkbox?: any;
//   ranking?: any;
//   input?: any;
//   dropdown?: any;
// }

const TakeSurvey = () => {
  const history = useHistory();
  const toastr = useToastr();
  const dashboardUtil = useUtil();
  const layout = useSidebar();
  const state = useSurvey();
  const { id, surveyId } = useParams<any>();

  const questions = state.getActiveQuestions();
  const { meta, sections } = state;

  const media = useWindowSize();
  const [showSidebar, setShowSidebar] = React.useState(false);

  const completionLabel = `${state.getCompletion()} Completed`;
  const lastModifiedLabel = `${meta.lastModified}`;
  const [faqs, setFAQs] = React.useState([]);
  const [show, setShow] = React.useState(false);

  useEffect(() => {
    state.getSurveyData(id, surveyId);

    //eslint-disable-next-line
  }, []);

  useEffect(() => {
    getFAQs();
  }, [meta]);

  const getFAQs = async () => {
    if (meta.indexId === 0) return;
    try {
      const data = await getAllFAQs({ survey_index: meta.indexId });
      setFAQs(data);
    } catch (e: any) {
      new Error(e.message || "");
    }
  };
  const onFeedbackUpdate = (question: any, feedback?: any, commentId?: any) => {
    state.onQuestionUpdate({
      ...question,
      commentId,
      feedback,
    });
  };

  const scrollToTop = () => {
    if (document?.documentElement) document.documentElement.scrollTop = 0;
  };

  const index = sections.findIndex((item: any) => item.id === state?.section?.id);
  const onNext = () => {
    if (meta.canSubmit) {
      state.setConfirmSubmit(true);
    } else {
      // const index = sections.findIndex(
      //   (item: any) => item.id === state.section.id
      // );
      const _section = util.lib.cloneDeep(sections[index + 1]);
      state.setSection(_section);
    }
    scrollToTop();
  };


  const onSubmit = async () => {
    await state.onSubmitSurvey(id);
    state.setConfirmSubmit(true);

    scrollToTop();
  };

  const onBack = () => {
    // const index = sections.findIndex(
    //   (item: any) => item.id === state.section.id
    // );
    const _section = util.lib.cloneDeep(sections[index - 1]);
    state.setSection(_section);
  };

  const getLoadingTemplate = (range?: any) => {
    return util.lib.range(1, range || 5).map((item: any) => {
      return (
        <Div key={item} className="p-3 m-2">
          <Placeholder width="col-3" />
          <Placeholder width="col-8" />
          <Placeholder width="col-8" />
        </Div>
      );
    });
  };

  const getSectionsTemplate = () => {
    if (state.isLoading) {
      return getLoadingTemplate(3);
    }
    return (
      <Categories
        label="string"
        completed={state.analytics.sections}
        categories={sections}
        onSelect={(item: any) => {
          scrollToTop();
          const selected = sections.find((c: any) => c.id === item.id);
          state.setSection(selected || {});
          state.setConfirmSubmit(false);
        }}
        isActive={state.section.id}
      />
    );
  };

  const getQuestionsTemplate = () => {
    if (state.isLoading) {
      return getLoadingTemplate();
    }
    const questionNumbers = util.getQuestionNumbers(questions);

    return (
      <Div>
        {questions.map((item: any) => {
          return (
            <QuestionCard
              key={item.id}
              sNo={questionNumbers[item.id]}
              surveyResponseId={id}
              title={item.title}
              question={item}
              feedback={item.feedback}
              showFeedback={item.hasFeedback && item.type !== QUESTION.Input}
              onAddFeedback={(val: any, commentId: any) =>
                onFeedbackUpdate(item, val, commentId)
              }
              onRemoveFeedback={(commentId: any) =>
                onFeedbackUpdate(item, "", commentId)
              }
              isValid={!state.invalidResponses.includes(item.id)}
              setSaving={state.setSaving}
              isInput={item.type === QUESTION.Input}
              hideOptions={item.type === QUESTION.Paragraph}
              {...{
                patchCommentResponseFact,
                postCommentResponseFact,
                removeCommentResponseFact,
              }}
            >
              {getOptionsTemplate(item, state, id, toastr)}
            </QuestionCard>
          );
        })}
      </Div>
    );
  };

  const getSubmitResponseTemplate = () => {
    return (
      <SubmitResponses
        meta={meta}
        marginLeft={layout.marginLeft}
        onBack={() => {
          state.setConfirmSubmit(false);
        }}
        surveyResponseId={id}
        surveyType={util.enums.Survey.Upward}
        isLoading={state.isLoading}
        completionLabel={completionLabel}
        lastModifiedLabel={lastModifiedLabel}
        setShowSidebar={setShowSidebar}
        onSubmit={
          () => {
            // state.onSubmitSurvey(id).then((success: boolean) => {
            //   if (success) {
            const route = util.appUrls.user.dashboard.upwardReview.getUrl(
              meta.indexId
            );
            history.push(route);
          }
          // })
        }
      />
    );
  };

  const getContainerTemplate = () => {
    // debugger;
    return (
      <>
        <TakeSurveyContainer
          title={meta.title}
          isLoading={state.isLoading}
          isSaving={state.isSaving}
          percentage={completionLabel}
          lastSave={lastModifiedLabel}
          surveyType={util.enums.Survey.Upward}
          sectionTitle={state.section.title}
          setShowSidebar={setShowSidebar}
          showScrollButton={true}
          color={dashboardUtil.getSurveyColor(util.enums.Survey.Upward)}
          ScrollToBottom={ScrollToBottom}
        >
          <Div className="px-1 pb-5">
            {/* <Text.H2>{state.section.title}</Text.H2> */}
            {getQuestionsTemplate()}
          </Div>
        </TakeSurveyContainer>
        {!state.isLoading && (
          <TakeSurveyFooter
            marginLeft={media.width >= 768 ? layout.marginLeft : ""}
            // onNext={onNext}
            onNext={index === sections.length - 1 ? onSubmit : onNext}
            onBack={onBack}
            hideBack={meta.hideBack}
            nextTitle={meta.nextTitle}
            buttonType={meta.buttonType}
            section={{
              completed: state.analytics.sections,
              section: state.section,
            }}
            //
          />
        )}
      </>
    );
  };

  return (
    <Div>
      {showSidebar || media.width >= 768 ? (
        <>
          <Layout.Sidebar
            className={`bg-white border-right ${
              media.width >= 768 ? "" : "position-fixed"
            }`}
            hasHeader
            style={{
              marginLeft: media.width >= 768 ? layout.sidebar.marginLeft : "",
              zIndex: 1050,
              maxHeight: "calc(100vh - 45px)",
            }}
            width={layout.sidebar.width}
          >
            <Div className="border-bottom">
              {meta.indexId ? <BackToContact id={meta.indexId} /> : ""}
            </Div>
            <Div className="border-bottom p-3">
              {/* {meta.indexId ? <BackToContact id={meta.indexId} /> : ""} */}
              <Text.P1 className="fs-14 text-muted">Survey Ends On</Text.P1>
              <Text.P1 className="fs-14 my-1 fw-600">{meta.endDate}</Text.P1>
            </Div>
            <Div className="border-bottom p-2">
              {faqs?.length > 0 && <Button variant="link" onClick={() => setShow(true)}>
                FAQs
              </Button>}
            </Div>
            <Div className="border-bottom py-4">
              <Div className="pl-4 text-muted">
                <Text.H3>Feedback For</Text.H3>
              </Div>
              <Div className="pl-4 pt-3">
                <Text.H3>{meta.surveyFor}</Text.H3>
                {/* <Text.H4>Partner</Text.H4> */}
              </Div>
              <Div className="pt-2">
                <QuestionsProgress
                  label="Questions"
                  isLoading={state.isLoading}
                  total={state.analytics.total}
                  status=""
                />
                <QuestionsProgress
                  label="Completed"
                  isLoading={state.isLoading}
                  total={state.analytics.completed}
                  status="success"
                />
              </Div>
            </Div>
            <Div className="py-4">
              <Div className="pl-4 text-muted">
                <Text.H4>Categories</Text.H4>
              </Div>
              <Div className="py-3">{getSectionsTemplate()}</Div>
            </Div>
          </Layout.Sidebar>
          {showSidebar || media.width <= 768 ? (
            <Overlay onClick={() => setShowSidebar(false)} />
          ) : (
            ""
          )}
        </>
      ) : (
        // <Div
        //   className="px-3 py-2 position-fixed bg-white shadow border cursor-pointer"
        //   style={{ top: 60, left: 0, zIndex: 999 }}
        //   onClick={() => setShowSidebar(true)}
        // >
        //   <Icon icon="far fa-bars" />
        // </Div>
        <></>
      )}
      <Div
        style={{
          marginLeft: media.width >= 768 ? layout.container.marginLeft : "",
        }}
      >
        {!state.confirmSubmit
          ? getContainerTemplate()
          : getSubmitResponseTemplate()}
      </Div>
      <Modal
        show={show}
        onHide={() => setShow(false)}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        dialogClassName="modal-90w"
      >
        <Modal.Header closeButton>
          <Modal.Title>FAQs</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <FAQView faqs={faqs} />
        </Modal.Body>
      </Modal>
    </Div>
  );
};

export default TakeSurvey;
const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(6px);
  z-index: 998;
  height: 100vh;
  width: 100vw;
  cursor: pointer;
`;
