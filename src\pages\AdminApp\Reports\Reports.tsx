import React from "react";
import { Redirect, Route } from "react-router";
import appUrls from "unmatched/utils/urls/app-urls";
import AppRoutes from "../../AppRoutes";
import REPORTS_ROUTES from "./reports-routes";

export default function Reports() {
  return (
    <AppRoutes routes={REPORTS_ROUTES}>
      <Route path={appUrls.admin.reports.default}>
        <Redirect to={appUrls.admin.reports.surveyList} />
      </Route>
    </AppRoutes>
  );
}
