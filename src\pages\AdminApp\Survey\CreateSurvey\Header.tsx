import React, { ReactNode } from "react";
// Helpers
import useCustomLayout from "../../Shared/custom-layout-hook";
// Components
import CustomHeader from "../../Shared/CustomHeader/CustomHeader";
interface HeaderProps {
  title: ReactNode;
  metaItem?: ReactNode;
  breadcrumbs?: any;
  styles?: any;
}

const Header = (props: HeaderProps) => {
  const { title, metaItem, breadcrumbs } = props;
  const layout = useCustomLayout();

  return (
    <CustomHeader
      title={title}
      metaItem={metaItem}
      style={{ marginLeft: layout.marginLeft, ...props.styles }}
      breadcrumbs={breadcrumbs}
    />
  );
};

export default Header;
