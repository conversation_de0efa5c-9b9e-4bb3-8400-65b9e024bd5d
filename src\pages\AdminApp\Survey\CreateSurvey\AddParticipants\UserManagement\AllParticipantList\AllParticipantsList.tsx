import React from "react";
// Components
import { Text, Layout, Div, Table, FormControl } from "unmatched/components";
import { fetchParticipantFact } from "../participants-api";
import { useTable } from "unmatched/hooks";
import { get, keys, map } from "lodash";
import useFilter from "pages/CommonFilters/hook";
import _ from "lodash";
import useSession from "unmatched/modules/session/hook";
// import useToastr from "unmatched/modules/toastr/hook";

interface Pairing {
  count_items: number;
  count_pages: number;
  results: Array<Participants>;
  surveys: Array<{ id: number; name: string }>;
}
interface Participants {
  // rater: PairInfo;
  // target: PairInfo;
  status: null | string;
  email: string;
  emp_id: string;
  first_name: string;
  id: number;
  last_name: string;
  metadata: any;
  // Wright"
  // file: string;÷
}

const AllParticipantsList = ({ survey, show, counter }: any) => {
  // const toastr = useToastr();
  const { user } = useSession();
  const tableMeta = useTable({});
  const [lSearch, setLSearch] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(true);
  // const [ordering, setOrdering] = React.useState("-updated_at");
  const [pairings, setPairings] = React.useState<Pairing>({
    count_items: 0,
    count_pages: 0,
    results: [],
    surveys: [],
  });
  const [filters, setFilters] = React.useState({
    search: "",
    page: 1,
    totalPages: 4,
  });

  const [
    ,
    //isSurveyBuilder,
    setIsSurveyBuilder,
  ] = React.useState(false);

  const filtersState = useFilter();

  // setMetas
  const [metas, setMetas] = React.useState<any>([]);

  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      _.forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      // arr.push({
      //   title: "All",
      //   value: ["All"],
      // });
      setMetas(arr);
    });
  };

  React.useEffect(() => {
    getFilters();
    setIsSurveyBuilder(window.location.href.includes("admin/survey/create"));
  }, []);

  React.useEffect(() => {
    if (!show || counter > 0) {
      getParticipants();
    }
  }, [show, counter]);

  React.useEffect(() => {
    getParticipants();
  }, [filters, lSearch]);

  const getParticipants = async (params?: any) => {
    try {
      setIsLoading(true);
      // const fileInfo = await fetchPairFileInfoFact(id);
      // setFileInfo(fileInfo.data);

      const adminPairings = await fetchParticipantFact(survey.id, {
        page: filters.page,
        page_size: 10,
        search: lSearch.length > 0 ? lSearch : undefined,
        index_id: survey.id,
        // ordering,
        ...(params && params),
      });
      // if (isSurveyBuilder) {
      //   adminPairings = await fetchSurveyAbstractPairingsFact({
      //     page: filters.page,
      //     page_size: 10,
      //     search: filters.search.length > 0 ? filters.search : undefined,
      //     index_id: survey.id,
      //     ordering,
      //     ...(params && params),
      //   });
      // } else {
      //   ;
      // }
      setPairings(adminPairings.data);
      tableMeta.updatePagination({
        totalPages: adminPairings.data?.count_pages,
      });
      setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  const onSearch = () => {
    setFilters((_filters) => ({
      ..._filters,
      search: lSearch,
      page: 1,
    }));
  };

  const onPageSelect = (page: number) => {
    setFilters((_filters) => ({
      ..._filters,
      page,
    }));
  };

  const getColumns = () => {
    const basic = [{ key: 2, label: "No.", hasSort: false }];

    const getSubmittedStatus = () => {
      if (
        (survey.type === "SurveyIndexEngagement" ||
          survey.type === "SurveyIndexSelf") &&
        survey.enableSubmittedStatus === true
      ) {
        return [
          {
            key: 6,
            label: "Submission Status",
            hasSort: false,
            sortKey: "status",
            sortValue: "",
          },
        ];
      }
      return [];
    };
    return [
      ...basic,
      {
        key: 3,
        label: "Full Name",
        hasSort: true,
        sortKey: "rater__first_name",
        sortValue: "",
      },
      { key: 4, label: "Employee ID", hasSort: false },
      {
        key: 5,
        label: "Email",
        hasSort: true,
        sortKey: "rater__email",
        sortValue: "",
      },
      ...getSubmittedStatus(),

      // {
      //   key: 6,
      //   label: "Target Full Name",
      //   hasSort: true,
      //   sortKey: "target__first_name",
      //   sortValue: "",
      // },
      // { key: 7, label: "Target Employee ID", hasSort: false },
      // {
      //   key: 8,
      //   label: "Target Email",
      //   hasSort: true,
      //   sortKey: "target__email",
      //   sortValue: "",
      // },
      // { key: 9, label: "Version", hasSort: false },
    ];
  };

  const columnsData = getColumns(); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL

  const getCompColumns = () => {
    const metasColums = () => {
      const arr: any = [];
      metas.map((item: any) => {
        if (item.key) {
          return arr.push({ key: item.key, label: item.title, hasSort: false });
        } else return 0;
      });
      return arr;
    };
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
      // ...metasColums(),
    }));
    // debugger
    console.log(columnsList);
    return [...columnsList, ...metasColums()];
  };

  const getMetaColumns = () => {
    const arr: any = [];
    metas.map((item: any) => {
      if (item.key) {
        return arr.push(item.key);
      } else return 0;
    });
    return arr;
  };
  const getRows = () => {
    return pairings?.results?.map((item: Participants, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.emp_id}>
          <Table.Data width="70px">
            <Text.P1>{filters.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {item.first_name} {item.last_name}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.email}</Text.P1>
          </Table.Data>
          {(survey.type === "SurveyIndexEngagement" ||
            survey.type === "SurveyIndexSelf") &&
            survey.enableSubmittedStatus === true && (
              <Table.Data>
                <Text.P1>{item.status}</Text.P1>
              </Table.Data>
            )}
          {getMetaColumns().map((_i: any) => (
            <Table.Data key={_i}>
              <Text.P1>
                {item?.metadata && item?.metadata[_i]
                  ? item?.metadata[_i]
                  : "-"}
              </Text.P1>
            </Table.Data>
          ))}
          {/* <Table.Data>
            <Text.P1>
              {item.target.first_name} {item.target.last_name}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.emp_id}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.target.email}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.survey?.name}</Text.P1>
          </Table.Data> */}
        </Table.Row>
      );
    });
  };

  return (
    <>
      <Layout.Row className="justify-content-end  my-2">
        <Layout.Col className="py-1 d-flex flex-row w-100" lg={5} md={12}>
          <Div className="w-100">
            <FormControl.Search
              value={lSearch}
              onChange={(evt: any) => setLSearch(evt.target.value)}
              onSearch={onSearch}
              placeholder="Search for name, email or emp id"
            />
          </Div>
        </Layout.Col>
      </Layout.Row>
      <Table
        columns={getCompColumns()}
        rows={pairings.results}
        customRows
        render={() => getRows()}
        hasPagination
        activePage={filters.page}
        pages={tableMeta.totalPages}
        onPageSelect={onPageSelect}
        isLoading={isLoading}
        // onSort={(item: any) => {
        //   const label = util.label.getSortingLabel(
        //     item.sortKey,
        //     item.sortValue
        //   );
        //   setColumnsData((_columns: any) => {
        //     return Object.values(tableMeta.resetColumns(_columns, item));
        //   });
        //   setOrdering(label);
        //   getParticipants({ ordering: label });
        // }}
      />
    </>
  );
};

export default AllParticipantsList;
