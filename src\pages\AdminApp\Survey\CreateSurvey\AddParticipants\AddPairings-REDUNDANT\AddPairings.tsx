import React from "react";
import util from "unmatched/utils";
import AllPairings from "./AllPairings";
import PairingUsers from "./PairingUsers";
import { useCreateSurveyContext } from "../../Provider";
import { mapFileToIndexFact } from "../../../survey-api";


const AddPairings = (props: any) => {
  const [viewPair, setViewPair] = React.useState<any>(null);
  const [selected, setSelected] = React.useState<any>([]);
  const [isSaving, setIsSaving] = React.useState<boolean>(false);
  const { updateBuilderStatus, survey, updateValidators } =
    useCreateSurveyContext();

  // const canConfirm = viewPair && selected && selected.key;

  const validate = () =>
    new Promise((resolve: Function, reject: Function) => {
      // const list = survey.data.pairings || [];
      if (selected.length) {
        resolve();
      } else {
        reject("Please select pairing file to continue");
      }
    });

  React.useEffect(() => {
    const key = `pairing-${survey.data.id}`;
    updateValidators({
      key,
      validate,
    });
    return () => {
      updateValidators({ key, validate: null });
    };
  }, [selected]);

  React.useEffect(() => {
    setSelected(survey.data.pairings || []);
    !props.viewOnly && updateBuilderStatus(util.enums.SurveyStatus.AddPairings);
  }, []);

  const onPairSelect = (item: any) => {
    setIsSaving(true);
    mapFileToIndexFact(item.key, survey.data.id).then(() => {
      // const pairings = [...selected, item.key];
      const pairings = [item.key];
      survey.setData({
        ...survey.data,
        pairings,
        updatedAt: util.date.getFormatedTime(
          new Date(),
          " MMMM dd, yyyy, HH:mm"
        ),
      });
      setSelected(pairings);
      setIsSaving(false);
      // setViewPair(true);
    });
  };

  const onBack = () => {
    setViewPair(null);
  };

  return (
    <div>
      {!viewPair ? (
        <AllPairings
          selected={selected}
          onPairSelect={onPairSelect}
          viewPair={viewPair}
          setViewPair={setViewPair}
          viewOnly={props.viewOnly}
          breadcrumbs={props.breadcrumbs}
          survey={survey}
          isSaving={isSaving}
        />
      ) : (
        <PairingUsers
          viewOnly={props.viewOnly}
          onBack={onBack}
          viewPair={viewPair}
          breadcrumbs={props.breadcrumbs}
        />
      )}
    </div>
  );
};

export default AddPairings;
