import React from "react";
import {
  Table,
  Text, //FormControl,
  Layout,
} from "unmatched/components";
import util from "unmatched/utils";
import { useTable } from "unmatched/hooks";
// import useFilters from "./filter-hook";
// import JSON_DATA from "./people-meta";
// import PeopleFilters from "./PeopleFilters";
import TableContainer from "./TableContainer";
// import { getPeopleAnalyticsFromId } from "../../analytics-api";
// import { Link } from "react-router-dom";
import { getOverallRankingsFact } from "../aggregate-api";
import AnalyticsFilters from "../../Shared/AnalyticsFilters/AnalyticsFilters";
import useAnalyticsFilters from "../../Shared/AnalyticsFilters/hook";
import useFilter from "pages/CommonFilters/hook";
// import { util } from "prettier";
// import PropTypes from 'prop-types'

interface User {
  key: string;
  overallAverage: string;
  selfAverage: string;
  categoryAverage: string;
  frequency: string;
  target: {
    id: string;
    empId: string;
    firstName: string;
    lastName: string;
    email: string;
    metadata: any;
  };
  index: {
    id: string;
    title: string;
  };
}
// interface Request {
//   page?: number;
// }

const defaultSelected = { id: "", key: "", title: "", values: [] };

const getFilterOptions = (_filters: any) => {
  const filters = _filters || {};
  return util.lib.entries(filters).map((item: any) => {
    const [key, value] = item;
    return {
      id: key,
      key,
      title: value.label,
      values: value.values,
    };
  });
};

const getSurveys = (_filters: any) => {
  const hasGroups = _filters.selected.groups.length;
  return {
    groups: _filters.selected.groups,
    surveys: hasGroups ? [] : _filters.selected.surveys,
    type: hasGroups ? ["surveyindex360"] : _filters.selected.types || [],
  };
};

const Rankings = () => {
  // const { id } = props;
  // const filters = useFilters();
  const tableMeta = useTable({
    page: 1,
    size: 10,
    totalPages: 0,
    search: "",
    sort: "",
  });
  const [users, setUsers] = React.useState<Array<User>>([]);
  const filters = useAnalyticsFilters();
  const metaFilters = useFilter();
  let filterOptions = getFilterOptions(metaFilters.filters);
  const [selected, setSelected] = React.useState(defaultSelected);

  const [columns, setColumns]: any = React.useState({
    // emp_id: { label: "Emp ID", sortValue: "", hasSort: true, order: 1 },
    label: {
      label: "Name",
      sortValue: "label",
      hasSort: true,
      order: 2,
      // pushDynamic: true,
    },
    overall_avg: {
      label: "Overall Avg",
      sortValue: "overall_avg",
      hasSort: true,
      order: 3,
    },
    // self_avg: { label: "Self Avg", sortValue: "", hasSort: false, order: 4 },
    frequency: {
      label: "Number of ratings",
      sortValue: "frequency",
      hasSort: true,
      order: 5,
    },
  });

  const [dynamicColumns, setDynamicColumns] = React.useState({});

  React.useEffect(() => {
    metaFilters.getFilters((_metaFilters: any) => {
      filterOptions = getFilterOptions(_metaFilters);
      const [item] = filterOptions;
      if (item) {
        setSelected(item);
      }
      onTableLoad("", item.key);
    });
    //eslint-disable-next-line
  }, []);

  const onTableLoad = (year?: any, meta?: string) => {
    filters
      .getFilters({
        year,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          getPeopleAnalytics(
            {},
            {
              ..._filters,
              surveys: _filters.surveys.filter(
                (item: any) => selectedSurvey?.type === item.type
              ),
              selected: {
                ..._filters.selected,
                surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                groups: [],
                // groups: params?.groups || _filters.selected.groups,
              },
            },
            meta
          );
        },
        (err: any) => {
          console.log(err);
          tableMeta.setLoading(false);
        }
      );
  };

  const onTypeChange = (item: any) => {
    const surveys = filters.surveysData.filter((_survey: any) => {
      if (_survey.type === 'surveyindex360group' && item.key === 'surveyindex360') return true
      return _survey.type === item.key
    }) || [];
    const [selectedSurvey]: any = surveys || {};
    const groups: any = selectedSurvey.rater_groups?.map(
      (group: any) => ({
        id: group.id,
        key: group.id,
        title: group.title,
        groupId: selectedSurvey.id,
        groupKey: group["rater_group"],
      })
    );
    getPeopleAnalytics(
      {},
      {
        ...filters,
        groups,
        selected: {
          ...filters.selected,
          types: [item.key === 'surveyindex360' ? 'surveyindex360group' : item.key],
          surveys:  selectedSurvey && selectedSurvey.id ? [selectedSurvey.id] : [],
          groups: [],
        },
      },
      selected.key
    );
  };

  const getPeopleAnalytics = (
    { page, search, ordering }: any,
    _filters?: any,
    meta?: string
  ) => {
    tableMeta.setLoading(true);
    getOverallRankingsFact(
      {
        ordering,
        search,
        page: page ?? 1,
        year: _filters.selected.years,
        // type: ,
        ...getSurveys(_filters),
      },
      meta
    ).then(
      (results: any) => {
        filters.setFilters(_filters);
        tableMeta.setLoading(false);
        setUsers(results);
      },
      (err: any) => {
        console.log(err);
        tableMeta.setLoading(false);
      }
    );
  };

  // const onSearch = (search: any) => {
  //   getPeopleAnalytics({ search }, filters);
  // };

  // const getSearchInput = () => {
  //   return (
  //     <>
  //       { === false ? (
  //         <FormControl.Search
  //           placeholder="Search for name, id, department"
  //           size="md"
  //           className="bg-white"
  //           value={tableMeta.search}
  //           onChange={(evt: any) => tableMeta.setSearch(evt.target.value)}
  //           onSearch={onSearch}
  //         />
  //       ) : null}
  //     </>
  //   );
  // };

  const getFiltersTemplate = () => {
    return (
      <>
        <Layout.Row className="mb-4">
          <Layout.Col className="" xl="3">
            <Table.Filter
              title="Select"
              selected={selected.key}
              selectedLabel={selected.title}
              options={filterOptions}
              onSelect={(item: any) => {
                setSelected(item);
                getPeopleAnalytics({}, filters, item.key);
              }}
            />
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col className="align-self-center">
            <AnalyticsFilters
              filters={filters}
              hideGroups
              config={{
                surveys: {
                  multiple: false,
                },
                layout: {
                  // year: {
                  //   xl: 3,
                  // },
                  type: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  // survey: {
                  //   xl: 3,
                  // },
                  // group: {
                  //   xl: 3,
                  // },

                  // },
                  // layout: {
                  year: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  survey: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  group: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad(item.key);
              }}
              onTypeChange={onTypeChange}
              onSurveyChange={(items: any) => {
                const selectedSurvey: any =
                  filters.surveys.find((item: any) =>
                    items.includes(item.id)
                  ) || {};
                const groups: any = selectedSurvey.rater_groups?.map(
                  (group: any) => ({
                    id: group.id,
                    key: group.id,
                    title: group.title,
                    groupId: selectedSurvey.id,
                    groupKey: group["rater_group"],
                  })
                );
                getPeopleAnalytics(
                  {},
                  {
                    ...filters,
                    groups,
                    selected: {
                      ...filters.selected,
                      surveys: items,
                      groups: [],
                    },
                  },
                  selected.key
                );
              }}
              onGroupChange={(items: any) => {
                getPeopleAnalytics(
                  {},
                  {
                    ...filters,
                    selected: {
                      ...filters.selected,
                      groups: util.lib.compact(items),
                    },
                  },
                  selected.key
                );
              }}
            />
          </Layout.Col>
          {/* <Layout.Col xl={2} lg={2} sm={3} xs={12}>
            {getSearchInput()}
          </Layout.Col> */}
        </Layout.Row>
      </>
    );
  };

  const getColumnsData = () => {
    const columnsArray = tableMeta.getColumnsArray(columns);
    const [userInfo, avgs] = tableMeta.splitColumns(columnsArray);
    const output = [
      ...userInfo,
      ...tableMeta.getColumnsArray(dynamicColumns),
      ...avgs,
    ];
    return output;
  };

  const getRowsTemplate = () => {
    return users.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.name}>
          <Table.Data>
            <Text.P1>{item.name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.overall_avg || "0"}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.frequency || "0"}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  if (!users?.length) return null;

  return (
    <TableContainer
      title={"Overall Ranking list"}
      filters={getFiltersTemplate()}
    >
      <Table
        tableClass="bg-white"
        columns={getColumnsData()}
        isLoading={tableMeta.isLoading}
        rows={users}
        customRows
        render={() => getRowsTemplate()}
        onSort={(item: any) => {
          getPeopleAnalytics(
            {
              ordering: util.label.getSortingLabel(item.key, item.sortValue),
            },
            filters,
            selected.key
          );
          if (item.isDynamic) {
            setDynamicColumns(tableMeta.resetColumns(dynamicColumns, item));
          } else {
            setColumns(tableMeta.resetColumns(columns, item) || {});
          }
        }}
        hasPagination={false}
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
      />
    </TableContainer>
  );
};

// Rankings.propTypes = {

// }

export default Rankings;
