import React from "react";
// Badge
import {
  Card,
  Div,
  Text,
  Layout,
  Span,
  Icon,
  PageContainer,
  Placeholder,
} from "unmatched/components";
import styled from "styled-components";
import CustomHeader from "pages/AdminApp/Shared/CustomHeader/CustomHeader";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";

const { Paste } = icons;

const LogDot = styled(Div)`
  position: absolute;
  left: -5px;
  top: -8px;
`;

const LogDotIcon = styled(Icon).attrs({
  className: "fs-10",
})`
  color: grey;
`;

export const ACTIVITY_LOG = [
  {
    if: 1,
    title: "Survey begins",
    time: "13th Aug 2020, 12:00 AM PST",
    user: "User 1",
  },
  {
    if: 2,
    title: "Activation Email sent to All Participants ",
    time: "13th Aug 2020, 12:00 AM PST",
    user: "User 1",
  },
  {
    if: 3,
    title: "Nudge Settings Updated to “Medium”",
    time: "13th Aug 2020, 12:00 AM PST",
    user: "User 1",
  },
];

const SurveyTimeline = (props: any) => {
  // meta
  const { survey, layout, globalLoad } = props;
  return (
    <Div className="Single-Survey">
      <CustomHeader
        style={{ marginLeft: layout.marginLeft }}
        title={
          <Layout.Flex>
            <Layout.FlexItem>
              <Text.H1 className="pb-2">
                {globalLoad ? <Placeholder width="col-12" /> : survey.name}
              </Text.H1>
            </Layout.FlexItem>
            <Layout.FlexItem className="pl-2">
              {/* <Badge variant={meta.variant}>{meta.title}</Badge> */}
            </Layout.FlexItem>
          </Layout.Flex>
        }
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Manage Survey" },
          { label: survey.name },
        ]}
      />
      <PageContainer className="pt-2">
        <Layout.Container fluid className="pt-3">
          <Card className="mb-4" noShadow>
            <Card.Header className="pl-3 py-2">
              <Text.H3>Survey Timeline Log:</Text.H3>
            </Card.Header>
            <Div className="pl-3 py-2 pt-4">
              {ACTIVITY_LOG.map((item: any, index: number) => {
                const borderClass =
                  index === ACTIVITY_LOG.length - 1 ? "" : "border-left";

                return (
                  <Layout.Flex key={index}>
                    <Layout.FlexItem
                      className={`${borderClass} px-1 mb-1 position-relative`}
                    >
                      {/* <Span style={{ borderRadius: "50%" }} className="bg-light"></Span> */}
                      <LogDot>
                        <LogDotIcon icon="far fa-circle" />
                      </LogDot>
                    </Layout.FlexItem>
                    <Layout.FlexItem className="pb-5 pl-2">
                      <Text.H3>{item.title}</Text.H3>
                      <Text.P1 className="pt-2">{item.time}</Text.P1>
                      <Div className="text-muted fs-12 pt-2">
                        <Span
                          style={{ borderRadius: "50%" }}
                          className="bg-warning px-2 py-1"
                        >
                          A
                        </Span>{" "}
                        {item.user}
                      </Div>
                    </Layout.FlexItem>
                  </Layout.Flex>
                );
              })}
            </Div>
          </Card>
        </Layout.Container>
      </PageContainer>
    </Div>
  );
};

// SurveyTimeline.propTypes = {

// }

export default SurveyTimeline;
