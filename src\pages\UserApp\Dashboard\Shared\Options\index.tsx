import React from "react";
import util from "unmatched/utils";
import surveyTakeCore from "unmatched/survey/take/components";
import {
  patchQuestionResponseFact,
  postQuestionResponseFact,
  removeQuestionResponseFact,
  patchCommentResponseFact,
  postCommentResponseFact,
  removeCommentResponseFact,
} from "../../dashboard-api";
// import useToastr from "unmatched/modules/toastr/hook";

const { Radio, Checkbox, Input, Rating } = surveyTakeCore;

const { QUESTION } = util.enums;

// import { animateScroll as scroll } from "react-scroll";

export const getOptionsTemplate = (
  question: any,
  state: any,
  surveyResponseId: any,
  toastr: any
) => {
  // const toastr = useToastr();
  const optionProps = {
    question,
    surveyResponseId,
    section: state.section,
    setSaving: state.setSaving,
    isSaving: state.isSaving,
  };

  // const autoScroll = (height: number) => {
  //   if (document?.documentElement) {
  //     document.documentElement.scrollTop += height
  //     const c = document.documentElement.scrollTop || document.body.scrollTop;
  //     if (c > 0) {
  //       window.requestAnimationFrame();
  //       window.scrollTo(0, c + c / 8);
  //     }
  //     scroll.scrollMore(height, {
  //   duration: 100,
  //   delay: 0
  // });
  //   }
  // };

  const getCheckboxTemplate = () => (
    <Checkbox
      {...optionProps}
      toastr={toastr}
      onSelect={(selected: any, responseId?: any) => {
        const payload = question;
        payload.responseId = responseId || payload.responseId;
        payload.checkbox.selected = selected;
        state.onQuestionUpdate(payload);
      }}
      onRemove={(selected: any) => {
        const payload = question;
        payload.isValid = !!selected.length;
        payload.checkbox.selected = selected;
        state.onQuestionUpdate(payload);
      }}
      onDeleteResponse={state.onDeleteResponse}
      {...{
        patchQuestionResponseFact,
        postQuestionResponseFact,
        removeQuestionResponseFact,
      }}
    />
  );

  const getRadioTemplate = () => (
    <Radio
      {...optionProps}
      onSelect={(value: string, responseId: any) => {
        // autoScroll(200);
        const payload = question;
        payload.responseId = responseId || payload.responseId;
        payload.radio.selected = value;
        state.onQuestionUpdate(payload);
        // autoScroll(200);
      }}
      onRemove={(value: string) => {
        const payload = question;
        payload.isValid = false;
        payload.radio.selected = payload.radio.selected.filter(
          (item: any) => item !== value
        );
        state.onQuestionUpdate(payload);
      }}
      onDeleteResponse={state.onDeleteResponse}
      {...{
        patchQuestionResponseFact,
        postQuestionResponseFact,
        removeQuestionResponseFact,
      }}
    />
  );

  const getRatingTemplate = () => (
    <Rating
      {...optionProps}
      onSelect={(value: string, key: string, responseId: any) => {
        // autoScroll(160);
        const payload = question;
        payload.responseId = responseId || payload.responseId;
        payload.isValid = !!value;
        payload.rating.selected = Number(key);
        state.onQuestionUpdate(payload);
        // autoScroll(160);
      }}
      onRemove={() => {
        const payload = question;
        payload.isValid = false;
        payload.checkbox.selected = 0;
        state.onQuestionUpdate(payload);
      }}
      onDeleteResponse={state.onDeleteResponse}
      {...{
        patchQuestionResponseFact,
        postQuestionResponseFact,
        removeQuestionResponseFact,
      }}
      hideNoBasisOption={state.meta.hideNoBasisOption}
    />
  );

  const getInputTemplate = () => (
    <Input
      {...optionProps}
      onSelect={(value: string, responseId: any) => {
        const payload = question;
        payload.commentId = responseId || payload.commentId;
        payload.feedback = value;
        state.onQuestionUpdate(payload);
      }}
      onDeleteResponse={() => {
        const payload = question;
        payload.feedback = "";
        payload.commentId = 0;
        state.onQuestionUpdate(payload);
      }}
      {...{
        patchCommentResponseFact,
        postCommentResponseFact,
        removeCommentResponseFact,
      }}
    />
  );

  const options = {
    [QUESTION.Radio]: getRadioTemplate,
    [QUESTION.Checkbox]: getCheckboxTemplate,
    [QUESTION.Rating]: getRatingTemplate,
    [QUESTION.Input]: getInputTemplate,
  };

  const getTemplate = util.lib.get(options, question.type);
  return getTemplate ? getTemplate() : <></>;
};
