import _, { isEqual } from "lodash";
import React, { useState } from "react";
import {
  FormControl,
  Form,
  FormGroup,
  FormikInput,
  Layout,
  Text,
  FormikAutoSave,
  ComboFilter,
  Div,
  OverlayTrigger,
  Tooltip,
  Icon,
  Modal,
} from "unmatched/components";
import util from "unmatched/utils";
import {
  getSurveyByIdFact,
  patchMinResCountFact,
  patchWavierQuestionFact,
} from "../../survey-api";
import { useDebounce } from "react-use";
import {
  getVisibilityMetaDetails,
  postVisibilityFilters,
} from "./questions-api";
import useToastr from "unmatched/modules/toastr/hook";
import ModalHeader from "pages/AdminApp/ModalHeader";

const getValue = (obj: any, key: string) => _.get(obj, key);

const CreateVersion = (props: any) => {
  const {
    version,
    onUpdate,
    // updateValidators,
    setLoading,
    permissions,
    formik,
    initialValues,
    patchSurveyVersionFact,
    wavier,
    getWavierQuestion,
    survey,
  } = props;

  const [metaForVisibility, setMetaForVisibility] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const { showToast } = useToastr();

  React.useEffect(() => {
    getSetMetaOptions();
    getSetMetaDetails();
  }, []);

  const getSetMetaOptions = async () => {
    try {
      const { data } = await getVisibilityMetaDetails(version.id);
      const comboFilterData = (data as any).labels.reduce(
        (acc: any, el: any) => {
          acc[el.field] = {
            label: el.display_name,
            values: (data as any).values[el.field] || [],
          };
          return acc;
        },
        {}
      );
      setMetaForVisibility(comboFilterData);
    } catch (err) {
      console.log(err);
    }
  };

  const getSetMetaDetails = async () => {
    try {
      const updatedSurvey = await getSurveyByIdFact(survey.data.id);
      const thisVersion = (updatedSurvey as any).versions.find(
        (el: any) => el.id === version.id
      );
      const targets = thisVersion?.settings?.rules?.target;
      if (targets && Object.keys(targets).length) {
        const preSelectedFilters = Object.keys(targets)
          .filter((t: any) => t !== "role__in")
          .reduce((acc: any, key: any) => {
            acc[key.split("__in")[0]] = targets[key];
            return acc;
          }, {});
        setSelectedFilters(preSelectedFilters);
      }
    } catch (err) {
      console.log(err);
    }
  };

  // const getRaterOptionsTemplate = () => {
  //   return _.map([], (item: any) => (
  //     <FormControl.SelectItem
  //       key={item}
  //       onSelect={() => {
  //         formik.setFieldValue("type", item, true);
  //       }}
  //     >
  //       <Text.P1 className={"text-capitalize"}>{item.toLowerCase()}</Text.P1>
  //     </FormControl.SelectItem>
  //   ));
  // };

  // const getTargetOptionsTemplate = () => {
  //   return _.map([], (item: any) => (
  //     <FormControl.SelectItem
  //       key={item}
  //       onSelect={() => {
  //         formik.setFieldValue("targetGroup", item.id, true);
  //       }}
  //     >
  //       <Text.P1 className={"text-capitalize"}>{item.toLowerCase()}</Text.P1>
  //     </FormControl.SelectItem>
  //   ));
  // };

  const onFormPatch = (_values: any) => {
    const payload = {
      ...version,
      ..._values,
      name: _values.label,
    };
    setLoading(true);
    patchSurveyVersionFact(version.id, payload).then(
      () => {
        setLoading(false);
        onUpdate(payload);
      },
      () => {
        setLoading(false);
      }
    );
    patchMinResCountFact(payload.eligibilityId, payload).then(
      () => {
        setLoading(false);
        onUpdate(payload);
        getWavierQuestion();
      },
      () => {
        setLoading(false);
      }
    );
  };

  useDebounce(
    () => {
      wavier.id && patchWavierQuestionFact(wavier.question, wavier.id);
    },
    1000,
    [wavier]
  );

  const detectRealFilterChange = (
    newSelectedFilters: any,
    selectedFilters: any
  ) => {
    const emptyRemoved = {};
    const emptyRemovedSelected = {};

    for (const key in newSelectedFilters) {
      const value = newSelectedFilters[key];
      if (value?.length) {
        (emptyRemoved as any)[key] = value;
      }
    }

    for (const key in selectedFilters) {
      const value = selectedFilters[key];
      if (value?.length) {
        (emptyRemovedSelected as any)[key] = value;
      }
    }

    return !isEqual(emptyRemoved, emptyRemovedSelected);
  };

  const getVisibilityTemplate = () => {
    if (permissions.hasVisibility) {
      return (
        <>
          <FormGroup className="mt-3">
            <FormGroup.Label>
              Reviewee group
              <OverlayTrigger
                placement="bottom"
                overlay={
                  <Tooltip id="button-tooltip">
                    <Div className="row">
                      <Div className="col pr-0 pt-2" style={{ maxWidth: 10 }}>
                        <Icon icon="fal fa-info-circle fs-12 ml-1" />
                      </Div>
                      <Div className="col">
                        <Text.P1 className="text-left fs-12 fw-300 p-2">
                          Select the reviewee group that will get feedback based
                          on this version.{" "}
                        </Text.P1>
                      </Div>
                    </Div>
                  </Tooltip>
                }
              >
                {({ ref, ...triggerHandler }) => (
                  <span>
                    <i
                      className="far fa-info-circle ml-1"
                      ref={ref}
                      {...triggerHandler}
                    />
                  </span>
                )}
              </OverlayTrigger>
            </FormGroup.Label>
            <Layout.Row className="my-2">
              <Layout.Col>
                <ComboFilter
                  filters={metaForVisibility}
                  selected={selectedFilters}
                  onFilterSelect={async (_selected: any) => {
                    setSelectedFilters(_selected);
                    if (detectRealFilterChange(_selected, selectedFilters)) {
                      try {
                        await postVisibilityFilters({
                          survey_version_id: version.id,
                          target_users_filter: _selected,
                        });
                        getSetMetaOptions();
                      } catch (err: any) {
                        getSetMetaDetails();
                        showToast({
                          variant: "danger",
                          title: err.msg,
                          content: "Try different criteria.",
                        });
                        console.log(err);
                      }
                    }
                  }}
                  onSubmit={() => ""}
                />
              </Layout.Col>
            </Layout.Row>
          </FormGroup>
          {version.eligibilityId && wavier.id && <Div className="click-to-open" onClick={() => setShowModal(true)}>View Waiver Options</Div>}
          <Modal show={showModal} size="lg" centered>
            <ModalHeader
              title="Waiver Options"
              onHide={() => setShowModal(false)}
            />
            <Modal.Body>
              {version.eligibilityId && wavier.id && (
                <>
                  <FormGroup className="mt-3">
                    <FormGroup.Label>Waiver Question</FormGroup.Label>
                    <FormControl.Textarea
                      rows={5}
                      value={wavier.question}
                      onChange={(e: any) => {
                        props.setWavier((d: any) => ({
                          ...d,
                          question: e.target.value,
                        }));
                      }}
                    />
                  </FormGroup>
                  <FormGroup className="mt-3">
                    <FormGroup.Label>Waiver Options</FormGroup.Label>
                    {wavier?.options?.map((item: any, index: number) => {
                      return (
                        <Layout.Row key={item.id} className="mb-2 mx-0">
                          <Layout.Col className="px-0" xl={1}>
                            <FormControl.Text value={index + 1} disabled />
                          </Layout.Col>
                          <Layout.Col>
                            <FormControl.Textarea
                              rows={3}
                              title={item.title}
                              value={item.label}
                              disabled
                            />
                          </Layout.Col>
                        </Layout.Row>
                      );
                    })}
                  </FormGroup>
                </>
              )}
            </Modal.Body>
          </Modal>
        </>
      );
    }
    return null;
  };

  return (
    <Form onSubmit={formik.handleSubmit} onReset={formik.handleReset}>
      <FormikAutoSave
        initialValues={initialValues}
        values={formik.values}
        onSave={onFormPatch}
      >
        {survey.data.type !== "SurveyIndexEngagement" &&
          survey.data.type !== "SurveyIndexSelf" && (
            <Div className="section-title my-2">Version Details</Div>
          )}
        <Layout.Row>
          <Layout.Col xl={6} lg={6}>
            <FormikInput.Text
              label="Title"
              keyName="label"
              placeholder="Name"
              formik={formik}
              required={true}
            />
          </Layout.Col>
          {survey.data.type !== "SurveyIndexEngagement" &&
            survey.data.type !== "SurveyIndexSelf" && (
              <Layout.Col xl={6} lg={6}>
                <FormGroup>
                  <FormGroup.Label>
                    Minimum required responses
                    <span className="required-input">*</span>
                    <OverlayTrigger
                      placement="bottom"
                      overlay={
                        <Tooltip id="button-tooltip">
                          <Div className="row">
                            <Div
                              className="col pr-0 pt-2"
                              style={{ maxWidth: 10 }}
                            >
                              <Icon icon="fal fa-info-circle fs-12" />
                            </Div>
                            <Div className="col">
                              <Text.P1 className="text-left fs-12 fw-300 p-2">
                                Select the number number of responses required
                                for a reviewee to get a full report.
                              </Text.P1>
                            </Div>
                          </Div>
                        </Tooltip>
                      }
                    >
                      {({ ref, ...triggerHandler }) => (
                        <span>
                          <i
                            className="far fa-info-circle"
                            ref={ref}
                            {...triggerHandler}
                          />
                        </span>
                      )}
                    </OverlayTrigger>
                  </FormGroup.Label>
                  {/* - Select the number number of responses required for a reviewee to get a full report. */}
                  <Div className="flex">
                    <FormControl.Number
                      name="minResponses"
                      isInvalid={util.formik.isFieldInvalid(
                        formik,
                        "minResponses"
                      )}
                      value={getValue(formik.values, "minResponses")}
                      onBlur={formik.handleBlur}
                      onChange={(e: any) => {
                        // if (e.target.value === "") return;
                        // const isInvalid = +e.target.value < 2;
                        // if (isInvalid) return;
                        formik.handleChange(e);
                      }}
                      style={{ width: 60 }}
                      min={2}
                    />
                  </Div>
                </FormGroup>
              </Layout.Col>
            )}
        </Layout.Row>
        {getVisibilityTemplate()}
      </FormikAutoSave>
    </Form>
  );
};

// CreateVersion.propTypes = {

// };

export default CreateVersion;
