import React, { useState, useEffect } from "react";
import anchorme from "anchorme";
import styled from "styled-components";
import { Div, Text } from "unmatched/components";
import useSession from "unmatched/modules/session/hook";

const ContactInfo = styled(Div)`
  background: #f2f2f2;
`;

const TH = styled.th`
  text-align: left;
  padding: 8px;
  background-color: #f2f2f2;
`;

const TR = styled.tr`
  text-align: left;
  padding: 8px;
  font-size: 14px;
  font-weight: 400;
`;

const TD = styled.td`
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
  font-size: 14px;
  font-weight: 400;
  user-select: all;
`;

const Table = styled.table`
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
`;

const ContactUs = () => {
  const { client } = useSession();

  const [contactInfo, setContactInfo] = useState({
    contactText: "",
    contacts: [],
  });

  useEffect(() => {
    setContactInfo({
      contactText: client.contactText,
      contacts: client.contacts,
    });
  }, [client]);

  const input = contactInfo.contactText;
  const resultA = input.split(/[\r\n]+/g).map((_i: string) => {
    return anchorme(_i);
  });

  return (
    <>
      <ContactInfo className="p-4 mx-3">
        <Text.H3 className="mb-2">Contact Us</Text.H3>
        {resultA.map((_i: string) => (
          <Text.P1 dangerouslySetInnerHTML={{ __html: _i }} />
        ))}
      </ContactInfo>
      <Div className="p-4 mx-3 mb-4">
        {contactInfo?.contacts && contactInfo?.contacts?.length > 0 && (
          <Table>
            <TR>
              <TH>Location</TH>
              <TH>Branch</TH>
              <TH>Name</TH>
              <TH>Phone</TH>
              {/* <TH>Email</TH> */}
            </TR>
            {contactInfo.contacts.map((c: any) => (
              <TR key={c.id}>
                <TD>{c.location}</TD>
                <TD>{c.branch}</TD>
                <TD>{c.fullName}</TD>
                <TD>{c.phone}</TD>
                {/* <TD>{c.email}</TD> */}
              </TR>
            ))}
          </Table>
        )}
      </Div>
    </>
  );
};

// ContactUs.propTypes = {

// }

export default ContactUs;
