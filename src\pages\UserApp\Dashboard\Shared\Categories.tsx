import React from "react";
import styled from "styled-components";
import { Div } from "unmatched/components";
import util from "unmatched/utils";

interface CategoryItem {
  title: string;
  isActive: boolean;
  totalQuestions: number;
  questionsLeft: number;
  id?: number;
}

interface CategoryProps {
  label: string | React.ReactNode;
  categories: Array<CategoryItem | any>;
  onSelect?: Function;
  completed: any;
  isActive?: number;
}

const Categories = (props: CategoryProps) => {
  const { categories, isActive, completed } = props;
  return (
    <Div>
      {categories.map((ct, i) => {
        // console.log(ct);
        const completedCount = util.lib.get(completed, ct.id) || 0;
        const questionsLeft =
          ct.totalQuestions - completedCount - ct.instructionsCount;
        return (
          <CatergoryList
            isActive={isActive === ct.id}
            className="pointer py-2 pl-45"
            key={i}
            onClick={() => (props.onSelect ? props.onSelect(ct) : "")}
          >
            <p
              className={`title ${
                isActive === ct.id ? "text-primary" : ""
              } m-0`}
            >
              {ct.title}
            </p>
            {/* {ct.isDemographic ? (
              ""
            ) : ( */}
              <>
                <Div className="pr-3">
                  <ProgressBar
                    width={
                      (100 * completedCount) /
                      (ct.totalQuestions - ct.instructionsCount)
                    }
                  />
                </Div>
                <p className="title text-success m-0">
                  {questionsLeft} questions left
                </p>
              </>
            {/* )} */}
          </CatergoryList>
        );
      })}
    </Div>
  );
};

const ProgressBar = (props: any) => {
  return (
    <ProgressBarWidget {...props}>
      <Div className="inner-progress-bar" />
    </ProgressBarWidget>
  );
};

const CatergoryList = styled(Div)`
  width: 100%;
  background: ${(props) => (props.isActive ? "#F2F2F2" : "none")};
  border-left: solid ${(props) => (props.isActive ? "#518CFF" : "none")};
  border-left-width: 2px;
  &.pl-45 {
    padding-left: ${(props) =>
      props.isActive ? "1.37rem !important" : "1.5rem !important"};
  }
  box-sizing: border-box;
  .title {
    font-size: 12px;
  }
`;
const ProgressBarWidget = styled(Div)`
  width: 100%;
  overflow: hidden;
  height: 2px;
  border-radius: 20px;
  background: #f2f2f2;
  margin: 5px 0;
  .inner-progress-bar {
    width: ${(props) => (props.width ? props.width : "0")}%;
    background: #36b37e;
    height: 2px;
  }
`;
export default Categories;
