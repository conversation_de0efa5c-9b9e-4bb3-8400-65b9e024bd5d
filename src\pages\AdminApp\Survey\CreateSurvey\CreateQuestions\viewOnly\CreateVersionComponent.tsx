import {
  FormControl,
  Form,
  FormGroup,
  Layout,
  Text,
  FormikAutoSave,
  ComboFilter,
} from "@unmatchedoffl/ui-core";
import _ from "lodash";
import React, { useState } from "react";

import util from "unmatched/utils";
import { getVisibilityMetaDetails } from "../questions-api";
import { getSurveyByIdFact } from "pages/AdminApp/Survey/survey-api";

const getValue = (obj: any, key: string) => _.get(obj, key);

const CreateVersionComponent = (props: any) => {
  const {
    version,
    // setLoading,
    permissions,
    formik,
    initialValues,
    // patchSurveyVersionFact,
    wavier,
    survey
  } = props;

  const [metaForVisibility, setMetaForVisibility] = useState({});
  const [selectedFilters, setSelectedFilters] = useState({});

  React.useEffect(() => {
    getSetMetaOptions();
    getSetMetaDetails();
  }, []);

  const getSetMetaOptions = async () => {
    try {
      const { data } = await getVisibilityMetaDetails(version.id);
      const comboFilterData = (data as any).labels.reduce((acc: any, el: any) => {
        acc[el.field] = { label: el.display_name, values: (data as any).values[el.field] };
        return acc;
      }, {});
      setMetaForVisibility(comboFilterData);
    } catch(err) {
      console.log(err);
    }
  }

  const getSetMetaDetails = async () => {
    try {
      const updatedSurvey = await getSurveyByIdFact(survey?.data?.id);
      const thisVersion = (updatedSurvey as any).versions.find((el: any) => el.id === version.id);
      const targets = thisVersion?.settings?.rules?.target;
      if (targets && Object.keys(targets).length) {
        const preSelectedFilters = Object.keys(targets)
          .filter((t: any) => t !== "role__in")
          .reduce((acc: any, key: any) => {
            acc[key.split("__in")[0]] = targets[key];
            return acc;
          }, {});
        setSelectedFilters(preSelectedFilters);
      }
    } catch(err) {
      console.log(err);
    }
  }

  const getVisibilityTemplate = () => {
    if (permissions.hasVisibility) {
      return (
        <>
          <FormGroup className="mt-3">
            <FormGroup.Label>Survey Visibility</FormGroup.Label>
            <Layout.Row className="my-2">
              <Layout.Col>
              <ComboFilter
                filters={metaForVisibility}
                selected={selectedFilters}
                // onFilterSelect={async () => {}}
                // onSubmit={() => ""}
              />
              </Layout.Col>
            </Layout.Row>
          </FormGroup>
          <FormGroup className="mt-3">
            <FormGroup.Label>Minimum Required Responses</FormGroup.Label>
            <Layout.Row>
              <Layout.Col xl={2} md={2} sm={2}>
                <FormControl.Number
                  name="minResponses"
                  disabled
                  isInvalid={util.formik.isFieldInvalid(formik, "minResponses")}
                  value={getValue(formik.values, "minResponses")}
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                />
              </Layout.Col>
              <Layout.Col className="align-self-center pl-0">
                <Text.P1>
                  Minimum responses are required to generate a report.
                </Text.P1>
              </Layout.Col>
            </Layout.Row>
          </FormGroup>
          {version.report_eligibility_settings && wavier.question && (
            <>
              <FormGroup className="mt-3">
                <FormGroup.Label>Waiver Question</FormGroup.Label>
                <FormControl.Textarea
                  rows={5}
                  value={wavier.question}
                  disabled
                />
              </FormGroup>
              <FormGroup className="mt-3">
                <FormGroup.Label>Waiver Options</FormGroup.Label>
                {wavier?.options?.map((item: any, index: number) => {
                  return (
                    <Layout.Row key={item.id} className="mb-2 mx-0">
                      <Layout.Col className="px-0" xl={1}>
                        <FormControl.Text value={index + 1} disabled />
                      </Layout.Col>
                      <Layout.Col>
                        <FormControl.Text
                          title={item.title}
                          value={item.label}
                          disabled
                        />
                      </Layout.Col>
                    </Layout.Row>
                  );
                })}
              </FormGroup>
            </>
          )}
        </>
      );
    }
    return null;
  };

  return (
    <Form onSubmit={formik.handleSubmit} onReset={formik.handleReset}>
      <FormikAutoSave
        initialValues={initialValues}
        values={formik.values}
        onSave={() => null}
      >
        <div style={{ display: "flex" }}>
          <FormGroup.Label>Survey Version Name:</FormGroup.Label>{" "}
          <span>{version.name}</span>
        </div>
        {getVisibilityTemplate()}
      </FormikAutoSave>
    </Form>
  );
};

export default CreateVersionComponent;
