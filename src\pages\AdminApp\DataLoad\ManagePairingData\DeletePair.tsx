import React from "react";
import styled from "styled-components";
import { Modal, Text, Button } from "unmatched/components";

const ModalHeader = styled(Modal.Header)`
  background: #fbfbfb;
`;

const ModalFooter = styled(Modal.Footer)`
  border: none;
  padding: 2rem;
`;

const DeletePair = (props: any) => {
  const { show, onHide, onAction } = props;
  return (
    <Modal
      show={show}
      onHide={onHide}
      backdrop="static"
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <ModalHeader closeButton>
        <Modal.Title id="contained-modal-title-vcenter">
          <Text.P1 className="pt-1">Delete Pairing</Text.P1>
        </Modal.Title>
      </ModalHeader>
      <Modal.Body>
        <Text.P1>Are you sure you want to delete this pairing? </Text.P1>
      </Modal.Body>
      <ModalFooter>
        <Button
          variant="outline-danger"
          className="mx-1"
          onClick={() => (onAction ? onAction() : "")}
        >
          Delete
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default DeletePair;
