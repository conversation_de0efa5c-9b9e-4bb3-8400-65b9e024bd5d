import React from "react";
import { useHistory } from "unmatched/hooks";
import styled from "styled-components";

import { Div, Layout, Text, Icon, Nav, Button } from "unmatched/components";
import { useCreateSurveyContext } from "./Provider";
import util from "unmatched/utils";

const NavItem = styled(Nav.Item)`
  font-size: 12px;
  // .active {
  //   position: relative;
  //   color: #2f2f2f !important;
  //   font-weight: 600;
  //   border-radius: 0;
  //   &::after {
  //     border-bottom: 2px solid #518cff;
  //     position: absolute;
  //     content: "";
  //     z-index: 999;
  //     width: 10%;
  //     left: 15px;
  //     bottom: 0;
  //   }
  // }
`;

const NavLink = styled(Nav.Link).attrs({
  className: "text-muted",
})``;

interface StepItemTypes {
  sNo: number;
  hideStepper?: boolean;
  itemKey?: string;
  id: string;
  surveyId: number;
  error?: number;
  defaultVersionId: number | string;
  isComplete: boolean;
  nested?: React.ReactNode;
  onRouteChange: Function;
}

const StepItem = (props: StepItemTypes) => {
  const {
    // hideStepper,
    surveyId,
    id,
    nested,
    defaultVersionId,
    onRouteChange,
    isComplete,
  } = props;
  const history = useHistory();
  const { getLinkContent } = useCreateSurveyContext();
  const currentRoute = history.location.pathname;

  const getRouteStatus = (routePath: string) => {
    return {
      route: routePath,
      active: routePath === currentRoute,
    };
  };

  let linkData: any = getLinkContent(id);

  linkData = {
    ...linkData,
    ...getRouteStatus(linkData.getRoute(surveyId, defaultVersionId)),
  };

  const { title, active, completed, route } = linkData;

  let label = <Text.P1 className="text-white">{title}</Text.P1>;
  let icon: any = "";

  if (props.error) {
    icon = <Icon icon="fas fa-exclaimation" variant="danger" />;
  } else if (isComplete) {
    // label = <Text.P1 className="text-dark font-weight-bold">{title}</Text.P1>;
    icon = (
      <Icon icon="fas fa-check-circle" className="fs-18" variant="success" />
    );
  } else if (active) {
    label = <Text.P1 className="font-weight-bold6">{title}</Text.P1>;
    // icon = <Icon icon="fad fa-circle" variant="primary" />;
  } else if (completed) {
    icon = <Icon icon="fas fa-check-circle" variant="success" />;
  }

  return (
    <NavLink
      className="my-2 px-0 py-2 dark-nav-link"
      eventKey={props.itemKey}
      active={active}
      onSelect={() => onRouteChange(route)}
    >
      <Layout.Flex className="px-3">
        {label}
        <Div className="ml-auto">{icon}</Div>
      </Layout.Flex>
      <Div className="pl-2">{active && nested ? nested : ""}</Div>
    </NavLink>
    // <Div className="cursor-pointer step-item">
    //   <Layout.Flex>
    //     <Layout.FlexItem>{icon}</Layout.FlexItem>
    //     <Layout.FlexItem className="pl-3 pb-1">
    //       <Text.H2>
    //         {/* <Link to={route}>{label}</Link> */}
    //         <Button
    //           onClick=
    //           variant="link"
    //           className="px-0"
    //         >
    //           {label}
    //         </Button>
    //       </Text.H2>
    //     </Layout.FlexItem>
    //   </Layout.Flex>
    //   {!hideStepper && (
    //     <Div
    //       style={{
    //         borderLeft: " 1px dashed #ccc",
    //       }}
    //       className="pb-5 ml-2 pl-4"
    //     >
    //       {active && nested}
    //     </Div>
    //   )}
    // </Div>
  );
};

const Sidebar = (props: {
  getVersions: Function;
  onRouteChange: Function;
  onSubmit?: Function;
  viewOnly?: boolean;
}) => {
  const { getVersions, onRouteChange, onSubmit, viewOnly } = props;
  // const routeParams: any = useParams();

  const { getLinks, activeVersionId, completed, survey, errors } =
    useCreateSurveyContext();
  const steps = getLinks(survey.data.type);
  return (
    <Div className="survey-sidebar my-4">
      <Nav variant="pills" className="flex-column">
        <NavItem>
          {steps.map((item: string, index: number) => {
            const hasVersions = item === "editSurvey" || item === "preview";
            const isComplete = completed.includes(item);
            return (
              <StepItem
                sNo={index + 1}
                key={item}
                itemKey={item}
                id={item}
                surveyId={survey.data.id}
                isComplete={isComplete}
                hideStepper={index === steps.length - 1}
                nested={hasVersions ? getVersions(item) : null}
                defaultVersionId={activeVersionId}
                onRouteChange={onRouteChange}
                error={util.lib.get(errors, item)}
              />
            );
          })}
        </NavItem>
      </Nav>
      <hr />
      {!viewOnly && (
        <Div className="text-center my-4">
          <Button size="lg" className="px-5" onClick={onSubmit}>
            Verify and Launch
          </Button>
        </Div>
      )}
    </Div>
  );
};

export default Sidebar;
