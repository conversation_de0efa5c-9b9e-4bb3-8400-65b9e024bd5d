import React from "react";
import {
  PageContainer,
  Text,
  Layout,
  Table,
  Div,
  ComboBasicFilter,
  Button,
} from "unmatched/components";
import { useTable } from "unmatched/hooks";
import Header from "../Header";
import TABS from "../tabs.interface";
import { reportStatsFact, targetReturnFact } from "./report-count-api";
import appUrls from "unmatched/utils/urls/app-urls";
import icons from "assets/icons/icons";
import { util } from "@unmatchedoffl/ui-core";
import { get, keys, map } from "lodash";

const { Paste } = icons;

interface Counter {
  key: string;
  totalReviewee: number;
  receivingReports: number;
  notReceiving: number;
  reportByWaiver: number;
  group: string;
}

// const COHARTS = [
//   { key: "department", title: "Department" },
//   { key: "location", title: "Location" },
// ];

// const getDeptarmentFact = () =>
//   new Promise((resolve: Function) => {
//     window.setTimeout(() => {
//       resolve();
//     }, 400);
//   });

const ReportCount = (props: any) => {
  const { survey, layout, indexId, metas, globalLoad, tab } = props;
  const tableMeta = useTable({});

  const [metaFields, setMetaFields] = React.useState([
    {
      title: "",
      key: "",
      value: [],
    },
  ]);
  const [selectedMeta, setSelectedMeta] = React.useState<any>({
    cohert: "",
    applied: undefined,
    isSet: false,
  });

  React.useEffect(() => {
    if (!globalLoad) {
      setMetaFields(metas);
      setSelectedMeta({
        ...selectedMeta,
        cohert: metas[0]?.key || "",
        isSet: true,
      });
    }
    //eslint-disable-next-line
  }, [metas, globalLoad]);

  React.useEffect(() => {
    if (!globalLoad && selectedMeta.isSet && tab === TABS.ReportCount) {
      getReportCount();
    }
    //eslint-disable-next-line
  }, [metas, selectedMeta, globalLoad, tab]);

  const getCohertSelected = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return "";
    }
    return (
      metaFields.filter((meta: any) => meta.key === cohert)[0]?.title ?? ""
    );
  };

  const getCohertValues = (cohert: string) => {
    if (cohert === undefined || cohert === null) {
      return [];
    }
    return (
      metaFields.filter((meta: any) => meta.key === cohert)[0]?.value ?? []
    );
  };
  const filters = {
    showUnflagged: false,
    cohart: {
      options: metaFields,
      selected: getCohertSelected(selectedMeta.cohert),
    },
    applied: {
      options: getCohertValues(selectedMeta.cohert),
      selected: selectedMeta.applied,
    },
  };

  const [isLoading, setLoading] = React.useState(false);
  const [departments, setDepartments] = React.useState<Array<Counter>>([]);

  const checkSelected = (item: Counter) => {
    return tableMeta.isSelected(item.key);
  };

  const onDownloadTargetResponses = async () => {
    await targetReturnFact({
      index_id: indexId,
    });
  };

  // const onSearch = (search: string) => {
  //   tableMeta.setSearch(search);
  // };

  // const onSelectDept = (item: Counter) => {
  //   tableMeta.onSelect(item, "empId");
  // };

  // const onCheckAll = (checked: boolean) => {
  //   tableMeta.onSelectAll(departments, checked, "empId");
  // };

  // const onDownload = (departments: Array<Counter>) => {};

  // const onEmail = () => '';

  const getReportCount = async (page?: number, params?: any) => {
    try {
      setLoading(true);
      // tableMeta.setPagination({
      //   page: 0,
      //   totalPages: 0,
      //   size: 10,
      // });
      const reportStats = await reportStatsFact({
        index_id: indexId,
        parent_cat: selectedMeta.cohert,
        page: page || 1,
        page_size: 10,
        ...(ordering && { ordering: params.ordering })
      });
      setDepartments(reportStats.data);
      tableMeta.setPagination({
        page: page || 1,
        totalPages: reportStats.totalPages,
        size: 10,
      });
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  // Header Templates
  const getHeaderMetaTemplate = () => {
    return (
      <>
        {/* <FormControl.Search
          value={tableMeta.search}
          onChange={(e: any) => {
            tableMeta.setSearch(e.target.value);
          }}
          style={{
            width: "400px",
          }}
          onSearch={(value: string) => {
            onSearch(value);
          }}
          placeholder="Search for employee , ID, department.."
        /> */}
      </>
    );
  };

  // TableColumn templates

  // const getCheckAllTemplate = () => (
  //   <FormGroup className="pb-1">
  //     <FormControl.Checkbox>
  //       <FormControl.Checkbox.Input
  //         checked={tableMeta.selectAll}
  //         onChange={(evt: any) => onCheckAll(evt.target.checked)}
  //       />
  //     </FormControl.Checkbox>
  //   </FormGroup>
  // );

  const getColumns = () => {
    return [
      { key: 2, label: "No.", hasSort: false },
      { key: 3, label: getCohertSelected(selectedMeta.cohert), hasSort: false },
      { key: 4, label: "Total Reviewees", hasSort: true, sortValue: "asc",sortKey: "total_reviewee", },
      { key: 6, label: "Receiving Report", hasSort: true, sortValue: "",sortKey: "receiving_reports", },
      { key: 8, label: "Report By Waiver", hasSort: true, sortValue: "",sortKey: "report_by_waiver", },
      { key: 9, label: "Not Receiving Report", hasSort: true, sortValue: "",sortKey: "not_receiving", },
    ];
  };

  const [columnsData, setColumnsData] = React.useState<any>(getColumns()); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL
  const [ordering, setOrdering] = React.useState("");

  const getCompColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  // Table Row Templates

  const getRowsTemplate = () => {
    return departments.map((item: Counter, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={index} selected={checked}>
          {/* <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectDept(item)}
                  onClick={(evt: any) => evt.stopPropagation()}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data> */}
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.group}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.totalReviewee}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.receivingReports}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.reportByWaiver}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.notReceiving}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };
  // if (!departments.length && !isLoading) {
  //   return <ReportsNoDataFound />;
  // }

  return (
    <PageContainer>
      <Header
        survey={survey}
        layout={layout}
        metaTemplate={getHeaderMetaTemplate()}
        isLoading={globalLoad}
        breadcrumbs={[
          {
            label: "Surveys",
            icon: <Paste className="grey-icon__svg" />,
            route: appUrls.admin.survey.default,
          },
          { label: "Manage Survey" },
          { label: survey.name },
        ]}
      />
      <div className="mt-5" />
      {/* tableMeta.selected.length && */}
      <div className="mt-4" />
      <Layout.Container className="pt-2" fluid>
        <Layout.Row>
          <Layout.Col md={12} className="mb-3"></Layout.Col>
          <Layout.Col md={4}>
            <ComboBasicFilter
              cohart={filters.cohart}
              applied={filters.applied}
              onCohartUpdate={(e: string) =>
                setSelectedMeta({
                  ...selectedMeta,
                  cohert: e,
                  applied: undefined,
                })
              }
              onAppliedUpdate={(e: string) =>
                setSelectedMeta({ ...selectedMeta, applied: e })
              }
              isAppliedShown={false}
            />
          </Layout.Col>
        </Layout.Row>
        <Div className="pt-4">
          <Table
            columns={getCompColumns()}
            isLoading={isLoading}
            rows={departments}
            customRows
            render={() => getRowsTemplate()}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            onPageSelect={(d: any) => {
              tableMeta.onPageSelect(d);
              getReportCount(d);
            }}
            onSort={(item: any) => {
              const label = util.label.getSortingLabel(
                item.sortKey,
                item.sortValue
              );
              setColumnsData((_columns: any) => {
                return Object.values(tableMeta.resetColumns(_columns, item));
              });
              setOrdering(label);
              getReportCount(tableMeta.page, { ordering: label });
              // dataCall({ empOrdering: label });
            }}
          />
        </Div>
        <hr />
        <Text.P1 className="text-muted">
          Download a list with total number of responses received by all of the
          reviewees.
        </Text.P1>
        <Button className="mt-2" onClick={onDownloadTargetResponses}>
          Download
        </Button>
      </Layout.Container>
    </PageContainer>
  );
};

export default ReportCount;
