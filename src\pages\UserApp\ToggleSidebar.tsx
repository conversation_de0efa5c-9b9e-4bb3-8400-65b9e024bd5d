import React from "react";
import { setSidebarAction } from "./user-app-store";
import { Button, Icon } from "unmatched/components";
import { useCheckSelector, useDispatch } from "unmatched/hooks";

const ToggleSidebar = () => {
  const sidebar = useCheckSelector((state: any) => state.user.meta.sidebar);
  const margin = useCheckSelector((state: any) => state.user.meta.margin);
  const dispatch = useDispatch();

  return !margin ? (
    <Button
      variant="link"
      className="pt-0 mr-2 fs-20"
      onClick={() => {
        dispatch(setSidebarAction(!sidebar));
      }}
    >
      <Icon icon={"far fa-bars"} variant="dark" />
    </Button>
  ) : null;
};

export default ToggleSidebar;
