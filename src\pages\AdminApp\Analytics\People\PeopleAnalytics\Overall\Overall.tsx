import React, { useEffect } from "react";
import {
  Div,
  Layout,
  // FormGroup,
  // FormControl,
  Text,
  Table,
  Sentiment,
} from "unmatched/components";
import { useTable, useXHR } from "unmatched/hooks";
import DownloadExcel from "../../../DownloadExcel";
// import { GRAPH_DATA } from "./time-series-meta";
import {
  getOverallGraphDataFact,
  getOverallTimeseriesTableFact,
} from "../../people-api";
import useFilter from "pages/CommonFilters/hook";
import util from "unmatched/utils";
import useToastr from "unmatched/modules/toastr/hook";
import Detailed from "./Detailed";
import { cloneDeep } from "lodash";
import TimeSeriesGraphNew from "pages/AdminApp/Analytics/TimeSeriesGraph/TimeSeriesGraphNew";

const Overall = (props: { userId: any; user: any }) => {
  const tableMeta = useTable({});
  const { user } = props;
  const [resources, setResources] = React.useState({
    type: "upward",
    options: ["Overall"],
    legends: [],
    selected: [],
  });
  const [years, setYears]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });
  const [coulmns, setColumns]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });

  const [selectedTimeGraphCompareList, setSelectedTimeGraphCompareList] =
    React.useState<any>({});
  // const [selfYears, setSelfYears]: any = React.useState([]);

  useEffect(() => {
    getGraphDataNew();
  }, [selectedTimeGraphCompareList]);

  const getGraphDataNew = async (filters?: any, type?: string) => {
    graph.setLoading(true);
    const payload = {
      compare_list: Object.values(selectedTimeGraphCompareList),
      // people: [...(filters?.people || []), user.empId],
      rater_groups:
        (filters && filters["rater_groups"]) ||
        (resources.type === "360" ? resources.selected : []),
    };
    return getOverallGraphDataFact(
      user.id,
      payload,
      type || resources.type
    ).then(
      (response: any) => {
        graph.onSuccess(response.graph);
        setResources((_resources: any) => {
          return {
            ..._resources,
            type: type || resources.type,
            options: util.lib.uniq([...resources.options, ...response.groups]),
            legends: response.legends,
          };
        });
        return true;
      },
      (err: any) => {
        toastr.onError(err);
        graph.setLoading(false);
      }
    );
  };

  const toastr = useToastr();

  const graph = useXHR({
    defaultResponse: {
      data: [],
      labels: [],
    },
  });

  const filtersState = useFilter();

  // const getGraphData = async (filters?: any, type?: string) => {
  //   graph.setLoading(true);
  //   const payload = {
  //     ...(filters || {}),
  //     people: [...(filters?.people || []), user.empId],
  //     rater_groups:
  //       (filters && filters["rater_groups"]) ||
  //       (resources.type === "360" ? resources.selected : []),
  //   };
  //   return getOverallGraphDataFact(
  //     user.id,
  //     payload,
  //     type || resources.type
  //   ).then(
  //     (response: any) => {
  //       graph.onSuccess(response.graph);
  //       setResources((_resources: any) => {
  //         return {
  //           ..._resources,
  //           type: type || resources.type,
  //           options: util.lib.uniq([...resources.options, ...response.groups]),
  //           legends: response.legends,
  //         };
  //       });
  //       return true;
  //     },
  //     (err: any) => {
  //       toastr.onError(err);
  //       graph.setLoading(false);
  //     }
  //   );
  // };

  const getTableData = (type: any, isDownload: boolean) => {
    getOverallTimeseriesTableFact(user.id, type, isDownload).then(
      (response: any) => {
        if (!isDownload) {
          setYears((_years: any) => {
            return {
              ..._years,
              [type]: response.data,
            };
          });
          setColumns((_columns: any) => {
            return {
              ..._columns,
              [type]: response.columns,
            };
          });
        }
      }
    );
  };

  const onFilterSelect = (data: any, groupID?: any) => {
    const computedData = Object.entries(data).reduce((acc, [k, v]) => {
      if ((v as any).length) {
        (acc as any)[k] = v;
      }
      return acc;
    }, {});
    setSelectedTimeGraphCompareList((s: any) => {
      let newList = { ...s, [groupID]: computedData };
      newList = Object.entries(newList).reduce((acc, [k, v]) => {
        if (Object.keys(v as any).length) {
          (acc as any)[k] = v;
        }
        return acc;
      }, {});
      return newList;
    });
  };

  const onFilterRemove = (id: string) => {
    const clone = cloneDeep(selectedTimeGraphCompareList);
    delete clone[id];
    setSelectedTimeGraphCompareList(clone);
  };

  // const onFilterSelect_REMOVE = (data: any) => {
  //   getGraphDataNew({
  //     ...data,
  //     people: data.people
  //       ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //       : [user.empId],
  //   });
  // };

  const onResourceChange = (data: any) => {
    getGraphDataNew(
      {
        rater_groups: ["Overall"],
      },
      data
    ).then(() => {
      setResources((_data: any) => {
        return {
          ..._data,
          selected: ["Overall"],
        };
      });
    });
  };

  const onGroupChange = (data: any) => {
    getGraphDataNew({
      rater_groups: data,
    }).then(() => {
      setResources({
        ...resources,
        selected: data,
      });
    });
  };

  const getColumnsData = (type: string) => {
    const output = [
      { key: 1, label: "No.", hasSort: false, disableSelfLabel: true },
      {
        key: 2,
        label: "Year",
        hasSort: false,
        disableSelfLabel: true,
      },
      {
        key: 21,
        label: "Survey Title",
        hasSort: false,
        disableSelfLabel: true,
      },
      {
        key: 3,
        label: "Reviewee Average",
        hasSort: false,
      },
      { key: 41, label: "Firm Average", hasSort: false },
      ...coulmns[type],
      // { key: 5, label: "Department Average", hasSort: false },
      // { key: 6, label: "Practice Group Average", hasSort: false },
      // { key: 7, label: "Title Average", hasSort: false },
      { key: 8, label: "Self Average", hasSort: false, disableSelf: true },
      { key: 9, label: "Comment Sentiment", hasSort: false, disableSelf: true },
    ];
    return type === "self"
      ? output
          .filter((item: any) => !item.disableSelf)
          .map((item: any) => {
            return {
              ...item,
              label: !item.disableSelfLabel ? `${item.label} Self` : item.label,
            };
          })
      : output;
  };

  const getRowsTemplate = (type: any) => {
    // const _list = (!isSelf ? years : selfYears) || [];
    return (years[type] || []).map((item: any, index: number) => {
      return (
        <Table.Row>
          <Table.Data>
            <Text.P1>{index + 1}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.year}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.name}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.revieweeAverage}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firmAverage}</Text.P1>
          </Table.Data>
          {coulmns[type].map((row: any) => {
            return (
              <Table.Data>
                <Text.P1>{item[row.key]}</Text.P1>
              </Table.Data>
            );
          })}
          {/* <Table.Data>{item.departmentAverage}</Table.Data> */}
          {/* <Table.Data>{item.practiceGroupAverage}</Table.Data> */}
          {/* <Table.Data>{item.titleAverage}</Table.Data> */}
          {type !== "self" && (
            <>
              <Table.Data>
                <Text.P1>{item.selfAverage || "-"}</Text.P1>
              </Table.Data>
              <Table.Data>
                <Sentiment sentiment={item.sentiment.toUpperCase()} />
              </Table.Data>
            </>
          )}
        </Table.Row>
      );
    });
  };

  useEffect(() => {
    if (user.id) {
      getTableData("upward", false);
      getTableData("self", false);
      getTableData("360", false);
      getGraphDataNew();
      filtersState.getFilters();
    }
  }, [user]);

  return (
    <Layout.Container fluid className="pt-3">
      <Div>
        <TimeSeriesGraphNew
          // onFilterSelect_REMOVE={onFilterSelect_REMOVE}
          // filtersState={filtersState}
          onFilterSelect={onFilterSelect}
          onFilterRemove={onFilterRemove}
          userName={user.name}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
          resourceTypes={{
            ...resources,
            onChange: onResourceChange,
            onGroupChange,
          }}
        />
      </Div>
      {years.upward?.length > 0 && (
        <Div>
          <DownloadExcel
            hideFilters
            title="Time Series Table - Upward Review"
            onDownload={() => getTableData("upward", true)}
          />
          <Table
            columns={getColumnsData("upward")}
            isLoading={tableMeta.isLoading}
            rows={years["upward"]}
            customRows
            render={() => getRowsTemplate("upward")}
            hasPagination
            activePage={tableMeta.page}
            pages={tableMeta.totalPages}
            onPageSelect={tableMeta.onPageSelect}
          />
        </Div>
      )}
      {years["360"]?.length > 0 &&<Div>
        <DownloadExcel
          hideFilters
          title="Time Series Table - 360 Degree"
          onDownload={() => getTableData("360", true)}
        />
        <Table
          columns={getColumnsData("360")}
          isLoading={tableMeta.isLoading}
          rows={years["360"]}
          customRows
          render={() => getRowsTemplate("360")}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>}
      {years["self"]?.length > 0 && <Div>
        <DownloadExcel
          hideFilters
          title="Time Series Table - Self Assessment"
        />
        <Table
          columns={getColumnsData("self")}
          isLoading={tableMeta.isLoading}
          rows={years["self"]}
          customRows
          render={() => getRowsTemplate("self")}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={tableMeta.onPageSelect}
        />
      </Div>}
      <Div>
        <Detailed userId={user.id} />
      </Div>
    </Layout.Container>
  );
};

Overall.propTypes = {};

export default Overall;
