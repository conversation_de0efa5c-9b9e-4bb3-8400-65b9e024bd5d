import { createSlice } from "@reduxjs/toolkit";

// interface Filter {
//   title: string;
//   variant: string;
//   delay: number;
//   content: string;
// }

// interface StateType {
//   filters: Array<Filter>;
// }

const initialState: any = {
  filters: {},
};

const filterSlice = createSlice({
  name: "commonFilters",
  initialState,
  reducers: {
    set: (state: any, { payload }: any) => {
      state.filters = payload;
    },
    reset: (state: any) => {
      state.filters = [];
    },
  },
});

export const filterReducer = filterSlice.reducer;

export default filterSlice.actions;
