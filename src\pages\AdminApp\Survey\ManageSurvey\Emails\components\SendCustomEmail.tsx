import ModalHeader from "pages/AdminApp/ModalHeader";
import {
  getComputedValue,
  getComputedValueColorSub,
  getComputedValueText,
} from "pages/AdminApp/Survey/CreateSurvey/SendSurvey/SendSurvey";
import React, { useEffect, useState } from "react";
import {
  Div,
  Button,
  Form,
  FormGroup,
  MultiSelect,
  Icon,
  Modal,
  Text,
} from "unmatched/components";
import RichEditor from "unmatched/components/RichEditor";
import useSession from "unmatched/modules/session/hook";
import useToastr from "unmatched/modules/toastr/hook";
import { CUSTOM_EMAIL_INSERT_OPTIONS } from "../../manage-survey-meta";
import {
  recipientsDescMap,
  userFriendlyRecipientsMap,
  userFriendlyTemplatesMap,
} from "../Emails";
import { ConfirmEmailSend } from "./ConfirmEmailSend";
import {
  optionStyles,
  CustomOption,
  targetGroups,
  allRecepients,
  RecipientOption,
  TemplateOption,
} from "./QuickSend";
import Tip from "./Tip";
import { searchUsersFact } from "pages/AdminApp/DataLoad/dataload-api";

const SendCustomEmail = (props: any) => {
  const [template, setTemplate] = useState("CUSTOM");
  const [targetGroupArr, setTargetGroupArr] = useState<any>([
    { value: "CUSTOM", label: "CUSTOM" },
  ]);
  const [subject, setSubject] = useState("");
  const [body, setBody] = useState<any>({ text: "", html: "" });
  const [showModal, setShowModal] = React.useState(false);
  const [recipientsCountArr, setRecipientsCountArr] = React.useState<any>({});

  const { user } = useSession();
  const toastr = useToastr();

  const [loggedInUser, setLoggedInUser] = React.useState<any>({});
  
  useEffect(() => {
    if (user?.email) {
      searchUsersFact("", { search: user.email })
        .then((res) => {
          setLoggedInUser(res.data?.results[0])
        })
        .catch((err) => console.log(err));
    }
  }, [user?.email]);

  useEffect(() => {
    const defTemplate = props.getTemplate("CUSTOM");
    populateEmail(defTemplate);
  }, []);

  useEffect(() => {
    const getCount = async (recipients: any) => {
      const countArray = await props.getRecipientsCountsArr(recipients);
      setRecipientsCountArr(countArray);
    };

    targetGroupArr?.[0]?.value !== "CUSTOM" && getCount(targetGroupArr);
  }, [targetGroupArr]);

  let richTextRef: any = null;
  let subRichTextRef: any = null;

  const setRef = (ref: any) => {
    richTextRef = ref;
  };

  const setSubRef = (ref: any) => {
    subRichTextRef = ref;
  };

  const emailInputProps = {
    closeMenuOnSelect: false,
    isCustom: true,
    onInputChange: props.onInputChange,
    isMulti: true,
    placeholder: "",
    options: props.userOptions,
    CustomOption,
    styles: optionStyles,
    postfix: () =>
      props.selectedEmails.length > 0 ? (
        <span
          onClick={(e: any) => {
            e.stopPropagation();
            props.setSelectedEmails([]);
          }}
        >
          <Icon
            className="mr-2 fw-300 fs-12 cursor-pointer"
            icon="fas fa-times"
          />
        </span>
      ) : null,
    customValContainer: true,
  };

  const onSelect = (users: any) => {
    props.setSelectedEmails(users);
  };

  const populateEmail = (defTemplate: any) => {
    if (defTemplate) {
      setBody({
        text: defTemplate.text,
        body: defTemplate.body,
      });
      setSubject(defTemplate.subject);
      const quillRef = richTextRef.getEditor();
      const subQuillRef = subRichTextRef.getEditor();
      setTimeout(() => {
        quillRef.clipboard.dangerouslyPasteHTML(
          getComputedValue(defTemplate.body, true)
        );

        subQuillRef.clipboard.dangerouslyPasteHTML(
          getComputedValueColorSub(getComputedValue(defTemplate.subject, true))
        );
      }, 100);
    }
  };

  const updateCustomTemplate = async () => {
    const customTemplate = props.getTemplate("CUSTOM");
    customTemplate.subject = getComputedValueText(subject, false);
    customTemplate.body = getComputedValue(body.html, false);
    customTemplate.text = getComputedValueText(body.text, false);
    return props.updateEmailData(customTemplate);
  };

  return (
    <>
      <Div>
        <Div>
          <Form>
            <Div className="d-flex">
              <FormGroup>
                <Div style={{ width: "100%", minWidth: 230 }}>
                  <Div className="d-flex">
                    <FormGroup.Label className="pb-2">
                      Recipients
                    </FormGroup.Label>{" "}
                    <Tip>
                      {allRecepients.map((r: any, i: number) => {
                        return (
                          <Text.P1 key={r}>
                            {i + 1}. {(recipientsDescMap as any)[r]}
                          </Text.P1>
                        );
                      })}
                    </Tip>
                  </Div>
                  <MultiSelect
                    CustomOption={RecipientOption}
                    options={allRecepients.map((v: any) => ({
                      value: v,
                      label: v,
                      disabled:
                        v !== "CUSTOM"
                          ? !!targetGroupArr.find(
                              (tg: any) => tg.value === "CUSTOM"
                            )
                          : allRecepients
                              .filter((r: any) => r !== "CUSTOM")
                              .some((r: any) => {
                                return !!targetGroupArr.find(
                                  (tg: any) => tg.value === r
                                );
                              }),
                    }))}
                    isMulti={true}
                    value={targetGroupArr.map((tg: any) => ({
                      ...tg,
                      label: (userFriendlyRecipientsMap as any)[tg.value],
                    }))}
                    onSelect={async (selected: any) => {
                      setTargetGroupArr(selected);
                    }}
                  />
                </Div>
              </FormGroup>
              <FormGroup className="d-flex ml-4">
                <Div style={{ width: 230 }}>
                  <FormGroup.Label className="pb-2">
                    Use Template
                  </FormGroup.Label>
                  <MultiSelect
                    CustomOption={TemplateOption}
                    options={[
                      ...Object.values(targetGroups)
                        .filter(
                          (v, i) => Object.values(targetGroups).indexOf(v) === i
                        )
                        .map((v: any) => ({
                          value: v,
                          label: (userFriendlyTemplatesMap as any)[v],
                        })),
                      { value: "CUSTOM", label: "CUSTOM" },
                    ]}
                    isMulti={false}
                    closeMenuOnSelect
                    value={{
                      value: template,
                      label: (userFriendlyTemplatesMap as any)[template],
                    }}
                    onSelect={async (selected: any) => {
                      setTemplate(selected.value);
                      const defTemplate = props.getTemplate(selected.value);
                      populateEmail(defTemplate);
                    }}
                  />
                </Div>
              </FormGroup>
            </Div>

            {!!targetGroupArr.find((tg: any) => tg.value === "CUSTOM") &&
              targetGroupArr.length === 1 && (
                <FormGroup>
                  <FormGroup.Label className="pb-2">
                    Enter Email Address
                  </FormGroup.Label>
                  <MultiSelect
                    {...emailInputProps}
                    onSelect={onSelect}
                    hasError={false}
                    wrap
                    placeholder="Search users by email or name"
                    value={props.selectedEmails}
                  />
                </FormGroup>
              )}

            <FormGroup>
              <FormGroup.Label>Email Subject</FormGroup.Label>
              <Div className="survey-quill-subject">
                <RichEditor
                  onChange={(html: string, text: string) => {
                    setSubject(text);
                  }}
                  value={subject}
                  insertOptions={CUSTOM_EMAIL_INSERT_OPTIONS.filter(
                    (el: any) => el.id !== 1 && el.id !== 2
                  )
                    .filter((el: any) => {
                      return !(
                        props.survey?.type === "SurveyIndexEngagement" &&
                        el.id === 7
                      );
                    })
                    .filter((el: any) => {
                      return !(
                        props.survey?.type === "SurveyIndexExit" &&
                        [5, 6, 7, 10].some((sel: any) => sel === el.id)
                      );
                    })
                    .filter((el: any) => {
                      return !(
                        props.survey?.type !== "SurveyIndexExit" && el.id === 11
                      );
                    })}
                  setRef={setSubRef}
                />
              </Div>
            </FormGroup>
            <FormGroup>
              <FormGroup.Label>Email Body</FormGroup.Label>
              <Div className="survey-quill custom-reminder">
                {
                  <RichEditor
                    onChange={(html: string, text: string) => {
                      setBody({ html, text });
                    }}
                    value={body.html}
                    insertOptions={CUSTOM_EMAIL_INSERT_OPTIONS.filter(
                      (el: any) =>
                        !(
                          props.survey?.type === "SurveyIndexEngagement" &&
                          el.id === 7
                        )
                    )
                      .filter((el: any) => {
                        return !(
                          props.survey?.type === "SurveyIndexExit" &&
                          [5, 6, 7, 10].some((sel: any) => sel === el.id)
                        );
                      })
                      .filter((el: any) => {
                        return !(
                          props.survey?.type !== "SurveyIndexExit" &&
                          el.id === 11
                        );
                      })}
                    setRef={setRef}
                  />
                }
              </Div>
            </FormGroup>
          </Form>
        </Div>
      </Div>
      {/* <Note style={{ width: "100% !important" }}>
        <Text.P1 style={{ fontWeight: 500 }}>
          <b>Note :</b> All the{" "}
          <b>
            {targetGroupArr
              .map((tg: any) => (userFriendlyRecipientsMap as any)[tg.value])
              .join(", ")}
          </b>{" "}
          raters of the survey will receive the{" "}
          <b>
            {" "}
            {(userFriendlyTemplatesMap as any)[template]}(
            {(templatesDescMap as any)[template]})
          </b>{" "}
          email.
        </Text.P1>
      </Note> */}
      <Div className="mb-2" />
      <Div style={{ textAlign: "right" }}>
        <Button onClick={() => setShowModal(true)} variant="outline-primary">
          Send Email
        </Button>
      </Div>
      <Modal show={showModal} size="lg" centered>
        <ModalHeader title="Send Email" onHide={() => setShowModal(false)} />
        <Modal.Body>
          <ConfirmEmailSend
            template={template}
            targetGroupArr={targetGroupArr}
            setShowModal={setShowModal}
            recipientsCountArr={recipientsCountArr}
            selectedEmails={props.selectedEmails}
            onSend={async () => {
              await updateCustomTemplate();
              if (
                targetGroupArr?.find((tg: any) => tg.value === "CUSTOM") &&
                targetGroupArr?.length === 1
              ) {
                props.sendCustomEmailTemplate(
                  "CUSTOM",
                  props.selectedEmails.map((usr: any) => usr.id),
                  true
                );
              } else {
                const res = await Promise.all(
                  targetGroupArr.map((tg: any) =>
                    props.sendEmail("CUSTOM", tg.value, false)
                  )
                ).catch((err: any) => console.log(err));
                if (res) {
                  // show toast
                  toastr.onSucces({
                    title: "Success",
                    content: "Email sent Successfully",
                  });
                }
                // props.sendEmail("CUSTOM", targetGroup);
              }
            }}
            onMockSend={async () => {
              await updateCustomTemplate();
              props.sendMockCustomEmail(template, [loggedInUser.id]);
            }}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};

export default SendCustomEmail;
