import React, { useEffect } from "react";
import {
  Text,
  Table,
  FormGroup,
  FormControl,
  Layout,
  Icon,
} from "unmatched/components";
import util from "unmatched/utils";
import { getAllPairingsFact } from "../../../survey-api";
import Header from "../../Header";
import { DateTime } from "luxon";

interface PairList {
  key: string;
  title: string;
  dateAdded: string;
  recordCount: number;
  tags: [];
}
const FILTER_DATA = [
  { key: "Option1", title: "Option1" },
  { key: "Option2", title: "Option2" },
];

export const disableInputs = () => {
  const addParticipantsRoot =
    document
      .querySelector("#add-participants-root")
      ?.getElementsByTagName("*") || [];
  const pagination =
    document.querySelector("#pagination")?.getElementsByTagName("*") || [];
  util.enableDisableTree(addParticipantsRoot, true);
  util.enableDisableTree(pagination, false);
};



export default function AllPairings(props: any) {
  const { selected, onPairSelect, setViewPair, viewOnly } = props;
  const [isLoading, setLoading] = React.useState(false);
  const [users, setUsers] = React.useState<Array<PairList>>([]);
  const [filters, setFilters] = React.useState({
    search: "",
    page: 1,
    totalPages: 0,
    department: {
      options: FILTER_DATA,
      selected: [],
    },
    location: {
      options: FILTER_DATA,
      selected: [],
    },
    level: {
      options: FILTER_DATA,
      selected: [],
    },
  });

  React.useEffect(() => {
    getUsers({});
  }, []);

  useEffect(() => {
    if (viewOnly) disableInputs();
  });

  const getUsers = async (params: any) => {
    try {
      setLoading(true);
      const { results, totalPages } = await getAllPairingsFact(params);
      setUsers(results);
      // tableMeta.updatePagination({ totalPages });
      setFilters((_filters: any) => {
        return {
          ..._filters,
          totalPages,
        };
      });
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };
  const getColumns = () => {
    return [
      { key: 1, label: "", hasSort: false },
      { key: 2, label: "Title", hasSort: true },
      { key: 3, label: "Uploaded On", hasSort: true },
      { key: 4, label: "Total Records", hasSort: true },
      { key: 5, label: "Tags", hasSort: true },
    ];
  };
  const getRows = () => {
    let lUsers = users;
    if (viewOnly) {
      lUsers = lUsers.filter(item => selected && selected.includes(item.key))
    }
    return lUsers.map((item: PairList, index: number) => {
      // const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row
          onClick={() => onPairSelect(item)}
          even={!isEven}
          key={`${item.title}-${item.key}`}
        >
          <Table.Data width="20px">
            <FormGroup>
              <FormControl.Radio>
                <FormControl.Radio.Input
                  name="select"
                  checked={selected && selected.includes(item.key)}
                  onChange={() => ""}
                ></FormControl.Radio.Input>
              </FormControl.Radio>
            </FormGroup>
          </Table.Data>
          <Table.Data>
            <Text.P1
              onClick={() => setViewPair({ key: item.key, name: item.title })}
              className="pointer"
            >
              {item.title ? item.title : <i className="text-muted">No Name</i>}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>
              {DateTime.fromISO(item.dateAdded).toFormat(" MMMM dd, yyyy")}
            </Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.recordCount}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.tags.join(", ")}</Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const onPageSelect = (page: number) => {
    setFilters((_filters) => {
      const params = {
        ...util.lib.pick(_filters, ["search", "page", "totalPages"]),
        page,
      };
      getUsers(params);
      return {
        ..._filters,
        ...params,
      };
    });
  };

  return (
    <>
      <Header
        metaItem={
          <Text.P1>
            {props.isSaving ? (
              <>
                <Icon spin icon="fal fa-spinner-third" /> Saving
              </>
            ) : (
              props.survey?.data?.updatedAt
            )}
          </Text.P1>
        }
        title={<Text.H1 className="pb-2">Add Pairings</Text.H1>}
        breadcrumbs={props.breadcrumbs}
      />
      <Layout.Container style={{ paddingTop: 25 }} fluid>
        <Text.P2 className="mb-3 user-select-none">
          Select a pairings list from the below pairings data sets. All the
          validated pairings in the selected list will be associated with the
          survey.
        </Text.P2>

        <Table
          columns={getColumns()}
          isLoading={isLoading}
          rows={users}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={filters.page}
          pages={filters.totalPages}
          onPageSelect={onPageSelect}
        />
      </Layout.Container>
    </>
  );
}
