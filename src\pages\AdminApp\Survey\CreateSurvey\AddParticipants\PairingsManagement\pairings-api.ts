import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

export const pairingFileUploadFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  const formData = new FormData();
  formData.append("data", data.File);
  formData.append("format", data.Format);
  return axios.post(`${util.apiUrls.PAIRING_UPLOAD}`, formData, config);
};

export const surveyPairingFileUploadFact = (
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  const formData = new FormData();
  formData.append("data", data.File);
  formData.append("format", data.Format);
  formData.append("index_id", data.indexID);
  formData.append("tags", data.tags);
  formData.append("title", data.title);
  return axios.post(`${util.apiUrls.PAIRING_UPLOAD}`, formData, config);
};

export const pairingPatchFileFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PAIRING_UPLOAD}`, config);
};

export const getAllPairFileFact = async (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  const dataCall: AxiosResponse = await axios.get(
    `${util.apiUrls.GET_ALL_PAIR_FILES}`,
    config
  );

  return {
    results: dataCall.data.results,
    totalItems: dataCall.data.count_items,
    totalPages: dataCall.data.count_pages,
  };
};

export const fetchAdminAbstractPairingsFact = (
  id?: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_PAIRINGS_FROM_FILE}`, config);
};

export const fetchSurveyAbstractPairingsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ALL_PAIRINGS_FROM_FILE}`, config);
};

export const fetchRealPairingsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ADMIN_PAIRINGS}`, config);
};

export const fetchPairFileInfoFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.FETCH_PAIRING_FILE_INFO(id)}`, config);
};

export const pairingsFileDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.PAIRINGS_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `pairings_${params.index_id}.${params.f}`);
      document.body.appendChild(link);
      link.click();
    })
    .catch((err: any) => console.log(err));
};

export const getRequiredPairFieldFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_REQUIRED_PAIR_FIELDS}`, config);
};

export const getSamplePairingDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.SAMPLE_PAIRING_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `example.${params.f}`);
      document.body.appendChild(link);
      link.click();
    });
};
export const patchPairUploadFact = (
  id: string,
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PATCH_PAIRING_UPLOAD(id)}`, data, config);
};

export const downloadPairFileInfoFact = async (
  id: string,
  type: string,
  fallback: Function
  // params?: any,
  // meta?: any
) => {
  const getInfo = await fetchPairFileInfoFact(id);
  switch (type) {
    case "DOWNLOAD":
      // return await call(getInfo.data.file)
      return window.open(getInfo.data.file);
    case "ERROR":
      if (getInfo.data.error_log_file) {
        return window.open(getInfo.data.error_log_file);
      } else {
        return fallback();
      }

    default:
      return Error("Invalid");
  }
};

export const deletePairFileFact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.PATCH_PAIRING_UPLOAD(id)}`, config);
};

export const addPairingsBulkFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.ADD_BULK_PAIRING}`, data, config);
};

export const addRealPairingsFileFact = (
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.ADD_BULK_PAIRING}`, data, config);
};

export const validatePairingFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.VALIDATE_PAIRING}`, data, config);
};

export const searchUsersFact = (id?: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_USERS}`, config);
};

export const getUserExitSurvey = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.get(`${util.apiUrls.EXIT_PARTIPANT}`, config);
};
export const addUserToExitSurvey = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.EXIT_PARTIPANT}`, data, config);
};

export const deleteUserToExitSurvey = (
  id: number,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.delete(`${util.apiUrls.EXIT_PARTIPANT_ACTION(id)}`, config);
};

export const updateUserToExitSurvey = (
  id: number,
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.patch(`${util.apiUrls.EXIT_PARTIPANT_ACTION(id)}`, data, config);
};

export const generatePairingFact = ({
  data,
  params,
  meta,
}: {
  data: { survey_index: string };
  params?: any;
  meta?: any;
}) => {
  //
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "application/json",
    },
  });
  return axios.post(`${util.apiUrls.AUTO_GENERATE_PAIR}`, data, config);
};
