import axios from "axios";
import util from "unmatched/utils";

export const createAFAQ = (data: any, params?: any, meta?: any) => {
  const reqData = {
    label: data.question,
    answer: data.answer,
  };
  const config = util.api.getConfigurations(params, {
    ...meta,
  });

  return axios.post(`${util.apiUrls.FAQ}`, { ...reqData }, config);
};

export const getAllFAQs = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.FAQ}`, config).then((response) => {
    const { data } = response;
    return data.map((faq: any) => {
      return {
        id: faq.id,
        question: faq.label,
        answer: faq.answer,
      };
    });
  });
};

export const patchAFAQ = (id: string, data: any, params?: any, meta?: any) => {
  const reqData = {
    label: data.question,
    answer: data.answer,
  };
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.FAQ_OPERATION(id)}/`, reqData, config);
};

export const deleteAFAQ = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.FAQ_OPERATION(id)}`, config);
};

export const saveContacts = (
  { takerContacts = [] }: any,
  params?: any,
  meta?: any
) => {
  const reqData = takerContacts.map((tc: any) => ({
    full_name: tc.fullName,
    contact_email: tc.email,
    contact_number: tc.phone,
    address: tc.title,
    description: tc.text,
    is_active: true,
  }));
  const config = util.api.getConfigurations(params, {
    ...meta,
  });

  const promises = reqData.map((rd: any) =>
    axios.post(`${util.apiUrls.POINT_OF_CONTACTS}`, rd, config)
  );

  return Promise.all(promises);
};

export const deleteContact = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.POINT_OF_CONTACTS}/${id}`, config);
};

export const getAllContacts = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });

  return axios
    .get(`${util.apiUrls.POINT_OF_CONTACTS}`, config)
    .then((response) => {
      const { data } = response;

      return data.map((contact: any) => {
        return {
          fullName: contact.full_name,
          email: contact.contact_email,
          phone: contact.contact_number,
          title: contact.address,
          text: contact.description,
        };
      });
    });
};
