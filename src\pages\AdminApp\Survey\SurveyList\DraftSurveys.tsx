import React from "react";
import _ from "lodash";
import {
  Table,
  Icon,
  Text,
  Div,
  Dropdown,
  Button,
  Modal,
  OverlayTrigger,
} from "unmatched/components";
import useToastr from "unmatched/modules/toastr/hook";
import { useTable, useXHR, useHistory } from "unmatched/hooks";
import util from "unmatched/utils";
import {
  cloneSurveyFact,
  get360SurveysFact,
  getDraftSurveysFactV2,
} from "../survey-api";
import { DateTime } from "luxon";
import styled from "styled-components";
import { Tooltip } from "react-bootstrap";

const DraftSurveys = (props: any) => {
  const { search, isActive, filter, resourceType } = props;

  const [ordering, setOrdering] = React.useState("-updated_at");
  const history = useHistory();
  const toastr = useToastr();
  const surveyData = useXHR({ defaultResponse: [] }, "surveys");
  const [collapse, setCollapse] = React.useState({});

  const tableMeta = useTable({
    totalPages: 0,
    size: 15,
    page: 1,
  });

  React.useEffect(() => {
    const page = 1;
    tableMeta.onPageSelect(page);
    if (isActive) getSurveys({ page });
  }, [search, isActive, resourceType]);

  const [columnsData, setColumnsData] = React.useState<any>({
    sNo: { label: "No." },
    title: { label: "Title", hasSort: true, sortValue: "", sortKey: "title" },
    startDate: {
      label: "Start Date",
      hasSort: true,
      sortValue: "",
      sortKey: "start",
    },
    endDate: {
      label: "End Date",
      hasSort: true,
      sortValue: "",
      sortKey: "deadline",
    },
    type: { label: "Type", hasSort: false, sortValue: "" },
    actions: { label: "Actions" },
  });

  const [cloneModal, setCloneModal] = React.useState<any>({
    show: false,
    survey: null,
  });
  const getColumns = () => {
    const coumnsList = _.keys(columnsData);
    return _.map(coumnsList, (key: string) => ({
      ..._.get(columnsData, key),
      key,
    }));
  };

  const getSurveys = (params?: any) => {
    surveyData.setLoading(true);
    const lParams = {
      search,
      filter,
      page: tableMeta.page,
      size: tableMeta.size,
      ordering,
      resource_type: resourceType,
      ...(params || {}),
    };

    const onSuccess = ({ data, totalPages, totalElements }: any) => {
      tableMeta.updatePagination({ totalPages, totalItems: totalElements });
      surveyData.setLoading(false);
      surveyData.setSurveys(data);
    };

    const onError = (err: any) => {
      surveyData.onError(err);
      toastr.onError(err);
    };

    if (resourceType === "SurveyIndex360") {
      get360SurveysFact(lParams, surveyData.getMetaConfig()).then(
        onSuccess,
        onError
      );
    } else {
      getDraftSurveysFactV2(lParams, surveyData.getMetaConfig()).then(
        onSuccess,
        onError
      );
    }
  };

  const isCollapsed = (id: number) => {
    return util.lib.get(collapse, id);
  };

  const toggleCollapse = (id: number) => {
    setCollapse((_collapse: any) => ({
      ..._collapse,
      [id]: !_collapse[id],
    }));
  };

  const getVersionsTemplate = (_survey: any, hasVersions: boolean) => {
    const isResourceType360 = resourceType === "SurveyIndex360";
    const { versions, id } = _survey;
    if (hasVersions && isCollapsed(id)) {
      return versions.map((item: any, index: number) => {
        return (
          <Table.Row
            {...(resourceType === "SurveyIndex360" && {
              onClick: () => {
                history.push(
                  util.appUrls.admin.survey.create.getUpwardReviewUrl(item.id)
                );
              },
            })}
            key={item.id}
            even
          >
            {!index && <Table.Data rowSpan={versions.length}></Table.Data>}
            <Table.Data>
              <Text.P1>{item.title}</Text.P1>
            </Table.Data>
            <Table.Data>
              {isResourceType360 ? (
                <Text.P1>
                  {DateTime.fromISO(item.startDate).toFormat(
                    " LLL dd, yyyy, hh:mm a"
                  )}
                </Text.P1>
              ) : (
                "-"
              )}
            </Table.Data>
            <Table.Data>
              {isResourceType360 ? (
                <Text.P1>
                  {DateTime.fromISO(item.endDate).toFormat(
                    " LLL dd, yyyy, hh:mm a"
                  )}
                </Text.P1>
              ) : (
                "-"
              )}
            </Table.Data>
            <Table.Data>
              <Text.P1>
                {isResourceType360 ? "360 Degree Feedback Survey" : "-"}
              </Text.P1>
            </Table.Data>
            <Table.Data>{renderActions(item)}</Table.Data>
          </Table.Row>
        );
      });
    }
  };

  const getSurveyType = (type: string) => {
    const types: any = {
      SurveyIndexUpward: "Upward Feedback",
      SurveyIndexEngagement: "Engagement Survey",
      SurveyIndexSelf: "Self Assessment Survey",
      SurveyIndex360: "360 Degree Feedback",
    };
    return types[type] ?? "";
  };

  const cloneSurvey = (id: string) => {
    cloneSurveyFact({ id }).then(
      ({ data }: any) => {
        toastr.onSucces({
          title: "Success",
          content: "Survey cloned successfully.",
        });
        history.push(
          util.appUrls.admin.survey.create.getUpwardReviewUrl(data.id)
        );
      },
      (err: any) => {
        toastr.onError(err);
      }
    );
  };

  const renderActions = (item: any) => {
    return (
      <Div className="d-flex">
        <OverlayTrigger
          placement="top"
          overlay={
            <Tooltip id="`tooltip-bottom" className="fs-10">
              Edit
            </Tooltip>
          }
        >
          <Div
            onClick={(e: any) => {
              e.stopPropagation();
              history.push(
                util.appUrls.admin.survey.create.getUpwardReviewUrl(item.id)
              );
            }}
            className="text-left text-truncate"
            style={{ maxWidth: 300 }}
          >
            <Icon
              className="fs-14"
              icon="fal fa-pencil-alt text-primary mr-2"
            />
          </Div>
        </OverlayTrigger>
        <OverlayTrigger
          placement="top"
          overlay={
            <Tooltip id="`tooltip-bottom" className="fs-10">
              Clone
            </Tooltip>
          }
        >
          <Div
            onClick={(e: any) => {
              e.stopPropagation();
              setCloneModal({ show: true, survey: item.title, id: item.id }); //cloneSurvey(item.id)
            }}
            className="text-left text-truncate"
            style={{ maxWidth: 300 }}
          >
            <Icon className="fs-14" icon="fal fa-clone text-primary mr-2" />
          </Div>
        </OverlayTrigger>
      </Div>
    );
    return (
      <Dropdown
        onClick={(e: any) => e.stopPropagation()}
        style={{ position: "absolute" }}
      >
        <OptionButton
          className="d-flex align-items-center justify-content-center btn-block"
          id={"dropdown-button-drop-start"}
        >
          <Icon icon="fal fa-ellipsis-h mr-1" />
        </OptionButton>

        <Dropdown.Menu
          alignRight
          className="text-left shadow-lg"
          style={{ zIndex: 1 }}
        >
          <Dropdown.Item
            className="fs-12 px-3"
            onClick={() =>
              history.push(
                util.appUrls.admin.survey.create.getUpwardReviewUrl(item.id)
              )
            }
          >
            <Icon icon="fal fa-pencil-alt text-primary mr-1" /> Edit
          </Dropdown.Item>
          <Dropdown.Item
            className="fs-12 px-3"
            onClick={
              () =>
                setCloneModal({ show: true, survey: item.title, id: item.id }) //cloneSurvey(item.id)
            }
          >
            <Icon icon="fal fa-clone text-primary mr-1" /> Clone
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    );
  };

  const renderRows = () => {
    return surveyData.surveys.map((item: any, index: number) => {
      const isCollapse = isCollapsed(item.id);
      const hasVersions = !!(item.versions && item.versions.length);
      const isResourceType360 = resourceType === "SurveyIndex360";
      const isEven = util.math.isEven(index);

      return (
        <React.Fragment key={item.id}>
          <Table.Row
            onClick={() =>
              history.push(
                util.appUrls.admin.survey.create.getUpwardReviewUrl(item.id)
              )
            }
            even={isEven}
          >
            <Table.Data width="30px">
              <Text.P1
                onClick={() =>
                  history.push(
                    util.appUrls.admin.survey.create.getUpwardReviewUrl(item.id)
                  )
                }
              >
                {tableMeta.getIndex(index)}
              </Text.P1>
            </Table.Data>
            <Table.Data>
              <Div className="d-flex">
                <Text.P1
                  onClick={() =>
                    history.push(
                      util.appUrls.admin.survey.create.getUpwardReviewUrl(
                        item.id
                      )
                    )
                  }
                >
                  {item.title}
                </Text.P1>
                {hasVersions && (
                  <Div className="ml-auto align-self-start">
                    <Button
                      className="py-0 text-dark fs-16"
                      variant="link"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        toggleCollapse(item.id);
                      }}
                    >
                      <Icon
                        icon={
                          !isCollapse
                            ? "fas fa-caret-right"
                            : "fas fa-caret-down"
                        }
                      />
                    </Button>
                  </Div>
                )}
              </Div>
            </Table.Data>
            <Table.Data width="190px">
              {!isResourceType360 && (
                <Text.P1>
                  {DateTime.fromISO(item.startDate).toFormat(
                    " LLL dd, yyyy, HH:mm"
                  )}
                </Text.P1>
              )}
            </Table.Data>
            <Table.Data width="190px">
              {!isResourceType360 && (
                <Text.P1>
                  {DateTime.fromISO(item.endDate).toFormat(
                    " LLL dd, yyyy, HH:mm"
                  )}
                </Text.P1>
              )}
            </Table.Data>
            <Table.Data width="280px">
              <Text.P1>
                {isResourceType360
                  ? "360 Degree Survey Group"
                  : getSurveyType(item.type)}
              </Text.P1>
            </Table.Data>
            <Table.Data width="100px">
              {!isResourceType360 && renderActions(item)}
            </Table.Data>
          </Table.Row>
          {getVersionsTemplate(item, hasVersions)}
        </React.Fragment>
      );
    });
  };

  return (
    <Div className="pt-4">
      <Table
        columns={getColumns()}
        isLoading={surveyData.isLoading}
        rows={surveyData.surveys}
        type="bordered"
        customRows
        // onRowClick={(item: any) => {
        //   history.push(
        //     util.appUrls.admin.survey.create.getUpwardReviewUrl(item.id)
        //   );
        // }}
        render={renderRows}
        onSort={(item: any) => {
          const label = util.label.getSortingLabel(
            item.sortKey,
            item.sortValue
          );
          setColumnsData((_columns: any) =>
            tableMeta.resetColumns(_columns, item)
          );
          setOrdering(label);
          getSurveys({ ordering: label, search });
        }}
        onPageSelect={(number: number) => {
          tableMeta.onPageSelect(number);
          getSurveys({ page: number });
        }}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        size={tableMeta.size}
        totalItems={tableMeta.totalItems}
        {...(search && { notFoundMsg: util.noSearchRecordsFoundMsg })}
      />
      <Modal show={cloneModal.show} centered>
        <Modal.Header className="py-3">
          <Modal.Title>
            <Text.H3>Clone Survey</Text.H3>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="pb-5">
          <Text.P1>
            Are you sure, you want to clone <b>{cloneModal.survey}</b>?
          </Text.P1>
        </Modal.Body>
        <Modal.Footer className="py-2">
          <Button className="fs-12" onClick={() => cloneSurvey(cloneModal.id)}>
            Yes
          </Button>
          <Button
            className="fs-12"
            variant="outline-danger"
            onClick={() => setCloneModal({ show: false, survey: null })}
          >
            No
          </Button>
        </Modal.Footer>
      </Modal>
    </Div>
  );
};

// DraftSurveys.propTypes = {

// }

export default DraftSurveys;

const OptionButton = styled(Dropdown.Toggle)`
  height: 18px;
  background: none !important;
  color: #000000 !important;
  padding: 0;
  border: none;
  position: relative;
  z-index: 0;
  &:focus,
  &:hover,
  &:active {
    background: none;
    color: #000 !important;
    outline: none;
    box-shadow: none !important;
  }
`;
