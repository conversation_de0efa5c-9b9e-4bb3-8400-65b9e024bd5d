import { combineReducers } from "redux";
import util from "unmatched/utils";

const { generateReducer, setState } = util.store;

const initialState = {
  sidebarWidth: 0,
  sidebar: true,
  margin: true,
};

// Selectors

export const userMetadataSelector = (state: any) => state.user.meta;

// Actions

const TYPES = {
  SET_SIDEBAR: "/user/sidebar",
  SET_MARGIN: "/user/margin",
};

export const setSidebarAction = (sidebar: boolean) => {
  return { type: TYPES.SET_SIDEBAR, payload: sidebar };
};

export const setMarginAction = (margin: boolean) => {
  return { type: TYPES.SET_MARGIN, payload: margin };
};

// Generate Reducers

export default combineReducers({
  meta: generateReducer(
    {
      [TYPES.SET_SIDEBAR]: (state: any, sidebar: boolean) =>
        setState(state, {
          sidebar,
        }),
      [TYPES.SET_MARGIN]: (state: any, margin: boolean) =>
        setState(state, {
          margin,
        }),
    },
    initialState
  ),
});
