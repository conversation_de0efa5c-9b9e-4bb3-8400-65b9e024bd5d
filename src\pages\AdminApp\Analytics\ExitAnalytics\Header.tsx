// import React from 'react';
import styled from "styled-components";
import { Div } from "unmatched/components";
// import PropTypes from 'prop-types';

const Header = styled(Div).attrs({
  className: "bg-light px-3 pt-5 pb-4 border-bottom",
})``;

// const Header = (props: any) => {
//   return (
//     <Div className="bg-light px-4 py-5">

//     </Div>
//   )
// }

// Header.propTypes = {

// }

export default Header;
