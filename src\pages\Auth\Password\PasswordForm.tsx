import React, { useRef, useState } from "react";
import {
  FormControl,
  FormGroup,
  Layout,
  Overlay,
  Tooltip,
  Text,
  Icon,
  Div,
} from "unmatched/components";
import styled from "styled-components";
import util from "unmatched/utils";

const { isFieldInvalid, isFieldValid } = util.formik;

export const StyledIcon = styled(Icon)`
  position: absolute;
  right: 44px;
  margin-top: -23px;
`;

// const ValidationTooltip = () => { };

const getValidationLine = (valid: boolean, text: string) => {
  const { className, icon } = !valid
    ? {
        icon: "far fa-times",
        className: "text-danger",
      }
    : {
        icon: "far fa-check",
        className: "text-success",
      };
  return (
    <Div className="d-flex">
      <Div>
        <Icon icon={icon} className={className} />
      </Div>
      <Text.P2 className="pl-2">{text}</Text.P2>
    </Div>
  );
};

export default function PasswordForm(props: {
  formik: any;
  onPasswordChange: Function;
  tooltip: any;
  colXL?: number;
}) {
  const { formik, onPasswordChange, tooltip, colXL = 5 } = props;
  const passwordRef = useRef();
  const [showPass, setShowPass] = useState(false);

  const isMatch = () => {
    const { values } = formik;
    return values.password === values.confirmPassword;
  };

  const getConfirmPasswordFeedback = () => {
    const { touched, errors } = formik;
    if (!touched.confirmPassword) return "";
    if (errors.confirmPassword) return errors.confirmPassword;
    if (!isMatch()) return "Passwords do not match";
  };
  const PassOrText = showPass ? FormControl.Text : FormControl.Password;
  return (
    <>
      <Layout.Row>
        <Layout.Col xl={colXL}>
          <FormGroup>
            <FormGroup.Label>Password</FormGroup.Label>
            <PassOrText
              ref={passwordRef}
              name="password"
              isInvalid={isFieldInvalid(formik, "password")}
              isValid={isFieldValid(formik, "password")}
              onBlur={formik.handleBlur}
              onChange={(event: any) => {
                if (onPasswordChange) {
                  onPasswordChange(event);
                } else {
                  formik.handleChange(event);
                }
              }}
              value={formik.values?.password || ""}
              placeholder="Password"
            />
            <Div onClick={() => setShowPass((s) => !s)}>
              <StyledIcon icon="fas fa-eye" />
            </Div>
            <Overlay
              target={() => passwordRef.current || null}
              show={isFieldInvalid(formik, "password")}
              placement="right"
            >
              {(props) => (
                <Tooltip id="password" {...props}>
                  <Div className="p-2 text-left text-white">
                    <Text.P2 className="pb-1">Password needs to have:</Text.P2>
                    {getValidationLine(tooltip.min, "At least 8 characters")}
                    {getValidationLine(tooltip.alphabet, "At least 1 letter")}
                    {getValidationLine(tooltip.number, "At least 1 number")}
                    {tooltip.same !== undefined &&
                      getValidationLine(
                        tooltip.same,
                        "Cannot be same as old password"
                      )}
                  </Div>
                </Tooltip>
              )}
            </Overlay>
          </FormGroup>
        </Layout.Col>
      </Layout.Row>
      <Layout.Row className="py-3">
        <Layout.Col xl={colXL}>
          <FormGroup>
            <FormGroup.Label>Confirm password</FormGroup.Label>
            <FormControl.Password
              name="confirmPassword"
              isInvalid={isFieldInvalid(formik, "confirmPassword")}
              isValid={isFieldValid(formik, "confirmPassword")}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              placeholder="Re-enter password"
            />
            <FormGroup.InValidFeedback text={getConfirmPasswordFeedback()} />
          </FormGroup>
        </Layout.Col>
      </Layout.Row>
    </>
  );
}
