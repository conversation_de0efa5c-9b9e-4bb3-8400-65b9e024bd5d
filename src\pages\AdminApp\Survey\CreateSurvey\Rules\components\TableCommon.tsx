import React, { useEffect, useState } from "react";
import useToastr from "unmatched/modules/toastr/hook";
import { useDebounce, useTable } from "unmatched/hooks";
import {
  Text,
  Layout,
  Button,
  Div,
  Table,
  FormControl,
  FormGroup,
  Confirmation,
} from "unmatched/components";
import ViewUserDetails from "./ViewUserDetails";
import { useParams } from "react-router-dom";
import { ComboBasicFilter, util } from "@unmatchedoffl/ui-core";
import { get, keys, map } from "lodash";
import _ from "lodash";
import {
  deletePossibleRatersFact,
  getPossibleRaters,
} from "pages/AdminApp/Survey/survey-api";
import ICONS from "assets/icons/icons";
import { useLocalFilter } from "pages/CommonFilters/useLocalFilters";

interface TableCommon {
  key: string;
  firstName: string;
  lastName: string;
  empId: string;
  email: string;
  location?: string;
  department?: string;
  results?: Array<any>;
  metadata?: any;
  id?: any;
}

enum ModalState {
  VIEW = "VIEW",
  MODIFY = "MODIFY",
}

const TableCommon = (props: any) => {
  const { showToast } = useToastr();
  const tableMeta = useTable({});
  const { id } = useParams<any>();
  const [searchDirty, setSearchDirty] = useState(false);
  // setMetas
  const [metas, setMetas] = React.useState<any>([]);
  const [selectedMeta, setSelectedMeta] = React.useState<any>({
    cohort: "",
    applied: undefined,
    isSet: false,
  });
  const getFilters = () => {
    filtersState.getFilters((_filters: any) => {
      const arr: any = [];
      _.forEach(_filters, (values: any, key: any) => {
        arr.push({
          title: values.label,
          key: key,
          value: values.values.map((value: any) => {
            return { key: value, title: value };
          }),
        });
      });
      setMetas(arr);
      setSelectedMeta({
        ...selectedMeta,
        cohort: arr[0]?.key || "",
        isSet: true,
      });
    });
  };
  const filtersState = useLocalFilter(id, undefined, props.filter === 'raters' ? 'rater' : 'target');

  const [selectAll, setSelectAll] = React.useState(false);
  const [ordering, setOrdering] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(true);
  const [users, setUsers] = React.useState<any>([]);
  const [search, setSearch] = React.useState("");
  const [confirmDelete, setConfirmDelete] = useState<any>(null);
  const [confirmDeleteBulk, setConfirmDeleteBulk] = useState<any>(null);
  // setFilters
  // const [filters] = React.useState<any>({});
  const [selected, setSelected] = React.useState<Array<string>>([]);
  const [selectedUser, setSelectedUser] = React.useState({
    empId: "",
    state: ModalState.VIEW,
  });
  React.useEffect(() => {
    getFilters();
  }, [props.filter]);

  React.useEffect(() => {
    if (users) {
      setColumnsData(getColumns());
    }
  }, [users?.length]);

  useEffect(() => {
    props.refreshRatersTable && getEmployees(undefined, true);
  }, [props.refreshRatersTable]);

  useEffect(() => {
    getEmployees();
  }, [props.filter]);

  const getEmployees = async (params?: any, endLoading?: boolean) => {
    try {
      setIsLoading(true);
      const response = await getPossibleRaters(
        {
          page_size: 10,
          search,
          selectedTab: props.filter,
          ...(params && { ...params }),
        },
        id
      );
      setUsers(response.results);
      tableMeta.updatePagination({
        totalPages: response?.totalPages, // deepscan-disable-line INSUFFICIENT_NULL_CHECK
        page: 1,
        totalItems: response?.totalElements,
      });
      props.setRefreshRatersTable(false);
      endLoading && setIsLoading(false);
    } catch (err) {
      console.log(err);
      setIsLoading(false);
    }
  };

  const getUserByEmpId = (empId: string) => {
    return empId ? users.find((item: any) => item.emp_Id === empId) : {};
  };

  const onDeleteBulkUsers = () => {
    deletePossibleRaters(selected, () => {
      setSelected([]);
      setSelectAll(false);
    });
  };

  useEffect(() => {
    setColumnsData(getColumns());
  }, [selectAll]);

  const onCheckAll = (checked: boolean) => {
    setSelectAll(checked);
    setSelected(checked ? users.map((item: any) => item.id) : []);
  };

  const onSelectUser = (item: TableCommon) => {
    if (checkSelected(item)) {
      setSelected((_selected) => {
        const copy = _selected;
        const index = copy.findIndex((o) => o === item.id);
        copy.splice(index, 1);
        return [...copy];
      });
    } else {
      setSelected((_selected) => {
        const copy = _selected;
        copy.push(item.id);
        return [...copy];
      });
    }
  };

  const checkSelected = (item: TableCommon) => {
    return selected.includes(item.id);
  };

  const onSearch = (term: string) => {
    setSearch(term);
  };

  const onModalHide = () => {
    setSelectedUser({
      empId: "",
      state: ModalState.VIEW,
    });
  };

  const onPageSelect = async (page: number) => {
    try {
      setIsLoading(true);
      const obj = {};
      const response = await getPossibleRaters(
        {
          page_size: 10,
          page,
          search: search.length > 0 ? search : undefined,
          selectedTab: props.filter,
          [selectedMeta.cohort]: selectedMeta.applied,
          ...obj,
          ...(ordering && { ordering }),
        },
        id
      );
      setUsers(response?.results);
      tableMeta.updatePagination({
        totalPages: response?.totalPages,
        page,
      });
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  useDebounce(
    () => {
      searchDirty && onPageSelect(1);
    },
    500,
    [search]
  );

  useDebounce(
    () => {
      if (selectedMeta.applied === undefined && selectedMeta.isSet === false) {
        return;
      }
      onPageSelect(1);
    },
    500,
    [selectedMeta]
  );

  const getColumns = () => {
    const metasColums = () => {
      const arr: any = [];
      metas.map((item: any) => {
        if (item.key) {
          return arr.push({ key: item.key, label: item.title, hasSort: false });
        } else return 0;
      });
      return arr;
    };
    const basic = [
      {
        key: 1,
        renderItem: () => (
          <FormGroup className="pb-1">
            <FormControl.Checkbox>
              <FormControl.Checkbox.Input
                checked={selectAll}
                onChange={(evt: any) => onCheckAll(evt.target.checked)}
              />
            </FormControl.Checkbox>
          </FormGroup>
        ),
        hasSort: false,
      },
      { key: 2, label: "No.", hasSort: false },
    ];

    return [
      ...basic,
      {
        key: 3,
        label: "First Name",
        hasSort: true,
        sortValue: "",
        sortKey: "first_name",
      },
      {
        key: 4,
        label: "Last Name",
        hasSort: true,
        sortValue: "",
        sortKey: "last_name",
      },
      {
        key: 5,
        label: "Employee ID",
        hasSort: true,
        sortValue: "",
        sortKey: "emp_id",
      },
      {
        key: 6,
        label: "Email",
        hasSort: true,
        sortValue: "",
        sortKey: "email",
      },
      ...metasColums(),
      {
        key: 7,
        label: "Actions",
        hasSort: false,
      },
    ];
  };

  useEffect(() => {
    const newCols = getColumns();
    setColumnsData(newCols);
  }, [metas]);

  const [columnsData, setColumnsData] = React.useState<any>(getColumns()); // deepscan-disable-line REFERENCE_BEFORE_LEXICAL_DECL

  const getCompColumns = () => {
    let columnsList = keys(columnsData);
    columnsList = map(columnsList, (key: string) => ({
      ...get(columnsData, key),
      key,
    }));
    return columnsList;
  };

  const getMetaColumns = () => {
    const arr: any = [];
    metas.map((item: any) => {
      if (item.key) {
        return arr.push(item.key);
      } else return 0;
    });
    return arr;
  };

  const deletePossibleRaters = async (ids: any, cb?: any) => {
    const res = await deletePossibleRatersFact(
      typeof ids !== "number" ? ids : [ids],
      id,
      props.filter
    );
    if (res?.status === 200) {
      showToast({
        variant: "success",
        title: "Users deleted successfully",
        content: "Users were deleted successfully",
      });
      getEmployees(undefined, true);
      cb?.();
    }
  };

  const getRows = () => {
    return users.map((item: TableCommon, index: number) => {
      const checked = checkSelected(item);
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.key} selected={checked}>
          <Table.Data width="30px">
            <FormGroup>
              <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  onChange={() => onSelectUser(item)}
                  checked={checked}
                />
              </FormControl.Checkbox>
            </FormGroup>
          </Table.Data>
          <Table.Data width="70px">
            <Text.P1>{tableMeta.page * 10 - 10 + index + 1}.</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.firstName}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.lastName}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.empId}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Text.P1>{item.email}</Text.P1>
          </Table.Data>
          {getMetaColumns().map((_i: any) => (
            <Table.Data key={_i}>
              <Text.P1>
                {item?.metadata && item?.metadata[_i]
                  ? item?.metadata[_i]
                  : "-"}
              </Text.P1>
            </Table.Data>
          ))}
          <Table.Data>
            {confirmDelete === item.email && (
              <Confirmation
                confirmText={`Are you sure you want to delete this user ${item.email}?`}
                show={confirmDelete}
                onOk={async () => {
                  deletePossibleRaters(item.id);
                }}
                setShow={setConfirmDelete}
              />
            )}
            <Text.P1
              onClick={() => setConfirmDelete(item.email)}
              className="cursor-pointer"
            >
              <ICONS.CrossButton width={20} height={20} />
            </Text.P1>
          </Table.Data>
        </Table.Row>
      );
    });
  };

  const getViewModifyTemplate = () => {
    const canView: boolean =
      !!selectedUser.empId && selectedUser.state === ModalState.VIEW;
    const userData: any = getUserByEmpId(selectedUser.empId);

    return (
      <>
        {canView && (
          <ViewUserDetails
            show={canView}
            user={userData}
            onModify={() => {
              setSelectedUser({
                empId: userData.empId,
                state: ModalState.MODIFY,
              });
            }}
            onHide={onModalHide}
          />
        )}
      </>
    );
  };

  const getCohortSelected = (cohort: string) => {
    if (cohort === undefined || cohort === null) {
      return "";
    }
    return metas.filter((meta: any) => meta.key === cohort)[0]?.title ?? "";
  };

  const getCohortValues = (cohort: string) => {
    if (cohort === undefined || cohort === null) {
      return [];
    }
    return metas.filter((meta: any) => meta.key === cohort)[0]?.value ?? [];
  };

  const filters = {
    showUnflagged: false,
    cohort: {
      options: metas,
      selected: getCohortSelected(selectedMeta.cohort),
    },
    applied: {
      options: getCohortValues(selectedMeta.cohort),
      selected: selectedMeta.applied,
    },
  };

  const getFilterLayout = () => {
    return (
      <>
        
        <Layout.Row className="py-3">
          
          <Layout.Col lg={5} md={5}>
            <ComboBasicFilter
              cohart={filters.cohort}
              applied={filters.applied}
              onCohartUpdate={(e: string) => {
                setSelectedMeta({
                  ...selectedMeta,
                  cohort: e,
                  applied: undefined,
                });
              }}
              onAppliedUpdate={(e: any) =>
                setSelectedMeta({ ...selectedMeta, applied: e.title })
              }
              isAppliedShown={true}
            />
            
            {filters.applied?.selected && (
              <Div
                className="cursor-pointer ml-3"
                style={{
                  fontSize: 12,
                  color: "red",
                  position: "absolute",
                  right: "-33px",
                  top: 4,
                  zIndex: 2,
                }}
                onClick={() => {
                  setSelectedMeta({
                    ...selectedMeta,
                    applied: undefined,
                  });
                }}
              >
                Clear
              </Div>
            )}
          </Layout.Col>

          <Layout.Col
            className="d-flex flex-row justify-content-end"
            lg={7}
            md={7}
          >
            <FormControl.Search
              value={search}
              onChange={(e: any) => {
                setSearchDirty(true);
                setSearch(e.target.value);
              }}
              onSearch={(value: string) => {
                onSearch(value);
              }}
              placeholder="Search for name, email or emp id"
            />
          </Layout.Col>
        </Layout.Row>
      </>
    );
  };

  return (
    <Div className="bg-white pb-5">
      <>
        <Div className="separating-b border-bottom mt-3 mb-2" />
        <Div className="section-title mb-2">
          {props.filter === "raters" ? "Reviewers" : "Reviewees"} List
        </Div>
        <Text.P1 className="mb-2">
          {props.filter === "raters"
            ? "This is the list of all employees who can be reviewers in the survey."
            : "This is the list of all employees who can be reviewees in the survey."}
        </Text.P1>
        {selected.length === 0 && getFilterLayout()}
        {selected.length > 0 && (
          <Layout.Row className="pb-3 pt-3">
            <Layout.Col xl={8}>
              <Text.H3 className="pt-2" style={{ fontWeight: 400 }}>
                {selected.length} rater{selected.length > 1 ? "s" : ""} Selected{" "}
                <span
                  style={{
                    color: "#518CFF",
                    textDecorationLine: "underline",
                    cursor: "pointer",
                  }}
                  className="px-2"
                  onClick={() => {
                    setSelected([]);
                    setSelectAll(false);
                  }}
                >
                  Clear Selection
                </span>
              </Text.H3>
            </Layout.Col>
            <Layout.Col xl={4} className="text-right">
              <Confirmation
                confirmText={`Are you sure you want to delete these users?`}
                show={confirmDeleteBulk}
                onOk={async () => {
                  onDeleteBulkUsers();
                }}
                setShow={setConfirmDeleteBulk}
              />
              <Button
                onClick={() => setConfirmDeleteBulk(true)}
                className="ml-2"
                variant="outline-danger"
              >
                Delete
              </Button>
            </Layout.Col>
          </Layout.Row>
        )}
        <Table
          columns={getCompColumns()}
          isLoading={isLoading}
          rows={users}
          customRows
          render={() => getRows()}
          hasPagination
          activePage={tableMeta.page}
          pages={tableMeta.totalPages}
          onPageSelect={(d: any) => {
            tableMeta.onPageSelect(d);
            onPageSelect(d);
          }}
          style={{ overflowX: "scroll" }}
          onSort={(item: any) => {
            const label = util.label.getSortingLabel(
              item.sortKey,
              item.sortValue
            );
            setColumnsData((_columns: any) => {
              return Object.values(tableMeta.resetColumns(_columns, item));
            });
            setOrdering(label);
            getEmployees({ ordering: label }, true);
            // dataCall({ empOrdering: label });
          }}
          totalItems={tableMeta.totalItems}
          {...(search && { notFoundMsg: util.noSearchRecordsFoundMsg })}
        />
      </>
      {getViewModifyTemplate()}
    </Div>
  );
};

export default TableCommon;
