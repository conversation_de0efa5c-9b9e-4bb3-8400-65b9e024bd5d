// import _ from "lodash";
import React from "react";
// import { Text, Div } from '../../../../components';
// Tooltip, Legend,
// import PropTypes from 'prop-types';
import {
  BarChart as <PERSON><PERSON>,
  Bar,
  // Cell,
  XAxis,
  YAxis,
  // CartesianGrid,
  // Tooltip,
  // Legend,
  ResponsiveContainer,
} from "recharts";

const BarChart = (props: any) => {
  const { data } = props;

  return (
    <ResponsiveContainer width={"100%"} height={300}>
      <BChart width={900} height={300} data={data}>
        <XAxis
          dataKey="name"
          tickLine={false}
          axisLine={{ stroke: "#DADADA" }}
          stroke="#DADADA"
          tick={{
            fontSize: "10px",
            dy: 0,
            stroke: "#838383",
            fontFamily: "Inter",
            fontWeight: "100",
          }}
          padding={{ left: 80, right: 80 }}
        />
        <YAxis dataKey="data" />
        {/* <Tooltip /> */}
        {/* <Legend /> */}
        {/* {getBarsTemplate()} */}
        <Bar
          dataKey="data"
          fill="#96B9FF"
          // shape={<RoundedBar />}
          // stroke={_.get(colors, 0)}
        />
      </BChart>
    </ResponsiveContainer>
  );
};

// LineChart;.propTypes = {

// }

export default BarChart;
