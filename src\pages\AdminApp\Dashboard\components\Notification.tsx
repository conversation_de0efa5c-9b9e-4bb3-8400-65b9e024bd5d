import React from "react";
import styled from "styled-components";
import { Div, Text } from "unmatched/components";

const Wrap = styled(Div)`
  max-width: 90%;
  width: 100%;
  height: 135px;
  background: #fbfbfb;
  border-radius: 8px;
  padding: 20px;
  margin-top: 25px;
`;

export default function Notification(props: any) {
  const { title, description, children } = props;

  return (
    <Div>
      <Wrap>
        <Text.H4>{title}</Text.H4>
        <Text.P1 className="mt-1">{description}</Text.P1>
        <Div className="mt-4">{children}</Div>
      </Wrap>
    </Div>
  );
}
