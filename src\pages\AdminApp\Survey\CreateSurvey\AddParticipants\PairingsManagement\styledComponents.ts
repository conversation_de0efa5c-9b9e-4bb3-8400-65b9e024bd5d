import { Button, Modal, Div } from "unmatched/components";
import styled from "styled-components";

export const RoundButton = styled(Button)`
  height: 40px;
  width: 40px;
  border-radius: 50%;
  font-size: 24px;
  padding: 8px;
`;
export const TranparentButton = styled.button`
  border: none;
  font-size: 12px;
  background: none;

  &:focus {
    background-color: none !important;
    outline: none;
  }
  &:active {
    border: none;
    outline: none;
  }
`;

export const ModalHeader = styled(Modal.Header)`
  background: #fbfbfb;
`;

export const ModalBody = styled(Modal.Body)`
  min-height: 50vh;
  max-height: 60vh;
  padding: 2rem;
  overflow-y: auto;
`;

export const ModalFooter = styled(Modal.Footer)`
  border: none;
  padding: 2rem;
`;

export const FileCard = styled.div`
  width: 360px;
  padding: 10px 15px;
  background: #f2f2f2;
  border-radius: 2px;
  font-size: 12px;
`;

export const CancelButton = styled.button`
  border: none;
  background: none;
  text-decoration-line: underline;
  color: #f34115;
`;
export const FormContainer = styled(Div)`
  background: #fbfbfb;
  padding: 16px 24px;
  border: 1px solid #f2f2f2;
  box-sizing: border-box;
  border-radius: 5px;
`;
