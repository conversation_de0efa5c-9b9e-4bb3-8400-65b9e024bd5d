import React from "react";
// But<PERSON>, Icon, Table, Text
import {
  Div,
  HeatMapTable,
  Layout,
  Button,
  Icon,
  Text,
  Table,
} from "unmatched/components";
// import DownloadExcel from "../../../DownloadExcel";
import {
  getCategoryGraphDataFact,
  getCategoryHeatMapFact,
} from "../aggregate-api";
import useFilter from "pages/CommonFilters/hook";
import { useEffect, useXHR } from "unmatched/hooks";
// import TimeSeriesGraph from "../../TimeSeriesGraph/TimeSeriesGraph";
import util from "unmatched/utils";
import useToastr from "unmatched/modules/toastr/hook";
import useAnalyticsFilters from "pages/AdminApp/Analytics/Shared/AnalyticsFilters/hook";
import AnalyticsFilters from "pages/AdminApp/Analytics/Shared/AnalyticsFilters/AnalyticsFilters";
import TimeSeriesGraphNew from "../../TimeSeriesGraph/TimeSeriesGraphNew";
import { cloneDeep } from "lodash";
// import { Table } from "react-bootstrap";
// import PropTypes from 'prop-types';

const TYPES = {
  "360": "surveyindex360",
  upward: "surveyindexupward",
  self: "surveyindexself",
  "360Group": "surveyindex360group",
};

const defaultSelected = { id: "", key: "", title: "", values: [] };

const getFilterOptions = (_filters: any) => {
  const filters = _filters || {};
  return util.lib.entries(filters).map((item: any) => {
    const [key, value] = item;
    return {
      id: key,
      key,
      title: value.label,
      values: value.values,
    };
  });
};

const FilterLayout = (props: any) => {
  const { selected, onSelect, filters } = props;

  return (
    <Div className="pt-3">
      <Layout.Row>
        <Layout.Col xl={3} lg={5} md={6} className="pt-3">
          <Text.H3>{props.title}</Text.H3>
        </Layout.Col>
        <Layout.Col xl={2} lg={4} md={6}>
          <Table.Filter
            title="Select"
            selected={selected.key}
            selectedLabel={selected.title}
            options={filters}
            onSelect={onSelect}
          />
        </Layout.Col>
      </Layout.Row>
      <Layout.Row className="pt-4 pb-3">
        <Layout.Col>{props.children}</Layout.Col>
        {/* {getFilterLayout()} */}
        <Layout.Col xl={2} lg={2} className="text-right pt-3">
          <Button
            onClcik={() => (props.onDownload ? props.onDownload() : "")}
            className="ml-auto"
            variant="outline-primary"
          >
            Download XLSX <Icon icon="fas fa-file-download" />
          </Button>
        </Layout.Col>
      </Layout.Row>
    </Div>
  );
};

const Category = () => {
  const filters = useFilter();

  let filterOptions = getFilterOptions(filters.filters);

  const [columns, setColumns]: any = React.useState({
    upward: [],
    self: [],
    ["360"]: [],
  });

  const [categories, setCategories]: any = React.useState({
    upward: [],
    self: [],
    // ["360"]: [],
  });

  const [selected, setSelected]: any = React.useState({
    upward: defaultSelected,
    self: defaultSelected,
    ["360"]: defaultSelected,
  });

  // const [resources, setResources] = React.useState({
  //   type: "360",
  //   options: ["Overall"],
  //   legends: [],
  //   selected: [],
  // });

  const graph = useXHR({
    defaultResponse: {
      data: [],
      labels: [],
    },
  });

  const filtersState = useFilter();

  const graphfilters = useAnalyticsFilters();

  const upwardfilters = useAnalyticsFilters();

  const selfFilters = useAnalyticsFilters();

  const selections: any = {
    upward: upwardfilters,
    self: selfFilters,
    graph: graphfilters,
  };

  const toastr = useToastr();

  // Graph Function calls

  const onGraphLoad = (year?: any) => {
    selections["graph"]
      .getFilters({
        year,
        // resource_types: TYPES["360"],
      })
      .then(
        (_filters: any) => {
          const filteredSurveys = _filters.surveys.filter(
            (item: any) => item.type !== TYPES["self"]
          );
          const [selectedSurvey] =
            filteredSurveys.filter((item: any) => item.categories.length) || [];
          if (!selectedSurvey) {
            toastr.warningToast("No Surveys Found");
            // selections["graph"].setFilters(_filters);
            return;
          }
          const [selectedCategory] = selectedSurvey.categories || [];
          if (!selectedCategory) {
            toastr.warningToast("No Categories Found");
            // selections["graph"].setFilters(_filters);
            return;
          }
          getGraphData(
            {},
            {
              ..._filters,
              surveys: filteredSurveys,
              // surveys: _filters.surveys.filter(
              //   (item: any) => selectedSurvey.type === item.type
              // ),
              categories: selectedSurvey.categories.map((item: any) => ({
                id: item.id,
                key: item.key,
                title: item.title || item.name,
              })),
              selected: {
                ..._filters.selected,
                surveys:
                  selectedSurvey && selectedSurvey.id
                    ? [selectedSurvey.id]
                    : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                groups: [],
                categories: selectedCategory ? [selectedCategory.id] : [],
              },
            }
          );
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: JSON.stringify(err.msg),
          });
        }
      );
  };

  const [selectedTimeGraphCompareList, setSelectedTimeGraphCompareList] =
    React.useState<any>({});

  useEffect(() => {
    getGraphData();
  }, [selectedTimeGraphCompareList]);

  const onFilterSelectNew = (data: any, groupID?: any) => {
    const computedData = Object.entries(data).reduce((acc, [k, v]) => {
      if ((v as any).length) {
        (acc as any)[k] = v;
      }
      return acc;
    }, {});

    setSelectedTimeGraphCompareList((s: any) => {
      let newList = { ...s, [groupID]: computedData };
      newList = Object.entries(newList).reduce((acc, [k, v]) => {
        if (Object.keys(v as any).length) {
          (acc as any)[k] = v;
        }
        return acc;
      }, {});
      return newList;
    });
  };

  const onFilterRemove = (id: string) => {
    const clone = cloneDeep(selectedTimeGraphCompareList);
    delete clone[id];
    setSelectedTimeGraphCompareList(clone);
  };

  const getGraphData = (filters?: any, _selections?: any) => {
    graph.setLoading(true);
    const raterGroups = _selections?.selected?.groups || [];
    const payload = {
      ...(filters || {}),
      compare_list: Object.values(selectedTimeGraphCompareList),
      // people: [...(filters?.people || [])],
      // rater_groups: ,
    };
    if (raterGroups.length) {
      payload["rater_groups"] = raterGroups;
    }
    return getCategoryGraphDataFact(
      {
        // userId: user.id,
        categoryId: _selections?.selected?.categories[0],
        type: _selections?.selected?.types[0],
      },
      payload
    ).then(
      (response: any) => {
        if (!util.lib.keys(payload).length) {
          filtersState.onSelect(response.defaultFilters);
        }
        selections["graph"].setFilters(_selections);
        graph.onSuccess(response);
        return true;
      },
      () => {
        graph.setLoading(false);
      }
    );
  };

  // const onFilterSelect = (data: any) => {
  //   getGraphDfdfdata(
  //     {
  //       ...data,
  //       people: data.people
  //         ? data.people.map((item: any) => util.getContentFromBrackets(item))
  //         : [],
  //     },
  //     selections["graph"]
  //   );
  // };

  // Heatmap Function calls

  const getDynamicColumns = (type: any, meta?: any) => {
    const options =
      filterOptions.find((item: any) => item.key === meta)?.values || [];
    return options.map((item: any) => {
      return {
        id: item,
        key: item,
        label: `${item} Average`,
      };
    });
  };

  const getColumnsData = (type: any) => {
    const output = [
      { id: 1, label: "Name" },
      // { id: 2, label: "Reviewee Average" },
      { id: 4, label: "Firm Category Average" },
      ...columns[type],
      // { id: 3, label: "Department Cateogry Average" },
      // { id: 5, label: "Practice group Category Average" },
      // { id: 6, label: "Title Category Average" },
      // { id: 7, label: "Self Category Average" },
    ];
    return output;
  };

  const getSelectedSurveyIndexs = (_selections?: any) => {
    if (_selections) {
      if (_selections.selected.groups && _selections.selected.groups.length) {
        return _selections.selected.groups;
      } else if (
        _selections?.selected.surveys &&
        _selections?.selected.surveys.length
      ) {
        return _selections?.selected.surveys;
      }
    }
    return [];
  };

  const getHeatMapData = (type: any, _selections?: any, meta?: any) => {
    getCategoryHeatMapFact({
      surveys: getSelectedSurveyIndexs(_selections),
      years: _selections?.selected.years || [],
      types: _selections?.selected.types || [],
      meta,
    }).then(
      (response: any) => {
        selections[type].setFilters(_selections);
        setCategories((_categories: any) => {
          return {
            ..._categories,
            [type]: response.data,
          };
        });
        setColumns((_columns: any) => {
          return {
            ..._columns,
            [type]: getDynamicColumns(type, meta),
          };
        });
      },
      (err: any) => {
        toastr.onError({
          ...err,
          msg: JSON.stringify(err.msg),
        });
      }
    );
  };

  const onTableLoad = (type: string, year?: any, meta?: any) => {
    selections[type]
      .getFilters({
        year,
        resource_types:
          type === "self"
            ? TYPES["self"]
            : `${TYPES["upward"]},${TYPES["360"]},${TYPES["360Group"]}`,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          if (!selectedSurvey) {
            // toastr.warningToast(`No ${type} Surveys Found`);
            return;
          }
          let groups = [];
          if (
            [TYPES["360"], TYPES["360Group"]].includes(selectedSurvey?.type)
          ) {
            groups =
              selectedSurvey &&
              selectedSurvey["rater_groups"].map((item: any) => {
                return {
                  id: item.id,
                  key: item.key,
                  title: item.title,
                  type: item.resource_type,
                  groupId: selectedSurvey?.id,
                };
              });
          }
          getHeatMapData(
            type,
            {
              ..._filters,
              surveys: _filters.surveys,
              groups,
              selected: {
                ..._filters.selected,
                surveys:
                  selectedSurvey && selectedSurvey.id
                    ? [selectedSurvey.id]
                    : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                groups: [],
              },
            },
            meta || selected[type].key
          );
        },
        (err: any) => {
          // console.log(err);
          toastr.onError({
            ...err,
            msg: JSON.stringify(err.msg),
          });
        }
      );
  };

  const getRows = (type: string) => {
    const list = categories[type] || [];
    return list;
  };

  const getAllValues = (type: string) => {
    const values: any = [];
    categories[type].forEach((item: any) => {
      const keys = [
        "firmAverage",
        ...(selected[type]?.values || []),
        // "revieweeAverage",
        // "departmentAverage",
        // "practiceGroupAverage",
        // "titleAverage",
        // "selfAverage",
      ];
      keys.forEach((key: string) => {
        if (item[key]) {
          values.push(item[key]);
        }
      });
      // console.log(item);
    });
    // console.log(values);
    return values;
  };

  const getHeatMapDataTemplate = (item: any, type: string) => {
    const { Data } = HeatMapTable;
    const values = getAllValues(type);
    return (
      <>
        <Data className="text-dark bg-light ">
          <Div className="text-left">{item.name}</Div>
        </Data>
        {/* <Data values={values} value={item.revieweeAverage}>
          {item.revieweeAverage}
        </Data> */}
        <Data values={values} value={item.firmAverage}>
          {item.firmAverage}
        </Data>
        {columns[type].map((col: any) => {
          return (
            <Data values={values} value={item[col.key]}>
              {item[col.key] || "0"}
            </Data>
          );
        })}
        {/* <Data values={values} value={item.departmentAverage}>
          {item.departmentAverage}
        </Data> */}
        {/* <Data values={values} value={item.practiceGroupAverage}>
          {item.practiceGroupAverage}
        </Data> */}
        {/* <Data values={values} value={item.titleAverage}>
          {item.titleAverage}
        </Data> */}
        {/* <Data values={values} value={item.selfAverage}>
          {item.selfAverage}
        </Data> */}
      </>
    );
  };

  React.useEffect(() => {
    filters.getFilters((_filters: any) => {
      onGraphLoad();
      filterOptions = getFilterOptions(_filters);
      const [item] = filterOptions;
      if (item) {
        setSelected({
          ...selected,
          upward: item,
          self: item,
        });
        onTableLoad("upward", "", item.key);
        onTableLoad("self", "", item.key);
      }
    });
  }, []);

  // Reacrt

  return (
    <Layout.Container fluid className="pt-3">
      <Div className="py-3">
        <AnalyticsFilters
          filters={selections["graph"]}
          hideTypes
          config={{
            surveys: {
              multiple: false,
            },
            groups: {
              multiple: false,
              // identifier: "groupKey",
            },
            layout: {
              year: {
                xl: 2,
                md: 4,
                sm: 6,
              },
              survey: {
                xl: 2,
                md: 4,
                sm: 6,
              },
              group: {
                xl: 3,
                md: 4,
                sm: 6,
              },
              category: {
                xl: 3,
                md: 4,
                sm: 6,
              },
            },
            // categories: {
            //   multiple: false
            // }
          }}
          onYearChange={(item: any) => {
            onGraphLoad(item.key);
            // onTableLoad(item.key, "graph");
          }}
          // onTypeChange={(item: any) => {
          //   // onFiltersChange({
          //   //   ...selections["graph"].selected,
          //   //   types: [item.key],
          //   //   surveys: [],
          //   // });
          // }}
          onSurveyChange={(items: any) => {
            const item = selections["graph"];
            const [surveyId] = items;
            const selected = item.surveys.find((s: any) => {
              return items.includes(s.id);
            });
            const groups = selected["rater_groups"]?.map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                groupKey: item["rater_group"],
                title: item.title,
                type: item.resource_type,
                groupId: surveyId,
                categories: item.categories || [],
              };
            });
            const categories = selected?.categories.map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                title: item.title || item.name,
                groupId: surveyId,
              };
            });
            getGraphData(filtersState.selected, {
              ...item,
              groups,
              categories,
              selected: {
                ...item.selected,
                surveys: items,
                groups: [],
                types: [selected.type],
                categories: categories.length ? [categories[0].id] : [],
              },
            });
          }}
          onGroupChange={(items: any) => {
            const item = selections["graph"];
            // const filteredGroups =
            //   item.groups.filter((item: any) => items.includes(item.id)) || [];
            const categories = item.categories.map((item: any) => {
              return {
                id: item.id,
                key: item.key,
                title: item.title || item.name,
                // groupId: selected.id,
              };
            });
            getGraphData(filtersState.selected, {
              ...item,
              // groups,
              // categories,
              selected: {
                ...item.selected,
                groups: items,
                // surveys: items,
                // groups: filteredGroups.map((item: any) => item.groupKey),
                types: [TYPES["360Group"]],
                categories: categories.length ? [categories[0].id] : [],
              },
            });
          }}
          onCategoryChange={(items: any) => {
            const item = selections["graph"];
            let [selected] =
              item.categories.filter((item: any) => items.includes(item.id)) ||
              {};
            if (!selected) {
              selected = item.categories.length ? item.categories[0] : {};
            }
            getGraphData(
              {},
              {
                ...item,
                // groups,
                // categories,
                selected: {
                  ...item.selected,
                  categories: selected.id ? [selected.id] : [],
                },
              }
            );
          }}
        />
      </Div>
      <Div>
        <TimeSeriesGraphNew
          // filtersState={filtersState}
          onFilterSelect={onFilterSelectNew}
          onFilterRemove={onFilterRemove}
          userName={""}
          data={graph.data.data}
          labels={graph.data.labels}
          isLoading={graph.isLoading}
          disallowedFilters={['people']}
          // resourceTypes={{
          //   ...resources,
          //   onChange: onResourceChange,
          //   onGroupChange,
          // }}
        />
      </Div>
      {getRows("upward").length > 0 && <Div>
        <Div>
          {/* {JSON.stringify(selections)} */}
          <FilterLayout
            title="Time Series Table Heat Map - Upward/360"
            selected={selected["upward"]}
            filters={filterOptions}
            onSelect={(item: any) => {
              setSelected({
                ...selected,
                upward: item,
              });
              getHeatMapData("upward", selections["upward"], item.key);
            }}
          >
            <AnalyticsFilters
              filters={selections["upward"]}
              hideTypes
              config={{
                surveys: {
                  multiple: false,
                },
                groups: {
                  multiple: false,
                },
                layout: {
                  year: {
                    xl: 2,
                    md: 4,
                    sm: 6,
                  },
                  survey: {
                    xl: 2,
                    md: 4,
                    sm: 6,
                  },
                  group: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad("upward", item.key);
              }}
              onSurveyChange={(items: any) => {
                const item = selections["upward"];
                const [surveyId] = items;
                const selectedSurvey = item.surveys.find((s: any) => {
                  return items.includes(s.id);
                });
                const groups = selectedSurvey["rater_groups"]
                  ? selectedSurvey["rater_groups"].map((item: any) => {
                      return {
                        id: item.id,
                        key: item.key,
                        title: item.title,
                        type: item.resource_type,
                        groupId: surveyId,
                      };
                    })
                  : [];
                getHeatMapData(
                  "upward",
                  {
                    ...item,
                    groups,
                    selected: {
                      ...item.selected,
                      surveys: items,
                      groups: [],
                      types: [selectedSurvey.type],
                    },
                  },
                  selected["upward"].key
                );
              }}
              onGroupChange={(items: any) => {
                const item = selections["upward"];

                getHeatMapData(
                  "upward",
                  {
                    ...item,
                    // groups,
                    selected: {
                      ...item.selected,
                      // surveys: items,
                      groups: items,
                      types: [TYPES["360"]],
                    },
                  },
                  selected["upward"].key
                );
              }}
            />
          </FilterLayout>
        </Div>
        <Div>
          <HeatMapTable
            columns={getColumnsData("upward")}
            values={getAllValues("upward")}
            rows={getRows("upward")}
            rowItem={(item: any) => getHeatMapDataTemplate(item, "upward")}
          />
        </Div>
      </Div>}
      {getRows("self").length > 0 && <Div>
        <Div>
          <FilterLayout
            title="Time Series Table Heat Map - Self"
            selected={selected["self"]}
            filters={filterOptions}
            onSelect={(item: any) => {
              setSelected({
                ...selected,
                self: item,
              });
              getHeatMapData("self", selections["self"], item.key);
            }}
          >
            <AnalyticsFilters
              filters={selections["self"]}
              hideTypes
              config={{
                surveys: {
                  multiple: false,
                },
                layout: {
                  year: {
                    xl: 2,
                    md: 4,
                    sm: 6,
                  },
                  survey: {
                    xl: 2,
                    md: 4,
                    sm: 6,
                  },
                  group: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad("self", item.key);
              }}
              onSurveyChange={(items: any) => {
                const item = selections["self"];
                getHeatMapData(
                  "self",
                  {
                    ...item,
                    selected: {
                      ...item.selected,
                      surveys: items,
                    },
                  },
                  selected["self"].key
                );
              }}
            />
          </FilterLayout>
        </Div>
        <Div>
          <HeatMapTable
            columns={getColumnsData("self")}
            values={getAllValues("self")}
            rows={getRows("self")}
            rowItem={(item: any) => getHeatMapDataTemplate(item, "self")}
          />
        </Div>
      </Div>}
    </Layout.Container>
  );
};

Category.propTypes = {};

export default Category;
