import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

// export const pairingFileUploadFact = (data: any, params?: any, meta?: any) => {
//   const config = util.api.getConfigurations(params, {
//     ...meta,
//     headers: {
//       "Content-Type": "multipart/form-data",
//     },
//   });
//   const formData = new FormData();
//   formData.append("data", data.File);
//   formData.append("format", data.Format);
//   return axios.post(`${util.apiUrls.PAIRING_UPLOAD}`, formData, config);
// };

export const surveyParticipantFileUploadFact = (
  data: any,
  type: "engagement" | "self",
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  const formData = new FormData();
  formData.append("data", data.File);
  formData.append("format", data.Format);
  formData.append("index_id", data.indexID);
  formData.append("tags", data.tags);
  formData.append("title", data.title);
  return axios.post(
    `${util.apiUrls.PARTICIPANT_UPLOAD(type)}`,
    formData,
    config
  );
};

export const pairingPatchFileFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PAIRING_UPLOAD}`, config);
};

export const getAllParticipantFileFact = async (
  type: "engagement" | "self",
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  const dataCall: AxiosResponse = await axios.get(
    `${util.apiUrls.GET_ALL_PARTICIPANTS_FILES(type)}`,
    config
  );

  return {
    results: dataCall.data.results,
    totalItems: dataCall.data.count_items,
    totalPages: dataCall.data.count_pages,
  };
};

// export const fetchAdminAbstractPairingsFact = (
//   id?: string,
//   params?: any,
//   meta?: any
// ) => {
//   const config = util.api.getConfigurations(params, {
//     ...meta,
//   });
//   return axios.get(`${util.apiUrls.GET_ALL_PAIRINGS_FROM_FILE}`, config);
// };

// export const fetchSurveyAbstractPairingsFact = (params?: any, meta?: any) => {
//   const config = util.api.getConfigurations(params, {
//     ...meta,
//   });
//   return axios.get(`${util.apiUrls.GET_ALL_PAIRINGS_FROM_FILE}`, config);
// };

export const fetchParticipantFact = (
  surveyId: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_ADMIN_RATERS(surveyId)}`, config);
};

// export const fetchPairFileInfoFact = (id: string, params?: any, meta?: any) => {
//   const config = util.api.getConfigurations(params, {
//     ...meta,
//   });
//   return axios.get(`${util.apiUrls.FETCH_PAIRING_FILE_INFO(id)}`, config);
// };

export const pairingsFileDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.PAIRINGS_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `pairings_${params.index_id}.${params.f}`);
      document.body.appendChild(link);
      link.click();
    })
    .catch((err: any) => console.log(err));
};

export const getRequiredParticipantFieldFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_REQUIRED_PARTICIPANT_FIELDS}`, config);
};

export const getSampleParticipantDownload = (params: any) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${util.apiUrls.SAMPLE_PARTICIPANT_DOWNLOAD}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `example.${params.f}`);
      document.body.appendChild(link);
      link.click();
    });
};
export const patchPairUploadFact = (
  id: string,
  data: any,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.PATCH_PAIRING_UPLOAD(id)}`, data, config);
};

export const downloadParticipantFileInfoFact = async (
  file: string,
  params?: any
) => {
  const config = util.api.getConfigurations(params, {
    headers: {
      // 'Content-Type': 'blob',
    },
    responseType: "blob",
  });
  return axios
    .get(`${file}`, config)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `pairings_${params.index_id}.${params.f}`);
      document.body.appendChild(link);
      link.click();
    })
    .catch((err: any) => console.log(err));
};

export const deleteParticipantFileFact = (
  id: string,
  params?: any,
  meta?: any
) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.PATCH_PARTICIPANT_UPLOAD(id)}`, config);
};

export const addParticipantFact = (data: any, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(
    {},
    {
      ...meta,
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  return axios.post(
    `${util.apiUrls.GET_ADMIN_RATERS(params.id)}`,
    data,
    config
  );
};

export const searchUsersFact = (id?: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.GET_USERS}`, config);
};
