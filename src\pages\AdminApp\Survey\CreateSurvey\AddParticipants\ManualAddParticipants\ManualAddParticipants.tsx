import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Div,
  FormControl,
  Text,
  Button,
  Dropdown,
} from "unmatched/components";
import Header from "../../Header";
import {
  addUserToExitSurvey,
  deleteUserToExitSurvey,
  getUserExitSurvey,
  searchUsersFact,
} from "../PairingsManagement/pairings-api";
import { useParams } from "react-router-dom";
import { useDebounce } from "react-use";
import { Icon, util } from "@unmatchedoffl/ui-core";
// import PropTypes from "prop-types";

// props
const ManualAddParticipants = () => {
  const { id } = useParams<any>();

  const [surveyDate, setSurveyDate] = React.useState(null);

  const [users, setUsers] = React.useState([]);
  const [selectedUser, setSeletedUser] = React.useState<any>(null);

  const [search, setSearch] = React.useState<string | null>(null);

  const [addedUsers, setAddedUsers] = React.useState<any[]>([]);

  const apiCall = async () => {
    const users = await getUserExitSurvey({ index: id });
    setAddedUsers(users.data);
  };

  React.useEffect(() => {
    apiCall();
  }, []);

  useDebounce(
    () => {
      if (search !== null) {
        findUser(search);
      } else {
        return;
      }
    },
    200,
    [search]
  );
  const findUser = async (search: string | null) => {
    const _d = await searchUsersFact("", { search });

    setUsers(_d.data.results);
    console.log(_d.data);
  };

  const userAdd = async () => {
    const deadline =
      util.date.getFormatedTime(surveyDate, "yyyy-MM-dd") + "T00:00:00Z";
    const user = await addUserToExitSurvey({
      index: id,
      rater: selectedUser.id,
      deadline,
    });

    setAddedUsers([...addedUsers, user.data]);
    setSeletedUser({});
    setSurveyDate(null);
    setSearch("");
  };

  const getHeaderTitleTemplate = () => {
    const _title = "Participants";

    return (
      <>
        <Text.H1 className="pb-2">{_title}</Text.H1>
        <Text.P1 className="pb-2">
          Add the email address of the participants whom you want to take up the
          survey.
        </Text.P1>
      </>
    );
  };

  const removeUser = async (userID: number) => {
    await deleteUserToExitSurvey(userID, { index: id });
    let users = [...addedUsers];
    users = users.filter((_item) => {
      return _item.id !== userID;
    });
    setAddedUsers(users);
  };

  return (
    <Div className="pt-5">
      <Header
        title={getHeaderTitleTemplate()}
        // metaItem={getNavMetaTemplate()}
        // breadcrumbs={props.breadcrumbs}
      />
      <Div className="row pt-5" style={{ maxWidth: 750 }}>
        <Div className="col-5 position-relative">
          <Text.P1 className="pb-2 text-muted">
            Email address of participant
          </Text.P1>
        </Div>
        <Div className="col-4">
          <Text.P1 className="pb-2 text-muted">Keep open till</Text.P1>
        </Div>
        <Div className="col-3 pt-4"></Div>
      </Div>
      {addedUsers.map((_sU: any) => (
        <Div
          className="row py-2"
          style={{ maxWidth: 750 }}
          key={_sU.rater.emp_id}
        >
          <Div className="col-5 position-relative">
            <Text.P1 className="py-2">{_sU.rater_details.email}</Text.P1>
          </Div>
          <Div className="col-2">
            {/* <DatePicker
              minDate={new Date()}
              // selected={_sU.deadline}
              onChange={(date: any) => {
                setSurveyDate(date);
              }}
              className="w-100"
              placeholderText={"Choose Date"}
              customInput={<FormControl.Date placeholder={"Choose Date"} />}
            /> */}
            <Text.P1 className="py-2">
              {util.date.getBrowserTime(_sU.deadline, "dd MMM, yyyy")}
            </Text.P1>
          </Div>
          <Div className="col-4 pt-1">
            <Button
              variant="link"
              className="p-0 fs-16 text-danger"
              onClick={() => removeUser(_sU.id)}
            >
              <Icon icon="far fa-times-circle" />
            </Button>
          </Div>
        </Div>
      ))}
      <Div className="row pt-5" style={{ maxWidth: 750 }}>
        <Div className="col-5 position-relative">
          <Dropdown style={{ position: "static" }}>
            <Dropdown.Toggle
              variant="link"
              className="px-0 py-0 mb-3 w-100"
              id="dropdown-basic"
              disabled={false}
            >
              <FormControl.Text
                type="text"
                onChange={(e: any) => setSearch(e.target.value)}
                placeholder="Enter name or email id"
                value={search}
              />
            </Dropdown.Toggle>

            {users.length > 0 && (
              <Dropdown.Menu
                className="shadow w-100 mt-2 py-0"
                style={{ zIndex: 999 }}
                align="right"
              >
                {users.map((item: any, i: number) => (
                  <Dropdown.Item
                    key={i}
                    className={`py-2 fs-12 fw-300 d-block`}
                    as={Button}
                    onClick={() => {
                      setSeletedUser(item);
                      setSearch(item.email);
                    }}
                  >
                    {item.email}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            )}
          </Dropdown>
        </Div>
        <Div className="col-4">
          <DatePicker
            minDate={new Date()}
            selected={surveyDate}
            onChange={(date: any) => {
              setSurveyDate(date);
            }}
            className="w-100"
            placeholderText={"Choose Date"}
            customInput={<FormControl.Date placeholder={"Choose Date"} />}
          />
        </Div>
        <Div className="col-3">
          <Button onClick={userAdd}>Add</Button>
        </Div>
      </Div>
    </Div>
  );
};

ManualAddParticipants.propTypes = {};

export default ManualAddParticipants;
