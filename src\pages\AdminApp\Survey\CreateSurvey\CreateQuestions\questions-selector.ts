export const createQuestionsSelector = (state: any) =>
  state.admin.survey.create.questions;

export const loadingSelector = (state: any) =>
  state.admin.survey.create.questions.isLoading;

export const questionsSelector = (state: any) =>
  createQuestionsSelector(state).questions;

// export const questionSelector = (state: any, id: number) => questionsSelector(state).content[`question${id}`];

export const sectionsSelector = (state: any) =>
  createQuestionsSelector(state).sections;

export const optionsSelector = (state: any) =>
  createQuestionsSelector(state).options;

export const dropdownSelector = (state: any) =>
  createQuestionsSelector(state).dropdowns;

export const rankingSelector = (state: any) =>
  createQuestionsSelector(state).rankings;

export const checkboxSelector = (state: any) =>
  createQuestionsSelector(state).checkboxes;

export const radioSelector = (state: any) =>
  createQuestionsSelector(state).radios;

export const inputSelector = (state: any) =>
  createQuestionsSelector(state).inputs;
