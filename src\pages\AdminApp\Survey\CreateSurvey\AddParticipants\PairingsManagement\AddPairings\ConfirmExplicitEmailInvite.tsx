import React from "react";
import { CustomModal as <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "unmatched/components";

export default function ConfirmExplicitEmailInvite(props: any) {
  return (
    <>
      <Modal.Body>
        <Text.H2>Confirm Rules</Text.H2>
        <Text.P1 className="mt-2">
          Please confirm if you want to send emails to the newly added pairings.
        </Text.P1>
      </Modal.Body>
      <Modal.Footer>
        <Button
          onClick={() => props.setScreen(2)}
          variant="primary"
          className="float-right"
        >
          Confirm
        </Button>
      </Modal.Footer>
    </>
  );
}
