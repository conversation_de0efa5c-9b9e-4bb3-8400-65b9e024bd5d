import axios, { AxiosResponse } from "axios";
import util from "unmatched/utils";

export const getMetaLabelsFact = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios
    .get(`${util.apiUrls.META_LABELS}`, config)
    .then(({ data }: AxiosResponse) => {
      let response = {};
      data.labels.forEach(({ field, display_name }: any) => {
        response = {
          ...response,
          [field]: {
            label: display_name,
            values: util.lib.get(data.values, field),
          },
        };
      });
      return response;
    });
};
