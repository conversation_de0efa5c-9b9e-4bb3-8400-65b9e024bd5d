import React from "react";
// import _ from "lodash";
import { useHistory } from "react-router";
import ICONS from "assets/icons/icons";
import styled from "styled-components";
import {
  Card,
  Div,
  Icon,
  Layout,
  Placeholder,
  Progress,
  Text,
} from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import { DateTime } from "luxon";
import { getAllSurveysV2, getStats } from "./surveys-api";
import useUtil from "../Shared/hooks/util-hook";
import useSession from "unmatched/modules/session/hook";
import anchorme from "anchorme";
import utilModule from "unmatched/utils";

interface SurveyCardProps {
  title: string;
  dueDate: any;
  type: string;
  totalQuestions: number;
  questionsLeft: number;
  progress: number;
  checked?: boolean;
  color: string;
  surveyType: string;
  todo: string;
  inProg: string;
  completed: string;
  surveyId: string;
  open: boolean;
}

const CheckIcon = styled(Div)`
  position: absolute;
  right: -10px;
  top: -10px;
  color: white;
`;

const CardHeader = styled(Card.Header)`
  position: relative;
  background: ${(props: { color: string }) => props.color} !important;
`;

const TabHeader = (props: any) => {
  return (
    <Div className="border-bottom mb-3 d-flex">
      <Div className={"border-bottom border-primary pr-5 text-truncate"}>
        {props.title}
      </Div>
    </Div>
  );
};

// const getSurveyColor = () => _.sample(["#fcdce6", "#ebd6ff", "#dce8ff"]);

const SurveyCard = (props: SurveyCardProps) => {
  const history = useHistory();

  const dashboardUtil = useUtil();

  const getDataLine = (_label: string, _value: string | number) => {
    return (
      <Div className="pb-3">
        <Text.P2 className="text-muted">{_label}</Text.P2>
        <Text.H4>{_value}</Text.H4>
      </Div>
    );
  };
  return (
    <Card
      className={`${props.open ? "cursor-pointer" : ""} mb-4 border-none`}
      noShadow
      onClick={() =>
        // history.push(appUrls.user.dashboard.upwardReview.getUrl(1))
        props.open
          ? history.push(
              appUrls.user.dashboard.getSurveyTermsUrl(props.surveyId)
            )
          : ""
      }
    >
      <CardHeader
        className={"p-3"}
        color={dashboardUtil.getSurveyColor(props.surveyType)}
      >
        <Text.H3 className="text-dark text-truncate">{props.title}</Text.H3>
        {props.progress === 100 && (
          <CheckIcon>
            <Icon
              icon="fas fa-check-circle bg-white rounded-circle shadow"
              className="fs-24"
              variant="success"
            />
          </CheckIcon>
        )}
      </CardHeader>
      <Div className="p-3 border-left border-right border-bottom">
        {getDataLine(
          "Due Date",
          DateTime.fromISO(props.dueDate).toFormat("LLL dd, yyyy")
        )}
        {/* {DateTime.toLocaleString(DateTime.DATE_SHORT)} */}
        {props.surveyType !== "SurveyIndexUpward" &&
        props.surveyType !== "SurveyIndex360" ? (
          <Layout.Row className="mt-2">
            <Layout.Col>
              {getDataLine("Total Questions", props.totalQuestions)}
            </Layout.Col>
            <Layout.Col>
              {getDataLine("Questions Left", props.questionsLeft)}
            </Layout.Col>
          </Layout.Row>
        ) : (
          <Layout.Row className="mt-2">
            <Layout.Col>{getDataLine("To-Do", props.todo)}</Layout.Col>
            <Layout.Col className="px-0">
              {getDataLine("In-progress", props.inProg)}
            </Layout.Col>
            <Layout.Col className="px-0">
              {getDataLine("Completed", props?.completed)}
            </Layout.Col>
          </Layout.Row>
        )}
        <Div className="my-4" />

        <Div style={{ minHeight: 24 }}>
          <Text.P2 className="text-success">
            {props.progress > 0
              ? `${props.progress}% Completed`
              : "Yet to Start"}
          </Text.P2>
          <Progress variant="success" now={props.progress} />
        </Div>
      </Div>
    </Card>
  );
};

const IsLoadingCard = () => {
  const dashboardUtil = useUtil();
  return (
    <Card className="cursor-pointer mb-4" noShadow>
      <CardHeader className={"p-3"} color={dashboardUtil.getSurveyColor("")}>
        <Placeholder width="col-12" />
      </CardHeader>
      <Div className="p-3">
        <>
          <Placeholder size="0.6" width="col-4" />
          <Placeholder width="col-2" size="0.8" className="" />{" "}
        </>
        <>
          <Placeholder size="0.8" width="col-12" />
          <Placeholder width="col-10" size="0.8" className="" />{" "}
        </>
        <Placeholder size="0.8" width="col-4" />
        {/* <Placeholder width="col-2" size="0.8" className="" />{" "} */}
        <Div style={{ minHeight: 24 }}>
          <Placeholder width="col-12" />
        </Div>
      </Div>
    </Card>
  );
};



const SurveyList = () => {
  const [surveyList, setSurveyList] = React.useState<any[]>([{}, {}, {}, {}]);
  const [statMap, setStatMap] = React.useState<any>({});
  const [isLoading, setIsLoading] = React.useState(true);

  const { client } = useSession();

  const getGeneralStats = async (surveys: any) => {
    const statObj = await Promise.all(surveys.map(async (s: any) => {
      const stat = await getStats('index', s.id, 'general')
      return new Promise((resolve) => resolve({ [s.id]: stat?.data }));
    }));
    setStatMap(statObj.reduce((acc: any, el: any) => {
      const [k, v] = Object.entries(el)[0];
      acc[k] = v;
      return acc;
    }, {}));
  }

  const getSurveys = async () => {
    try {
      const response = await getAllSurveysV2({});
      response.data?.sort(
        (d1: any, d2: any) =>
          new Date(d1.deadline).valueOf() - new Date(d2.deadline).valueOf()
      );
      setSurveyList(response.data);
      setIsLoading(false);
      getGeneralStats(response.data);
    } catch (err) {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    getSurveys();
  }, []);

  const _cl: any = client;
  const message = _cl?.introMessage?.split(/[\r\n]+/g).map((_i: string) => {
    return anchorme(_i);
  });

  return (
    <Layout.Container fluid className="px-5 py-4">
      <Div className="pb-3">
        <Text.H1>Home</Text.H1>
      </Div>
      <Div className="my-3">
        {_cl?.introMessage?.length !== 0 && (
          <ContactInfo className="p-3">
            {message &&
              message?.map((_i: string) => (
                <Text.P1 dangerouslySetInnerHTML={{ __html: _i }} />
              ))}
          </ContactInfo>
        )}
      </Div>
      <Div className="mt-4">
        <TabHeader
          title={
            <Text.H3 className="pb-2">
              <ICONS.SurveyOngoing /> On-Going
            </Text.H3>
          }
        />
        <Layout.Row>
          {isLoading
            ? surveyList.map((item: any, index: number) => (
                <Layout.Col key={item.id} xl={3} lg={4} md={6} sm={12}>
                  <IsLoadingCard key={index} />
                </Layout.Col>
              ))
            : surveyList.map((item: any) => {
                let deadline = item.deadline;
                
                if (item.resourcetype === "SurveyIndexExit") {
                  deadline = item.participant_deadline;
                }
                if (
                  Math.floor(DateTime.now().toSeconds()) <
                    DateTime.fromISO(deadline).toSeconds() &&
                  Math.floor(DateTime.now().toSeconds()) >
                    DateTime.fromISO(item.start).toSeconds()
                ) {
                  const daysLeft = utilModule.daysBetween(new Date(deadline), new Date());

                  return (
                    <Layout.Col key={item.id} xl={3} lg={4} md={6} sm={12}>
                      { daysLeft < 8 && <div className="badge"> <ICONS.SurveyOngoingb className="clock__svg" />&nbsp;ENDING SOON</div>}
                      <SurveyCard
                        title={item.title}
                        dueDate={deadline}
                        type={item.is_anonymous ? "Anonymous" : "Non Anonymous"}
                        totalQuestions={statMap?.[item.id]?.total_questions}
                        questionsLeft={statMap?.[item.id]?.questions_left}
                        progress={statMap?.[item.id]?.percentage_completed}
                        color="#518CFF"
                        surveyType={item.resourcetype}
                        todo={statMap?.[item.id]?.TODO}
                        inProg={statMap?.[item.id]?.PROG}
                        completed={statMap?.[item.id]?.SUBM}
                        surveyId={item.id}
                        open={true}
                        checked={false}
                      />
                    </Layout.Col>
                  );
                }
                  
                else return null;
              })}
        </Layout.Row>
      </Div>
    </Layout.Container>
  );
};
const ContactInfo = styled(Div)`
  background: #f2f2f2;
`;
export default SurveyList;
