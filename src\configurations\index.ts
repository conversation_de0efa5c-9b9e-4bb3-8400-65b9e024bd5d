import reportWebVitals from "./reportWebVitals";
import "react-app-polyfill/ie11";
import "react-app-polyfill/stable";
// import userAgent from "unmatched/utils/browser";
import * as serviceWorker from "./serviceWorker";
import util from "../unmatched/utils";

const { userAgent } = util;

export default function initEssentials() {
  window.document.body.classList.add(userAgent.osClass);
  window.document.body.classList.add(userAgent.browserClass);
  if (process.env.NODE_ENV !== "production") {
    // Measure Performance
    // eslint-disable-next-line no-console
    reportWebVitals(console.log);
  } else {
    // Service worker for better cashing
    serviceWorker.register({});
  }
}
