import React from "react";
import { <PERSON> } from "react-router-dom";
import { Dropdown, Div, Text, Portal } from "unmatched/components";
import useSession from "unmatched/modules/session/hook";
import util from "unmatched/utils";

const Profile = (props: {
  onLogout: Function;
  name: string;
  togglePasswordModal: Function;
}) => {
  const { onLogout, name, togglePasswordModal } = props;
  const { user } = useSession();
  const settingRoutes = [
    {
      id: 3,
      title: "Switch to user",
      route: util.appUrls.user.default,
    },
    {
      id: 3,
      title: "Go to Launchpad",
      route: "/",
    },

    {
      id: 2,
      title: "Change password",
      onClick: () => togglePasswordModal(true),
    },
    {
      id: 1,
      title: "Logout",
      route: "",
      onClick: () => onLogout && onLogout(),
    },
  ];

  const getOptionsTemplate = () => {
    return (
      <Dropdown>
        <Dropdown.Toggle
          variant="primary"
          size="sm"
          style={{ width: 30, height: 30, borderRadius: "50%" }}
        >
          {name}
        </Dropdown.Toggle>
        <Portal root="profile-root">
          <Dropdown.Menu
            align="right"
            style={{ zIndex: 1090, width: 200 }}
            className="mt-2"
          >
            <Div className="font-weight-bold pl-md-3 my-1">
              <Text.H3>{`${user?.firstName} ${user?.lastName}`}</Text.H3>
            </Div>
            <Div className="fs-12 pl-md-3 pr-3 text-break text-muted text-truncate">
              {user?.email}
            </Div>
            <hr className="my-2" />
            {settingRoutes.map((item: any) => {
              const dropdownItem = () => {
                return (
                  <Dropdown.Item
                    as={Div}
                    className="fs-14 cursor-pointer px-3"
                    onSelect={() => item.onClick && item.onClick()}
                  >
                    {item.title}
                  </Dropdown.Item>
                );
              };
              if (item.route === "/") {
                return <a href="/">{dropdownItem()}</a>;
              }
              return (
                <Link key={item.id} to={item.route || "#"}>
                  {dropdownItem()}
                </Link>
              );
            })}
          </Dropdown.Menu>
        </Portal>
      </Dropdown>
    );
  };

  return getOptionsTemplate();
};

export default Profile;
