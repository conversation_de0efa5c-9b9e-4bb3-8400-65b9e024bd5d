import axios from "axios";
import util from "unmatched/utils";

export const createAFAQ = (data: any, params?: any, meta?: any) => {
  const reqData = {
    label: data.question,
    answer: data.answer,
    survey_index: data.id,
  };
  const config = util.api.getConfigurations(params, {
    ...meta,
  });

  return axios.post(`${util.apiUrls.FAQ}`, { ...reqData }, config);
};

export const getAllFAQs = (params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.get(`${util.apiUrls.FAQ}`, config).then((response) => {
    const { data } = response;
    return data.map((faq: any) => {
      return {
        id: faq.id,
        question: faq.label,
        answer: faq.answer,
      };
    });
  });
};

export const patchAFAQ = (id: string, data: any, params?: any, meta?: any) => {
  const reqData = {
    label: data.question,
    answer: data.answer,
  };
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.patch(`${util.apiUrls.FAQ_OPERATION(id)}/`, reqData, config);
};

export const deleteAFAQ = (id: string, params?: any, meta?: any) => {
  const config = util.api.getConfigurations(params, {
    ...meta,
  });
  return axios.delete(`${util.apiUrls.FAQ_OPERATION(id)}`, config);
};
